import {onDocumentCreated, onDocumentDeleted, onDocumentUpdated} from 'firebase-functions/v2/firestore';
import * as admin from 'firebase-admin';
import {FieldValue} from 'firebase-admin/firestore';
import {Project, StartupShowcaseProject, University, UniversityDbModelSafe} from '@creator-campus/common';
import {onCall} from 'firebase-functions/https';

export const onUniversityCreated = onDocumentCreated(
  {
    document: 'universities/{universityId}',
  },
  async (event) => {
    const universityId = event.params.universityId;
    console.log(`Detected university ${universityId} created.`);

    await admin
      .firestore()
      .collection('global')
      .doc('platform')
      .update({
        universityList: FieldValue.arrayUnion(universityId),
      });
  },
);

export const onUniversityUpdated = onDocumentUpdated(
  {
    document: 'universities/{universityId}',
  },
  async (event) => {
    if (!event.data) {
      console.error('Event data is undefined!');
      return;
    }

    const universityId = event.params.universityId;
    console.log(`Detected university ${universityId} created.`);

    const beforeData = event.data.before.data() as UniversityDbModelSafe;
    const afterData = event.data.after.data() as UniversityDbModelSafe;

    if (beforeData.partner !== afterData.partner) {
      console.log('Detected partner status changed.');
      await admin
        .firestore()
        .collection(University.collectionName)
        .doc(universityId)
        .update({
          partnerSince: afterData.partner ? FieldValue.serverTimestamp() : null,
        });
    }

    const justReachedUserLimit = !beforeData.reachedUserLimit && afterData.reachedUserLimit;
    if (justReachedUserLimit && afterData.paymentState !== 'paying') {
      console.log('Detected user limit reached and not paying. Disabling partnership.');
      await admin.firestore().collection(University.collectionName).doc(universityId).update({
        partner: false,
      });
    }
  },
);

export const onUniversityDeleted = onDocumentDeleted(
  {
    document: 'universities/{universityId}',
  },
  async (event) => {
    const universityId = event.params.universityId;
    console.log(`Detected university ${universityId} deleted.`);

    await admin
      .firestore()
      .collection('global')
      .doc('platform')
      .update({
        universityList: FieldValue.arrayRemove(universityId),
      });
  },
);

export const validateStartupShowcaseToken = onCall(async (request) => {
  const {universityId, token} = request.data;
  console.log(`Validating startup showcase token for university ${universityId}...`);

  if (!universityId || !token) {
    console.error(`At least one required argument is undefined. universityId=${universityId}, token=${token}`);
    return {valid: false};
  }

  const uniSnapshot = await admin.firestore().collection(University.collectionName).doc(universityId).get();
  if (!uniSnapshot.exists) {
    console.error(`University ${universityId} does not exist.`);
    return {valid: false};
  }

  const university = uniSnapshot.data() as UniversityDbModelSafe;
  if (!university.showcaseEnabled) {
    console.error(`University ${universityId} does not have showcase enabled.`);
    return {valid: false};
  }

  const privateUniSnapshot = await admin.firestore().collection(University.collectionName).doc(universityId).collection('private').doc('startupShowcase').get();
  if (!privateUniSnapshot.exists) {
    return {valid: false};
  }

  const privateData = privateUniSnapshot.data() as {token: string};

  if (privateData.token !== token) {
    console.error(`Tokens do not match. universityId=${universityId}, token=${token}`);
    return {valid: false};
  }

  const projectsSnapshot = await admin.firestore().collection(Project.collectionName).where('universityId', '==', universityId).get();
  const startups = projectsSnapshot.docs.map((d) => {
    const data = d.data();
    return {...data, id: d.id, creationDate: data.creationDate.toMillis(), logoUpdatedAt: data.logoUpdatedAt.toMillis()} as StartupShowcaseProject;
  });

  return {
    valid: true,
    startups,
  };
});
