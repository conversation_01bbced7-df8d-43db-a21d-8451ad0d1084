name: Deploy Firebase Functions

on:
  push:
    branches:
      - main
    paths:
      - 'functions/**'
      - 'common/**'
      - 'package.json'
      - 'firebase.json'
      - '.firebaserc'
      - '.npmrc'
      - '.github/workflows/deploy_firebase_functions.yml'

jobs:
  deploy:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        project: [production, demo]

    steps:
      - uses: actions/checkout@v2

      # Use Node 20
      - name: Setup Node 20
        uses: actions/setup-node@v3
        with:
          node-version: 20.x

      - name: Install dependencies
        run: yarn install

      # Deploy functions
      - name: Deploy Functions
        uses: w9jds/firebase-action@master
        with:
          args: deploy --only functions --project ${{ matrix.project }} --force
        env:
          FIREBASE_TOKEN: "${{ secrets.FIREBASE_DEPLOY_TOKEN }}"
