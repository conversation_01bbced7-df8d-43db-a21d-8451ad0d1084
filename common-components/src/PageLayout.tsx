import {Box} from '@mui/joy';
import {Header} from './Header';
import {Outlet, useLocation} from 'react-router-dom';

export function PageLayout() {
  const location = useLocation();
  const isOnboarding = location.pathname === '/onboarding';

  return (
    <Box
      component='main'
      className='MainContent'
      sx={{
        flex: 1,
        // pt: { xs: 'calc(12px + var(--Header-height))', md: 3 },
        // pb: { xs: 2, sm: 2, md: 3 },
        // px: { xs: 2, md: 4 },
        // py: 2,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        minWidth: 0,
        height: '100dvh',
        gap: 1,
        overflow: 'auto',
        backgroundColor: 'background.body',
        borderBottom: '1px solid',
        borderColor: 'divider',
      }}
    >
      {!isOnboarding && <Header />}
      <Outlet />
    </Box>
  );
}
