import Stripe from 'stripe';
import * as admin from 'firebase-admin';
import {PaymentInterval, University} from '@creator-campus/common';
import {FieldValue} from 'firebase-admin/firestore';
import * as Sentry from '@sentry/node';
import {onCall, onRequest} from 'firebase-functions/https';

const usingEmulators = !!process.env.FIRESTORE_EMULATOR_HOST;
const stripeOptions = {
  apiVersion: '2025-03-31.basil' as any,
};

interface CreateCheckoutSessionPayload {
  universityId: string;
  interval: PaymentInterval;
  priceGbp: number;
  baseUrl: string;
}

export const createCheckoutSession = onCall<CreateCheckoutSessionPayload>({secrets: ['REACT_APP_STRIPE_SK_TEST', 'REACT_APP_STRIPE_SK_LIVE']}, async (data, context) => {
  try {
    const {universityId, interval, priceGbp, baseUrl} = data.data;
    console.log(`Creating checkout session for ${universityId}...`);

    Sentry.setTag('function', 'createCheckoutSession');
    Sentry.setContext('data', {...data.data});

    const secretKey = usingEmulators ? String(process.env.REACT_APP_STRIPE_SK_TEST) : String(process.env.REACT_APP_STRIPE_SK_LIVE);
    const stripe = new Stripe(secretKey, stripeOptions);

    const pricePence = priceGbp * 100;

    await admin.firestore().collection(University.collectionName).doc(universityId).update({
      paymentState: 'pending',
    });

    Sentry.addBreadcrumb({
      message: 'Updated payment state to pending',
      category: 'firestore',
      level: 'info',
    });

    const metadata = {
      universityId,
      interval,
    };

    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      mode: 'subscription',
      line_items: [
        {
          price_data: {
            currency: 'gbp',
            recurring: {
              interval: interval === 'monthly' ? 'month' : 'year',
            },
            product_data: {
              name: 'Creator Campus Subscription',
            },
            unit_amount: pricePence,
          },
          quantity: 1,
        },
      ],
      success_url: `${baseUrl}?checkoutStatus=success`,
      cancel_url: `${baseUrl}?checkoutStatus=cancelled`,
      metadata,
      subscription_data: {
        metadata,
      },
    });

    console.log(`Created checkout session ${session.id}`);
    return {sessionId: session.id};
  } catch (err) {
    Sentry.captureException(err, {extra: {message: 'Error creating checkout session', data}});
    console.error('Error creating checkout session:', err);
    return {sessionId: null};
  }
});

// To test locally, run: stripe listen --forward-to http://localhost:5001/creator-campus-app/europe-west2/stripePartnershipWebhook
// Then copy the webhook signing secret (printed in the console, 'whsec_...') to the
// webhookSecret in this function temporarily (then run yarn buildFunctions)
export const stripePartnershipWebhook = onRequest(
  {
    secrets: ['REACT_APP_STRIPE_WEBHOOK', 'REACT_APP_STRIPE_SK_TEST', 'REACT_APP_STRIPE_SK_LIVE'],
  },
  async (req, res) => {
    try {
      console.log('Received Stripe webhook');
      Sentry.setTag('function', 'stripePartnershipWebhook');

      const secretKey = usingEmulators ? String(process.env.REACT_APP_STRIPE_SK_TEST) : String(process.env.REACT_APP_STRIPE_SK_LIVE);
      const stripe = new Stripe(secretKey, stripeOptions);

      const signature = req.headers['stripe-signature'];
      if (!signature) {
        Sentry.captureException(new Error('No signature found in request headers'), {extra: {message: 'No signature found in request headers', signature, body: req.body}});
        console.error('No signature found in request headers');
        res.sendStatus(400);
        return;
      }

      const webhookSecret = String(process.env.REACT_APP_STRIPE_WEBHOOK);

      let event;

      try {
        event = stripe.webhooks.constructEvent(req.rawBody, signature, webhookSecret);
      } catch (e) {
        Sentry.captureException(e, {extra: {message: 'Webhook signature verification failed.', signature, body: req.body}});
        console.error('Webhook signature verification failed.', e);
        res.sendStatus(400);
        return;
      }

      console.log(`Event type: ${event.type}`);
      const eventData = event.data.object as Stripe.Checkout.Session | Stripe.Subscription | Stripe.Invoice;
      console.log('Event data:', eventData);
      Sentry.setContext('stripe_event', {
        type: event?.type,
        id: event?.id,
        eventData,
      });

      // TODO: Metadata doesn't seem to be included unless the event type is checkout.session.completed
      const universityId = eventData.metadata?.universityId;
      const interval = eventData.metadata?.interval as PaymentInterval | undefined;

      if (!universityId || !interval) {
        console.error('University ID or interval not found in metadata:', eventData.metadata);
        Sentry.captureException(new Error('University ID or interval not found in metadata'), {extra: {message: 'University ID or interval not found in metadata', metadata: eventData.metadata, eventData}});
        res.sendStatus(400);
        return;
      }

      await handleStripeEvent(event, universityId, interval);

      res.status(200).send('Received');
    } catch (err) {
      Sentry.captureException(err, {extra: {message: 'Error handling Stripe webhook', body: req.body}});
      console.error('Error handling Stripe webhook:', err);
      res.sendStatus(500);
    }
  },
);

async function handleStripeEvent(event: Stripe.Event, universityId: string, interval: PaymentInterval) {
  if (event.type === 'checkout.session.completed' || event.type === 'invoice.payment_succeeded') {
    console.log(`Marking ${universityId} as partner`);
    await admin.firestore().collection(University.collectionName).doc(universityId).update({
      partner: true,
      paymentState: 'paying',
    });
    Sentry.addBreadcrumb({
      message: 'Updated payment state to paying',
      category: 'firestore',
      level: 'info',
    });

    await admin.firestore().collection(University.collectionName).doc(universityId).collection('private').doc('partnership').update({
      lastPaymentDate: FieldValue.serverTimestamp(),
      paymentInterval: interval,
    });
    Sentry.addBreadcrumb({
      message: 'Updated partnership data',
      category: 'firestore',
      level: 'info',
      data: {
        lastPaymentDate: FieldValue.serverTimestamp(),
        paymentInterval: interval,
      },
    });
  } else if (event.type === 'customer.subscription.deleted' || event.type === 'invoice.payment_failed') {
    console.log(`Marking ${universityId} as not partner`);
    await admin.firestore().collection(University.collectionName).doc(universityId).update({
      partner: false,
      paymentState: 'notPaying',
    });
    Sentry.addBreadcrumb({
      message: 'Updated payment state to not paying',
      category: 'firestore',
      level: 'info',
    });

    await admin.firestore().collection(University.collectionName).doc(universityId).collection('private').doc('partnership').update({
      paymentInterval: null,
    });
    Sentry.addBreadcrumb({
      message: 'Reset payment interval',
      category: 'firestore',
      level: 'info',
    });
  } else {
    console.log(`Ignoring unhandled event type: ${event.type}`);
  }
}
