pre-commit:
  parallel: true
  commands:
    format:
      run: yarn format:apply
      pass_filenames: false

post-checkout:
  commands:
    post_checkout_tasks:
      run: |
        echo "Switched branches"
        
        lefthook install --force
        
        yarn install
        yarn buildCommon
        yarn buildFunctions

post-merge:
  commands:
    post_merge_tasks:
      run: |
        lefthook install --force

        # Get changed files in the last merge
        CHANGED_FILES=$(git diff --name-only HEAD@{1} HEAD)

        yarn install
        yarn build

        if echo "$CHANGED_FILES" | grep -q "^.env.example$"; then
          echo "Detected changes to .env.example. Running sync_secrets.sh..."
          sh sync_secrets.sh
        else
          echo "No changes to .env.example detected."
        fi
