import {onDocumentCreated, onDocumentDeleted, onDocumentUpdated} from 'firebase-functions/v2/firestore';
import {DocumentReference, FieldValue} from 'firebase-admin/firestore';
import * as admin from 'firebase-admin';
import {DiscussionPost, DiscussionPostBase, DiscussionPostDbModelSafe, University, User} from '@creator-campus/common';
import {deletePostAttachments, movePendingAttachments} from './utils';

async function processCreatedPost(post: DiscussionPostDbModelSafe, postId: string, universityId?: string) {
  const discussionDoc = universityId ? admin.firestore().collection(University.collectionName).doc(universityId) : admin.firestore().collection('global').doc('discussion');
  await discussionDoc.update({
    lastDiscussionPost: new Date(),
  });

  const originFolder = DiscussionPostBase.getPendingUploadsFolder(universityId);
  const destinationFolder = DiscussionPost.getUploadsFolder(postId, universityId);
  await movePendingAttachments(post, originFolder, destinationFolder);
}

export const onUniDiscussionPostCreated = onDocumentCreated('universities/{universityId}/discussion/{postId}', async (event) => {
  if (!event.data) {
    console.error('Event data was undefined!');
    return;
  }

  const post = event.data.data() as DiscussionPostDbModelSafe;
  const postId = event.params.postId;
  const universityId = event.params.universityId;

  await processCreatedPost(post, postId, universityId);
});

export const onGlobalDiscussionPostCreated = onDocumentCreated('global/discussion/discussion/{postId}', async (event) => {
  if (!event.data) {
    console.error('Event data was undefined!');
    return;
  }

  const post = event.data.data() as DiscussionPostDbModelSafe;
  const postId = event.params.postId;

  await processCreatedPost(post, postId);
});

async function processUpdatedPost(beforeData: DiscussionPostDbModelSafe, afterData: DiscussionPostDbModelSafe, docRef: DocumentReference) {
  const upVotesBefore = beforeData.upVotes.length;
  const upVotesAfter = afterData.upVotes.length;
  const upVotesChanged = upVotesBefore !== upVotesAfter;

  if (upVotesChanged) {
    // Update author's karma
    await admin
      .firestore()
      .collection(User.collectionName)
      .doc(afterData.authorId)
      .update({
        karma: FieldValue.increment(upVotesAfter - upVotesBefore),
      });

    return docRef.update({
      numUpVotes: upVotesAfter,
    });
  }

  return null;
}

export const onUniDiscussionPostUpdated = onDocumentUpdated('universities/{universityId}/discussion/{postId}', async (event) => {
  if (!event.data) {
    console.error('Event data was undefined!');
    return;
  }

  const beforeData = event.data.before.data() as DiscussionPostDbModelSafe;
  const afterData = event.data.after.data() as DiscussionPostDbModelSafe;

  return await processUpdatedPost(beforeData, afterData, event.data.after.ref);
});

export const onGlobalDiscussionPostUpdated = onDocumentUpdated('global/discussion/discussion/{postId}', async (event) => {
  if (!event.data) {
    console.error('Event data was undefined!');
    return;
  }

  const beforeData = event.data.before.data() as DiscussionPostDbModelSafe;
  const afterData = event.data.after.data() as DiscussionPostDbModelSafe;

  return await processUpdatedPost(beforeData, afterData, event.data.after.ref);
});

async function processDeletedPost(post: DiscussionPostDbModelSafe, postId: string, universityId?: string) {
  const storageFolder = DiscussionPost.getUploadsFolder(postId, universityId);
  await deletePostAttachments(post, postId, storageFolder);
}

export const onUniDiscussionPostDeleted = onDocumentDeleted('universities/{universityId}/discussion/{postId}', async (event) => {
  if (!event.data) {
    console.error('Event data was undefined!');
    return;
  }

  const post = event.data.data() as DiscussionPostDbModelSafe;
  const postId = event.params.postId;
  const universityId = event.params.universityId;

  return await processDeletedPost(post, postId, universityId);
});

export const onGlobalDiscussionPostDeleted = onDocumentDeleted('global/discussion/discussion/{postId}', async (event) => {
  if (!event.data) {
    console.error('Event data was undefined!');
    return;
  }

  const post = event.data.data() as DiscussionPostDbModelSafe;
  const postId = event.params.postId;

  return await processDeletedPost(post, postId);
});
