import {CircularProgress} from '@mui/joy';
import {<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Toolt<PERSON>} from 'recharts';
import Box from '@mui/joy/Box';
import {PieChartNoData} from '../PieChartNoData';
import { PieChartData } from '@creator-campus/common';

interface Props {
  data: PieChartData[] | null;
  dynamicShadesHue?: number;
  showEmptyLegendItems?: boolean;
  onSegmentClick?: (data: any) => void;
}

export function AdminPieChart({data, dynamicShadesHue, showEmptyLegendItems = true, onSegmentClick}: Props) {
  const baseSize = {
    width: 275,
    height: 275,
  };
  const outerRadius = baseSize.width / 3;
  const baseMargin = {
    top: 0,
    right: 0,
    left: 0,
    bottom: 0,
  };

  function getPredefinedColorPalette(): string[] {
    return ['#003f5c', '#2f4b7c', '#665191', '#a05195', '#d45087', '#f95d6a', '#ff7c43', '#ffa600'];
  }

  function getDynamicShades(hue: number, numShades: number): string[] {
    const minBrightness = 30;
    const maxBrightness = 90;

    const colours: string[] = [];
    for (let i = 0; i < numShades; i++) {
      const lightness = minBrightness + i * ((maxBrightness - minBrightness) / numShades);
      colours.push(`hsl(${hue}, 100%, ${lightness}%)`);
    }

    return colours;
  }

  // function generateColourPalette(numColours: number): string[] {
  //   const colours: string[] = [];
  //   const hueStep = 360 / numColours;
  //
  //   for (let i = 0; i < numColours; i++) {
  //     const hue = Math.round(i * hueStep);
  //     const color = `hsl(${hue}, 80%, 47%)`; // hue, saturation, lightness
  //     colours.push(color);
  //   }
  //
  //   return colours;
  // }

  function renderPieChartLabel(entry: {name: string; value: number}) {
    return entry.value > 0 ? `${entry.value}` : '';
  }

  function collapseSmallCategories(data: PieChartData[]) {
    const smallCategoryProportionThresh = 0.03; // Categories with less than 3% proportion are grouped into 'other' category
    const totalValue = data.reduce((sum, current) => sum + current.value, 0);

    let otherValue = 0;
    const smallCategories: PieChartData[] = [];
    for (const category of data) {
      if (category.value / totalValue < smallCategoryProportionThresh) {
        otherValue += category.value;
        smallCategories.push(category);
      }
    }

    if (otherValue > 0) {
      const filteredData = data.filter((item) => smallCategories.indexOf(item) < 0);
      filteredData.push({name: 'Other', value: otherValue, colour: undefined});
      return filteredData;
    } else {
      return data;
    }
  }

  if (!data) {
    return (
      <>
        <Box
          sx={{
            width: baseSize.width,
            height: baseSize.height,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <CircularProgress size='md' />
        </Box>
      </>
    );
  }

  let cleanData = collapseSmallCategories(data);
  cleanData.sort((a, b) => (a.value > b.value ? -1 : 1));

  const colourPalette = dynamicShadesHue ? getDynamicShades(dynamicShadesHue, cleanData.length) : getPredefinedColorPalette();

  // Filter the legend payload to exclude items with a value of 0
  const totalValue = cleanData.reduce((n, {value}) => n + value, 0);
  const legendPayload = cleanData
    .filter((entry) => (showEmptyLegendItems && totalValue > 0 ? true : entry.value / totalValue >= 0.1))
    .sort((a, b) => (a.value > b.value ? -1 : a.value < b.value ? 1 : 0))
    .map((entry) => ({
      id: entry.name,
      type: 'square',
      value: entry.name,
      color: entry.colour || colourPalette[cleanData.indexOf(entry) % colourPalette.length],
    }));

  // Adapt height when the legend has a lot of items
  const size = {
    width: baseSize.width,
    height: baseSize.height + legendPayload.length * 25,
  };

  const margin = {
    top: baseMargin.top,
    right: baseMargin.right,
    left: baseMargin.left,
    bottom: baseMargin.bottom + legendPayload.length * 25,
  };

  if (totalValue === 0) {
    return (
      <PieChartNoData
        size={size}
        margin={margin}
        outerRadius={outerRadius}
      />
    );
  }

  return (
    <>
      <PieChart
        width={size.width}
        height={size.height}
        margin={margin}
      >
        <Pie
          data={cleanData}
          dataKey='value'
          nameKey='name'
          cx='50%'
          cy='50%'
          outerRadius={outerRadius}
          labelLine={false}
          label={renderPieChartLabel}
          onClick={onSegmentClick}
        >
          {cleanData.map((entry, index) => (
            <Cell
              key={`cell-${entry.name}`}
              fill={entry.colour || colourPalette[index % colourPalette.length]}
            />
          ))}
        </Pie>
        <Tooltip />
        <Legend
          //@ts-ignore
          payload={legendPayload}
          height={20}
        />
      </PieChart>
    </>
  );
}
