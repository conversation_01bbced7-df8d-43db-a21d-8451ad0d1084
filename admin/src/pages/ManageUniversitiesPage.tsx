import {useState} from 'react';
import {collection} from 'firebase/firestore';
import {firestore, University, UniversityConverter} from '@creator-campus/common';
import {FirestoreInfiniteScroll, PageHeader, PageShell, useSnackbar} from '@creator-campus/common-components';
import UniversityCard from '../components/UniversityCard.tsx';
import Button from '@mui/joy/Button';
import {Add} from '@mui/icons-material';
import UniversityFormModal from '../components/UniversityFormModal.tsx';

export default function ManageUniversitiesPage() {
  const [showUniversityFormModal, setShowUniversityFormModal] = useState<boolean>(false);

  const {showSnackbar} = useSnackbar();

  return (
    <PageShell footer={false}>
      <PageHeader
        title='Manage Universities'
        subtitle='Watch over the empire!'
      />
      <Button
        variant='outlined'
        startDecorator={<Add />}
        sx={{mb: 2}}
        onClick={() => setShowUniversityFormModal(true)}
      >
        Add university
      </Button>
      {showUniversityFormModal && (
        <UniversityFormModal
          onSave={() => {
            showSnackbar('University created.', 'success');
            setShowUniversityFormModal(false);
          }}
          onCancel={() => setShowUniversityFormModal(false)}
        />
      )}
      <FirestoreInfiniteScroll
        col={collection(firestore(), University.collectionName)}
        order={[
          {sortField: 'partner', direction: 'desc'},
          {sortField: 'name', direction: 'asc'},
          {sortField: 'dateJoined', direction: 'asc'},
        ]}
        converter={new UniversityConverter()}
        scrollFullPage={true}
        itemBuilder={(u) => (
          <UniversityCard
            key={u.id}
            university={u}
          />
        )}
        itemHeight={100}
        sx={{display: 'flex', flexDirection: 'column', scrollbarWidth: 'none', gap: 10}}
      />
    </PageShell>
  );
}
