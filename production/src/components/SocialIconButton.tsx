import {ReactNode} from 'react';
import {openLink} from '@creator-campus/common';
import IconButton from '@mui/joy/IconButton';

interface Props {
  icon: ReactNode;
  link: string;
}

export default function SocialIconButton({icon, link}: Props) {
  return (
    <IconButton
      size='sm'
      variant='plain'
      color='neutral'
      onClick={() => openLink(link)}
    >
      {icon}
    </IconButton>
  );
}
