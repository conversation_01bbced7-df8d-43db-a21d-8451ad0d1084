import DeveloperMode from '@mui/icons-material/DeveloperMode';
import PhotoCamera from '@mui/icons-material/PhotoCamera';
import Crop from '@mui/icons-material/Crop';
import Videocam from '@mui/icons-material/Videocam';
import School from '@mui/icons-material/School';
import Translate from '@mui/icons-material/Translate';
import Brush from '@mui/icons-material/Brush';
import MusicNote from '@mui/icons-material/MusicNote';
import FitnessCenter from '@mui/icons-material/FitnessCenter';
import Edit from '@mui/icons-material/Edit';
import Web from '@mui/icons-material/Web';

import {ElementType} from 'react';
import {Persona} from './persona';

export type ServiceId = 'photography' | 'mobile-app-dev' | 'web-dev' | 'tutoring' | 'language-lessons' | 'graphic-design' | 'music-lessons' | 'fitness' | 'copywriting' | 'proofreading' | 'photo-editing' | 'video-editing';

export class Service {
  static readonly PHOTOGRAPHY = new Service('photography', 'Photography', PhotoCamera);
  static readonly MOBILE_APP_DEV = new Service('mobile-app-dev', 'Mobile App Development', DeveloperMode);
  static readonly WEB_DEV = new Service('web-dev', 'Web Development', Web);
  static readonly TUTORING = new Service('tutoring', 'Tutoring', School);
  static readonly LANGUAGE_LESSONS = new Service('language-lessons', 'Language Lessons', Translate);
  static readonly GRAPHIC_DESIGN = new Service('graphic-design', 'Graphic Design', Brush);
  static readonly MUSIC_LESSONS = new Service('music-lessons', 'Music Lessons', MusicNote);
  static readonly PERSONAL_TRAINING = new Service('fitness', 'Personal Training', FitnessCenter);
  static readonly PROOFREADING = new Service('proofreading', 'Proofreading & Editing', Edit);
  static readonly PHOTO_EDITING = new Service('photo-editing', 'Photo Editing', Crop);
  static readonly VIDEO_EDITING = new Service('video-editing', 'Video Editing', Videocam);

  private constructor(
    readonly id: ServiceId,
    readonly label: string,
    readonly icon: ElementType,
  ) {}

  static fromId(id: string | null) {
    return this.values().find((c) => c.id === id) || null;
  }

  static recommended(persona: Persona): Service[] {
    switch (persona) {
      case Persona.TECHNICAL:
        return [Service.MOBILE_APP_DEV, Service.WEB_DEV, Service.TUTORING];
      case Persona.BUSINESS:
        return [Service.TUTORING, Service.LANGUAGE_LESSONS, Service.PROOFREADING];
      case Persona.CREATIVE:
        return [Service.GRAPHIC_DESIGN, Service.PHOTOGRAPHY, Service.PHOTO_EDITING, Service.MUSIC_LESSONS];
      default:
        return [];
    }
  }

  static values(): Service[] {
    return Object.values(Service);
  }
}
