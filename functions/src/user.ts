import {onDocumentCreated, onDocumentDeleted, onDocumentUpdated} from 'firebase-functions/v2/firestore';
import * as admin from 'firebase-admin';
import {getInfluxWriter, newPoint} from './influxdb';
import {onSchedule} from 'firebase-functions/v2/scheduler';
import {FieldValue, QueryDocumentSnapshot, Timestamp} from 'firebase-admin/firestore';
import {
  CompleteProfileReminderEmailTemplate,
  CREATOR_CAMPUS_ADMINS,
  InactiveProfileReminderEmailTemplate,
  MembershipApplication,
  MembershipApplicationDbModel,
  OpportunityApplication,
  PartnershipData,
  PendingMembershipApplicationsEmailTemplate,
  Project,
  REAPPLICATION_COOLDOWN_DAYS,
  Role,
  StaffRole,
  University,
  UniversityDbModelSafe,
  UniversityPartnershipDbModel,
  User,
  UserDbModelSafe,
  UserLimitAlmostReachedEmailTemplate,
  UserLimitReachedEmailTemplate,
} from '@creator-campus/common';
import {calculateProfileScore, calculateProjectScore, emailC<PERSON><PERSON>ampusAdmins, fetchUniversityOwners, sendEmail, initSentryForFunction} from './utils';
import {onCall} from 'firebase-functions/v2/https';
import allUniversities from './uk_universities.json';
import * as Sentry from '@sentry/node';

async function checkUniHasMentors(universityId: string) {
  Sentry.addBreadcrumb({
    message: `Checking if university ${universityId} has any mentors`,
    category: 'university',
    level: 'info',
    data: {universityId},
  });

  console.log(`Checking if university ${universityId} has any mentors...`);
  const mentorsSnapshot = await admin.firestore().collection(User.collectionName).where('universityId', '==', universityId).where('role', '==', Role.MENTOR.id).where('profileCompleted', '==', true).count().get();
  const numMentors = mentorsSnapshot.data().count;

  Sentry.addBreadcrumb({
    message: `Found ${numMentors} mentors for university ${universityId}`,
    category: 'firestore',
    level: 'info',
    data: {universityId, numMentors},
  });

  console.log(`University ${universityId} has ${numMentors} mentors. Updating Firestore doc...`);
  await admin
    .firestore()
    .collection(University.collectionName)
    .doc(universityId)
    .update({
      hasMentors: numMentors > 0,
    });

  Sentry.addBreadcrumb({
    message: `Updated university hasMentors status`,
    category: 'firestore',
    level: 'info',
    data: {universityId, hasMentors: numMentors > 0},
  });
}

async function handleUserLimitReached(universityId: string, userLimit: number) {
  Sentry.addBreadcrumb({
    message: `User limit reached for university ${universityId}`,
    category: 'university',
    level: 'warning',
    data: {universityId, userLimit},
  });

  console.log(`User limit reached for university ${universityId}.`);
  await admin.firestore().collection(University.collectionName).doc(universityId).update({
    reachedUserLimit: true,
  });

  Sentry.addBreadcrumb({
    message: 'Updated university reachedUserLimit to true',
    category: 'firestore',
    level: 'info',
    data: {universityId},
  });

  console.log('Fetching university owners...');
  const owners = await fetchUniversityOwners(universityId);
  const ownerEmails = owners.map((o) => o.email);

  Sentry.addBreadcrumb({
    message: `Sending user limit reached emails to ${ownerEmails.length} owners`,
    category: 'email',
    level: 'info',
    data: {universityId, ownerEmails, userLimit},
  });

  console.log(`Sending warning email (UserLimitReachedEmailTemplate) to university owners: ${ownerEmails}`);
  await Promise.all(ownerEmails.map((email) => sendEmail(email, new UserLimitReachedEmailTemplate({userLimit})).catch(console.error)));
}

async function handleUserLimitAlmostReached(universityId: string, userLimit: number, numUsers: number) {
  Sentry.addBreadcrumb({
    message: `User limit almost reached for university ${universityId}`,
    category: 'university',
    level: 'warning',
    data: {universityId, userLimit, numUsers},
  });

  console.log(`User limit almost reached for university ${universityId}. Updating Firestore doc...`);
  await admin.firestore().collection(University.collectionName).doc(universityId).update({
    almostReachedUserLimit: true,
  });

  Sentry.addBreadcrumb({
    message: 'Updated university almostReachedUserLimit to true',
    category: 'firestore',
    level: 'info',
    data: {universityId},
  });

  console.log('Fetching university owners...');
  const owners = await fetchUniversityOwners(universityId);
  const ownerEmails = owners.map((o) => o.email);

  Sentry.addBreadcrumb({
    message: `Sending user limit almost reached emails to ${ownerEmails.length} owners`,
    category: 'email',
    level: 'info',
    data: {universityId, ownerEmails, userLimit, numUsers},
  });

  console.log(`Sending warning email (UserLimitAlmostReachedEmailTemplate) to university owners: ${ownerEmails}`);
  await Promise.all(ownerEmails.map((email) => sendEmail(email, new UserLimitAlmostReachedEmailTemplate({userLimit, numUsers})).catch(console.error)));
}

async function checkUniversityReachedUserLimit(universityId: string) {
  Sentry.addBreadcrumb({
    message: `Checking user limit for university ${universityId}`,
    category: 'university',
    level: 'info',
    data: {universityId},
  });

  // Count the number of users for this university
  const numUsersSnapshot = await admin.firestore().collection(User.collectionName).where('universityId', '==', universityId).where('profileCompleted', '==', true).count().get();
  const numUsers = numUsersSnapshot.data().count;
  console.log(`Found ${numUsers} users for university ${universityId}.`);

  // Fetch user limit for this university
  const partnershipDataSnapshot = await admin.firestore().collection(University.collectionName).doc(universityId).collection('private').doc('partnership').get();
  const userLimit = partnershipDataSnapshot.data()?.userLimit;
  console.log(`Found user limit of ${userLimit} for university ${universityId}.`);

  Sentry.addBreadcrumb({
    message: `University user count vs limit check`,
    category: 'university',
    level: 'info',
    data: {universityId, numUsers, userLimit, warningThreshold: userLimit ? University.getUserLimitWarningThreshold(userLimit) : null},
  });

  if (userLimit) {
    if (numUsers > userLimit) {
      await handleUserLimitReached(universityId, userLimit);
    } else if (numUsers > University.getUserLimitWarningThreshold(userLimit)) {
      await handleUserLimitAlmostReached(universityId, userLimit, numUsers);
    } else {
      console.log(`User limit not reached for university ${universityId}.`);
      Sentry.addBreadcrumb({
        message: 'User limit not reached',
        category: 'university',
        level: 'info',
        data: {universityId, numUsers, userLimit},
      });
    }
  } else {
    console.error(`No user limit found for university ${universityId}.`);
    Sentry.addBreadcrumb({
      message: 'No user limit found for university',
      category: 'university',
      level: 'error',
      data: {universityId},
    });
  }
}

export const onUserCreated = onDocumentCreated(
  {
    document: 'people/{userId}',
    secrets: ['REACT_APP_INFLUX_DB_TOKEN'],
  },
  async (event) => {
    initSentryForFunction('onUserCreated');

    if (!event.data) {
      console.error('Event data is undefined!');
      Sentry.addBreadcrumb({
        message: 'Event data is undefined in onUserCreated',
        category: 'function',
        level: 'error',
      });
      return;
    }

    const user = event.data.data() as UserDbModelSafe;
    const userId = event.params.userId;
    Sentry.setUser({id: userId, ...user});
    console.log(`Detected user ${userId} created.`);

    Sentry.addBreadcrumb({
      message: `User created: ${userId}`,
      category: 'user',
      level: 'info',
      data: {userId, universityId: user.universityId, role: user.role, email: user.email},
    });

    // Auto-accept mentors
    if (user.role === Role.MENTOR.id) {
      Sentry.addBreadcrumb({
        message: 'Auto-accepting mentor user',
        category: 'user',
        level: 'info',
        data: {userId, role: user.role},
      });

      await admin.firestore().collection(User.collectionName).doc(userId).update({
        applicationStatus: 'accepted',
      });

      Sentry.addBreadcrumb({
        message: 'Updated mentor application status to accepted',
        category: 'firestore',
        level: 'info',
        data: {userId},
      });
    }

    await ensureUniversityDocExists(user.universityId);
    await checkUniversityReachedUserLimit(user.universityId);

    Sentry.addBreadcrumb({
      message: 'Writing user creation metric to InfluxDB',
      category: 'metrics',
      level: 'info',
      data: {userId, universityId: user.universityId},
    });

    const influxToken = String(process.env.REACT_APP_INFLUX_DB_TOKEN);
    const influxWriter = getInfluxWriter(influxToken);

    const point = newPoint().intField('numUsers', 1).tag('university', user.universityId);
    influxWriter.writePoint(point);
  },
);

export const onUserUpdated = onDocumentUpdated('people/{userId}', async (event) => {
  initSentryForFunction('onUserUpdated');

  if (!event.data) {
    console.error('Event data is undefined!');
    Sentry.addBreadcrumb({
      message: 'Event data is undefined in onUserUpdated',
      category: 'function',
      level: 'error',
    });
    return;
  }

  const userId = event.params.userId;
  const before = event.data.before.data() as UserDbModelSafe;
  const after = event.data.after.data() as UserDbModelSafe;
  Sentry.setUser({id: userId, ...after});
  Sentry.setContext('userBefore', {...before});
  Sentry.setContext('userAfter', {...after});

  console.log(`Detected user ${userId} updated.`);

  Sentry.addBreadcrumb({
    message: `User updated: ${userId}`,
    category: 'user',
    level: 'info',
    data: {
      userId,
      emailChanged: before.email !== after.email,
      nameChanged: before.name !== after.name,
      applicationStatusChanged: before.applicationStatus !== after.applicationStatus,
      roleChanged: before.role !== after.role,
    },
  });

  // Update opportunity applications if user details changed
  const cvRemoved = before.cvUploaded && !after.cvUploaded;
  if (before.email !== after.email || before.name !== after.name || cvRemoved) {
    console.log('Detected user email or name changed.');

    Sentry.addBreadcrumb({
      message: 'User details changed, updating applications',
      category: 'user',
      level: 'info',
      data: {
        userId,
        emailChanged: before.email !== after.email,
        nameChanged: before.name !== after.name,
        cvRemoved,
      },
    });

    await updateUserApplications(userId, after.name, after.email, cvRemoved);
  }

  // Show hidden projects if user has been accepted
  if (before.applicationStatus !== 'accepted' && after.applicationStatus === 'accepted') {
    console.log('Detected membership application accepted.');

    Sentry.addBreadcrumb({
      message: 'Membership application accepted',
      category: 'user',
      level: 'info',
      data: {userId, projectCount: after.projectIds.length},
    });

    if (after.projectIds.length > 0) {
      await showHiddenProjects(after.projectIds);
    }
  }

  if (before.applicationStatus !== 'submitted' && after.applicationStatus === 'submitted') {
    console.log('Detected membership application submitted.');

    Sentry.addBreadcrumb({
      message: 'Membership application submitted',
      category: 'user',
      level: 'info',
      data: {userId, universityId: after.universityId, name: after.name},
    });

    await emailCreatorCampusAdmins('New User Application', `${after.name} from ${after.universityId} has applied to join Creator Campus. Please log in to the admin app (admin.creatorcampus.io) to review their application.`);
  }

  const {profileCompleteStatusChanged, isProfileCompletedNow} = checkProfileCompletionChanged(after);
  console.log(`Profile complete status changed: ${profileCompleteStatusChanged}. Is profile completed now: ${isProfileCompletedNow}.`);

  Sentry.addBreadcrumb({
    message: 'Profile completion status check',
    category: 'user',
    level: 'info',
    data: {userId, profileCompleteStatusChanged, isProfileCompletedNow},
  });

  if ((before.role === Role.MENTOR.id) !== (after.role === Role.MENTOR.id)) {
    // Role changed to or from mentor
    Sentry.addBreadcrumb({
      message: 'User role changed to/from mentor',
      category: 'user',
      level: 'info',
      data: {userId, beforeRole: before.role, afterRole: after.role, universityId: after.universityId},
    });

    await checkUniHasMentors(after.universityId);
  }

  // Update project scores if user's karma or last online changed
  const projectScoresChanged = before.karma !== after.karma || before.lastOnline !== after.lastOnline;
  if (projectScoresChanged) {
    Sentry.addBreadcrumb({
      message: 'User karma or last online changed, updating project scores',
      category: 'user',
      level: 'info',
      data: {
        userId,
        karmaChanged: before.karma !== after.karma,
        lastOnlineChanged: before.lastOnline !== after.lastOnline,
        projectCount: after.projectIds.length,
      },
    });

    for (const projectId of after.projectIds) {
      const newScore = await calculateProjectScore(projectId);
      console.log(`Project ${projectId} score changed to ${newScore}. Updating...`);

      Sentry.addBreadcrumb({
        message: `Updated project score`,
        category: 'firestore',
        level: 'info',
        data: {projectId, newScore},
      });

      await admin.firestore().collection(Project.collectionName).doc(projectId).update({
        score: newScore,
      });
    }
  }

  const {founderStatusChanged, isFounderNow} = checkFounderStatusChanged(after);
  const lastOnlineChangedSignificantly = Math.abs((before.lastOnline as Timestamp).toMillis() - (after.lastOnline as Timestamp).toMillis()) > 1000 * 60 * 60 * 24; // 1 day
  const profileScoreChanged = before.karma !== after.karma || lastOnlineChangedSignificantly || before.bio.length !== after.bio.length || !!before.linkedinUsername !== !!after.linkedinUsername || !!before.githubUsername !== !!after.githubUsername || !!before.website !== !!after.website;

  Sentry.addBreadcrumb({
    message: 'User status changes check',
    category: 'user',
    level: 'info',
    data: {
      userId,
      founderStatusChanged,
      isFounderNow,
      profileCompleteStatusChanged,
      isProfileCompletedNow,
      profileScoreChanged,
      lastOnlineChangedSignificantly,
    },
  });

  if (founderStatusChanged || profileCompleteStatusChanged || profileScoreChanged) {
    const newScore = profileScoreChanged ? await calculateProfileScore(userId) : null;
    if (newScore) {
      console.log(`Profile score changed from ${before.score} to ${newScore}.`);

      Sentry.addBreadcrumb({
        message: 'Profile score calculated',
        category: 'user',
        level: 'info',
        data: {userId, oldScore: before.score, newScore},
      });
    }

    const userUpdate = {
      ...(founderStatusChanged && {founder: isFounderNow}),
      ...(profileCompleteStatusChanged && {profileCompleted: isProfileCompletedNow}),
      ...(newScore && {score: newScore}),
    };

    console.log(`Updating user ${userId} with data:`, userUpdate);

    Sentry.addBreadcrumb({
      message: 'Updating user with calculated changes',
      category: 'firestore',
      level: 'info',
      data: {userId, userUpdate},
    });

    return event.data.after.ref.update(userUpdate);
  }

  return null;
});

async function updateUserApplications(userId: string, name: string, email: string, cvRemoved: boolean) {
  Sentry.addBreadcrumb({
    message: 'Updating user applications with new details',
    category: 'user',
    level: 'info',
    data: {userId, name, email, cvRemoved},
  });

  const applications = await admin.firestore().collection(OpportunityApplication.collectionName).where('applicantId', '==', userId).get();

  console.log(`Updating ${applications.size} opportunity applications...`);

  Sentry.addBreadcrumb({
    message: `Found ${applications.size} applications to update`,
    category: 'firestore',
    level: 'info',
    data: {userId, applicationCount: applications.size},
  });

  const updates = applications.docs.map((doc) => doc.ref.update({applicantName: name, applicantEmail: email, ...(cvRemoved && {cvDownloadUrl: null})}));
  await Promise.all(updates);

  Sentry.addBreadcrumb({
    message: 'Successfully updated all user applications',
    category: 'firestore',
    level: 'info',
    data: {userId, updatedCount: applications.size},
  });
}

async function ensureUniversityDocExists(universityId: string) {
  Sentry.addBreadcrumb({
    message: `Ensuring university doc exists for ${universityId}`,
    category: 'university',
    level: 'info',
    data: {universityId},
  });

  console.log(`Ensuring university doc exists for ${universityId}...`);
  const universityDoc = admin.firestore().collection('universities').doc(universityId);
  const snapshot = await universityDoc.get();

  if (snapshot.exists) {
    Sentry.addBreadcrumb({
      message: 'University doc already exists',
      category: 'university',
      level: 'info',
      data: {universityId},
    });
    return;
  }

  console.log(`User university ${universityId} does not yet exist. Creating it now...`);

  Sentry.addBreadcrumb({
    message: 'University doc does not exist, creating new one',
    category: 'university',
    level: 'info',
    data: {universityId},
  });

  const uni = allUniversities.find((u) => u.name === universityId);

  if (!uni) {
    console.error(`Error finding university ${universityId} in world universities list.`);

    Sentry.addBreadcrumb({
      message: 'University not found in world universities list',
      category: 'university',
      level: 'error',
      data: {universityId},
    });
    return;
  }

  const newUniDoc: UniversityDbModelSafe = {
    almostReachedUserLimit: false,
    branding: null,
    dateJoined: Timestamp.now(),
    domain: uni.domains[0],
    hasMentors: false,
    lastDiscussionPost: Timestamp.now(),
    name: uni.name,
    partner: false,
    partnerSince: null,
    paymentState: 'notPaying',
    pinnedPosts: [],
    reachedUserLimit: false,
    selfManagedAlumniApplications: false,
    showcaseEnabled: false,
    sidebarLinks: null,
  };

  console.log('Setting new university doc:', newUniDoc);

  Sentry.addBreadcrumb({
    message: 'Creating new university document',
    category: 'firestore',
    level: 'info',
    data: {universityId, domain: uni.domains[0], name: uni.name},
  });

  await universityDoc.set(newUniDoc);

  const partnershipData: UniversityPartnershipDbModel = {
    monthlyPriceGbp: 100,
    lastPaymentDate: null,
    paymentInterval: null,
    userLimit: 300,
  };

  console.log('Setting new partnership data doc:', partnershipData);

  Sentry.addBreadcrumb({
    message: 'Creating university partnership data',
    category: 'firestore',
    level: 'info',
    data: {universityId, userLimit: partnershipData.userLimit, monthlyPriceGbp: partnershipData.monthlyPriceGbp},
  });

  await admin.firestore().doc(`${universityDoc.path}/private/${PartnershipData.collectionName}`).set(partnershipData);
}

async function showHiddenProjects(projectIds: string[]) {
  Sentry.addBreadcrumb({
    message: 'Showing hidden projects for accepted user',
    category: 'project',
    level: 'info',
    data: {projectIds, projectCount: projectIds.length},
  });

  // Expecting only one projectId (only one project submission is allowed), but loop just in case
  for (const projectId of projectIds) {
    Sentry.addBreadcrumb({
      message: `Unhiding project ${projectId}`,
      category: 'firestore',
      level: 'info',
      data: {projectId},
    });

    await admin.firestore().collection(Project.collectionName).doc(projectId).update({hidden: false});
  }
}

function checkFounderStatusChanged(after: UserDbModelSafe) {
  const isFounderNow = after.projectIds.length > 0;
  const noLongerFounder = after.founder && !isFounderNow;
  const justBecameFounder = !after.founder && isFounderNow;

  const founderStatusChanged = after.applicationStatus === 'accepted' && (noLongerFounder || justBecameFounder);

  return {founderStatusChanged, isFounderNow};
}

function checkProfileCompletionChanged(after: UserDbModelSafe) {
  const isProfileCompletedNow = User.isProfileComplete(after);
  const profileCompleteStatusChanged = after.profileCompleted !== isProfileCompletedNow;

  return {profileCompleteStatusChanged, isProfileCompletedNow};
}

export const onUserDeleted = onDocumentDeleted('people/{userId}', async (event) => {
  initSentryForFunction('onUserDeleted');

  if (!event.data) {
    console.error('Event data is undefined!');
    Sentry.addBreadcrumb({
      message: 'Event data is undefined in onUserDeleted',
      category: 'function',
      level: 'error',
    });
    return;
  }

  const userId = event.params.userId;
  console.log(`Detected user ${userId} deleted.`);
  Sentry.setUser({id: userId, ...event.data.data()});

  Sentry.addBreadcrumb({
    message: `User deleted: ${userId}`,
    category: 'user',
    level: 'info',
    data: {userId},
  });

  // Delete user's projects
  const userProjectsSnapshot = await admin.firestore().collection(Project.collectionName).where('ownerId', '==', userId).get();
  console.log(`Deleting ${userProjectsSnapshot.size} projects for user ${userId}...`);

  Sentry.addBreadcrumb({
    message: `Deleting user's projects`,
    category: 'cleanup',
    level: 'info',
    data: {userId, projectCount: userProjectsSnapshot.size},
  });

  for (const project of userProjectsSnapshot.docs) {
    console.log(`Deleting project: ${project.id}`);
    await project.ref.delete();
  }

  // Delete user's opportunity applications
  const userApplicationsSnapshot = await admin.firestore().collection(OpportunityApplication.collectionName).where('applicantId', '==', userId).get();
  console.log(`Deleting ${userApplicationsSnapshot.size} opportunity applications for user ${userId}...`);

  Sentry.addBreadcrumb({
    message: `Deleting user's opportunity applications`,
    category: 'cleanup',
    level: 'info',
    data: {userId, applicationCount: userApplicationsSnapshot.size},
  });

  for (const application of userApplicationsSnapshot.docs) {
    console.log(`Deleting application: ${application.id}`);
    await application.ref.delete();
  }

  // Delete user's private collection
  const privateColSnapshot = await admin.firestore().collection(User.collectionName).doc(userId).collection('private').get();
  console.log(`Deleting ${privateColSnapshot.size} private docs for user ${userId}...`);

  Sentry.addBreadcrumb({
    message: `Deleting user's private documents`,
    category: 'cleanup',
    level: 'info',
    data: {userId, privateDocCount: privateColSnapshot.size},
  });

  for (const doc of privateColSnapshot.docs) {
    console.log(`Deleting private doc: ${doc.id}`);
    await doc.ref.delete();
  }

  Sentry.addBreadcrumb({
    message: 'User deletion cleanup completed',
    category: 'cleanup',
    level: 'info',
    data: {userId},
  });
});

export const sendProfileCompletionReminders = onSchedule('every day 09:00', async () => {
  initSentryForFunction('sendProfileCompletionReminders');

  Sentry.addBreadcrumb({
    message: 'Starting profile completion reminders job',
    category: 'scheduler',
    level: 'info',
  });

  try {
    const now = new Date();
    const oneDayAgo = new Date(now);
    oneDayAgo.setDate(now.getDate() - 1);

    const sevenDaysAgo = new Date(now);
    sevenDaysAgo.setDate(now.getDate() - 7);

    Sentry.addBreadcrumb({
      message: 'Fetching users eligible for profile completion reminders',
      category: 'firestore',
      level: 'info',
      data: {oneDayAgo: oneDayAgo.toISOString(), sevenDaysAgo: sevenDaysAgo.toISOString()},
    });

    // Fetch users eligible for the 24-hour reminder
    const twentyFourHourSnapshot = await admin
      .firestore()
      .collection(User.collectionName)
      .where('profileCompleted', '==', false)
      .where('dateJoined', '<=', oneDayAgo) // Users who joined at least 1 day ago
      .where('numCompleteProfileRemindersSent', '==', 0) // And haven't had any reminders yet
      .get();

    // Fetch users eligible for the 7-day reminder
    const sevenDaySnapshot = await admin
      .firestore()
      .collection(User.collectionName)
      .where('profileCompleted', '==', false)
      .where('dateJoined', '<=', sevenDaysAgo) // Users who joined at least 7 days ago
      .where('numCompleteProfileRemindersSent', '==', 1) // And have had one reminder so far
      .get();

    Sentry.addBreadcrumb({
      message: 'Found users for profile completion reminders',
      category: 'firestore',
      level: 'info',
      data: {
        twentyFourHourCount: twentyFourHourSnapshot.size,
        sevenDayCount: sevenDaySnapshot.size,
      },
    });

    interface Reminder {
      docSnap: QueryDocumentSnapshot;
      type: '7-day' | '24-hour';
    }

    // Combine results
    const allReminders = [...twentyFourHourSnapshot.docs.map((docSnap): Reminder => ({docSnap, type: '24-hour'})), ...sevenDaySnapshot.docs.map((docSnap): Reminder => ({docSnap, type: '7-day'}))];

    if (allReminders.length === 0) {
      console.log('No users found who require profile completion reminders.');

      Sentry.addBreadcrumb({
        message: 'No users found who require profile completion reminders',
        category: 'scheduler',
        level: 'info',
      });
      return;
    }

    console.log(`Sending reminders to ${allReminders.length} users.`);

    Sentry.addBreadcrumb({
      message: `Sending profile completion reminders to ${allReminders.length} users`,
      category: 'email',
      level: 'info',
      data: {totalReminders: allReminders.length},
    });

    // Send reminder emails
    const emailPromises = allReminders.map(async ({docSnap, type}) => {
      const user = docSnap.data();

      Sentry.addBreadcrumb({
        message: `Sending ${type} profile completion reminder`,
        category: 'email',
        level: 'info',
        data: {userId: docSnap.id, email: user.email, reminderType: type},
      });

      await sendEmail(
        user.email,
        new CompleteProfileReminderEmailTemplate({
          displayName: user.name || 'User',
          uid: docSnap.id,
          reminderType: type,
        }),
      ).catch(console.error);

      console.log(`Sent ${type} reminder to ${docSnap.id} (${user.email}).`);

      // Update user fields in Firestore
      await docSnap.ref.update({
        lastCompleteProfileReminderTs: now,
        numCompleteProfileRemindersSent: FieldValue.increment(1),
      });

      Sentry.addBreadcrumb({
        message: `Updated reminder count for user`,
        category: 'firestore',
        level: 'info',
        data: {userId: docSnap.id, reminderType: type},
      });
    });

    await Promise.all(emailPromises);

    console.log('Profile completion reminders sent successfully.');

    Sentry.addBreadcrumb({
      message: 'Profile completion reminders sent successfully',
      category: 'scheduler',
      level: 'info',
      data: {totalSent: allReminders.length},
    });
  } catch (error) {
    console.error('Error sending profile completion reminders:', error);

    Sentry.addBreadcrumb({
      message: 'Error sending profile completion reminders',
      category: 'scheduler',
      level: 'error',
      data: {error: error},
    });
  }
});

export const cleanUpRejectedUsers = onSchedule('every day 09:00', async () => {
  initSentryForFunction('cleanUpRejectedUsers');

  Sentry.addBreadcrumb({
    message: 'Starting cleanup of rejected users',
    category: 'scheduler',
    level: 'info',
    data: {cooldownDays: REAPPLICATION_COOLDOWN_DAYS},
  });

  console.log(`Fetching rejected users with accounts >= ${REAPPLICATION_COOLDOWN_DAYS} days old...`);

  const now = new Date();
  const deleteUsersCreatedBefore = new Date(now);
  deleteUsersCreatedBefore.setDate(now.getDate() - REAPPLICATION_COOLDOWN_DAYS);

  const usersToDelete = await admin.firestore().collection(User.collectionName).where('applicationStatus', '==', 'rejected').where('dateJoined', '<=', Timestamp.fromDate(deleteUsersCreatedBefore)).get();
  console.log(`Found ${usersToDelete.size} user docs to delete: ${usersToDelete.docs.map((u) => u.id)}`);

  Sentry.addBreadcrumb({
    message: `Found ${usersToDelete.size} rejected users to delete`,
    category: 'cleanup',
    level: 'info',
    data: {
      userCount: usersToDelete.size,
      userIds: usersToDelete.docs.map((u) => u.id),
      cutoffDate: deleteUsersCreatedBefore.toISOString(),
    },
  });

  const deletePromises: Promise<any>[] = [];

  for (const doc of usersToDelete.docs) {
    console.log(`Deleting ${doc.id}...`);

    Sentry.addBreadcrumb({
      message: `Deleting rejected user`,
      category: 'cleanup',
      level: 'info',
      data: {userId: doc.id},
    });

    deletePromises.push(doc.ref.delete().catch((e) => console.error('Failed to delete doc:', e)));
  }

  await Promise.all(deletePromises);

  Sentry.addBreadcrumb({
    message: 'Rejected users cleanup completed',
    category: 'scheduler',
    level: 'info',
    data: {deletedCount: usersToDelete.size},
  });
});

export const notifyCCAdminsOfPendingUsers = onSchedule('every day 18:00', async () => {
  initSentryForFunction('notifyCCAdminsOfPendingUsers');

  Sentry.addBreadcrumb({
    message: 'Starting notification of pending applications to CC admins',
    category: 'scheduler',
    level: 'info',
  });

  console.log(`Fetching pending applications...`);

  const snapshot = await admin.firestore().collection(User.collectionName).where('applicationStatus', '==', 'submitted').count().get();
  const numPendingApplications = snapshot.data().count;

  console.log(`Found ${numPendingApplications} pending applications.`);

  Sentry.addBreadcrumb({
    message: `Found ${numPendingApplications} pending applications`,
    category: 'firestore',
    level: 'info',
    data: {numPendingApplications},
  });

  if (numPendingApplications > 0) {
    console.log(`Emailing Creator Campus admins: ${CREATOR_CAMPUS_ADMINS}`);

    Sentry.addBreadcrumb({
      message: `Sending pending applications notification to ${CREATOR_CAMPUS_ADMINS.length} admins`,
      category: 'email',
      level: 'info',
      data: {
        numPendingApplications,
        adminEmails: CREATOR_CAMPUS_ADMINS,
        adminCount: CREATOR_CAMPUS_ADMINS.length,
      },
    });

    for (const email of CREATOR_CAMPUS_ADMINS) {
      await sendEmail(email, new PendingMembershipApplicationsEmailTemplate({numPendingApplications}));
    }
  } else {
    Sentry.addBreadcrumb({
      message: 'No pending applications found, skipping admin notification',
      category: 'scheduler',
      level: 'info',
    });
  }
});

interface CreateUserPayload {
  userType: 'production' | 'demo';
  email: string;
  password: string;
  universityId: string;
  firstName: string;
  lastName: string;
  willBeOwner: boolean;
}

export const createUser = onCall<CreateUserPayload, Promise<string | null>>({cors: true, secrets: ['DEMO_FIREBASE_PROJECT_CLIENT_EMAIL', 'DEMO_FIREBASE_PROJECT_PRIVATE_KEY']}, async (request) => {
  initSentryForFunction('createUser');

  Sentry.addBreadcrumb({
    message: 'CreateUser function called',
    category: 'function',
    level: 'info',
  });

  const requestAuth = request.auth?.token;

  if (!requestAuth?.email || !requestAuth?.email_verified || !CREATOR_CAMPUS_ADMINS.includes(requestAuth.email)) {
    console.log(`Unauthorized email: ${requestAuth?.email}`);

    Sentry.addBreadcrumb({
      message: 'Unauthorized createUser request',
      category: 'auth',
      level: 'warning',
      data: {email: requestAuth?.email, emailVerified: requestAuth?.email_verified},
    });

    return 'Unauthorized.';
  }

  const data = request.data;
  console.log('Received data:', data);

  Sentry.addBreadcrumb({
    message: 'Creating user with provided data',
    category: 'user',
    level: 'info',
    data: {
      userType: data.userType,
      email: data.email,
      universityId: data.universityId,
      firstName: data.firstName,
      lastName: data.lastName,
      willBeOwner: data.willBeOwner,
    },
  });

  const demoApp =
    admin.apps.find((app) => app?.name === 'demo') ||
    admin.initializeApp(
      {
        projectId: 'creator-campus-demo',
        credential: admin.credential.cert({
          projectId: 'creator-campus-demo',
          clientEmail: String(process.env.DEMO_FIREBASE_PROJECT_CLIENT_EMAIL),
          privateKey: String(process.env.DEMO_FIREBASE_PROJECT_PRIVATE_KEY).replace(/\\n/g, '\n'),
        }),
      },
      'demo',
    );

  console.log('Creating user...');

  Sentry.addBreadcrumb({
    message: 'Creating Firebase Auth user',
    category: 'auth',
    level: 'info',
    data: {userType: data.userType, email: data.email},
  });

  const firebaseAuth = data.userType === 'demo' ? admin.auth(demoApp) : admin.auth();
  const authUser = await firebaseAuth
    .createUser({
      email: data.email,
      password: data.password,
      emailVerified: true,
    })
    .catch((e) => {
      Sentry.addBreadcrumb({
        message: 'Error creating Firebase Auth user',
        category: 'auth',
        level: 'error',
        data: {error: e.message},
      });
      return `${e}`;
    });

  if (typeof authUser === 'string') {
    // Error creating auth user
    return authUser;
  }

  console.log(`Created auth user ${authUser.uid}`);

  Sentry.addBreadcrumb({
    message: 'Successfully created Firebase Auth user',
    category: 'auth',
    level: 'info',
    data: {uid: authUser.uid, email: data.email},
  });

  const user = User.defaults(authUser.uid, data.email, data.universityId).copyWith({name: `${data.firstName} ${data.lastName}`, staffRole: data.willBeOwner ? StaffRole.OWNER : undefined, applicationStatus: data.willBeOwner ? 'accepted' : undefined});
  const db = data.userType === 'demo' ? admin.firestore(demoApp) : admin.firestore();

  console.log('Adding user doc...');

  Sentry.addBreadcrumb({
    message: 'Creating Firestore user document',
    category: 'firestore',
    level: 'info',
    data: {
      uid: user.id,
      universityId: user.universityId,
      staffRole: user.staffRole?.id,
      applicationStatus: user.applicationStatus,
    },
  });

  const userData: UserDbModelSafe = {
    applicationStatus: user.applicationStatus,
    avatarUpdatedAt: Timestamp.fromDate(user.avatarUpdatedAt),
    bio: user.bio,
    cvUploaded: user.cvUploaded,
    dateJoined: Timestamp.fromDate(user.dateJoined),
    email: user.email,
    emailVerified: true,
    fake: false,
    featuredUntil: null,
    founder: user.founder,
    githubUsername: user.githubUsername,
    hasUnreadOppApplications: user.hasUnreadOppApplications,
    hideProfile: user.hideProfile,
    karma: user.karma,
    lastOnline: Timestamp.fromDate(user.lastOnline),
    lastViewedDiscussion: Timestamp.fromDate(user.lastViewedDiscussion),
    linkedinUsername: user.linkedinUsername,
    name: user.name,
    notifications: user.notifications,
    numCompleteProfileRemindersSent: user.numCompleteProfileRemindersSent,
    openToWork: user.openToWork,
    persona: user.persona?.id || null,
    profileCompleted: user.profileCompleted,
    projectIds: user.projectIds,
    role: user.role?.id || null,
    score: user.score,
    services: user.services.map((s) => s.id),
    staffRole: user.staffRole?.id || null,
    universityEmail: user.universityEmail,
    universityId: user.universityId,
    website: user.website,
  };

  const writeResult = await db
    .collection(User.collectionName)
    .doc(user.id)
    .set(userData)
    .catch((e) => {
      Sentry.addBreadcrumb({
        message: 'Error writing user document to Firestore',
        category: 'firestore',
        level: 'error',
        data: {uid: user.id, error: e.message},
      });
      return `${e}`;
    });

  if (typeof writeResult === 'string') {
    // Error writing to Firestore
    return writeResult;
  }

  Sentry.addBreadcrumb({
    message: 'Successfully created Firestore user document',
    category: 'firestore',
    level: 'info',
    data: {uid: user.id},
  });

  console.log('Adding private doc to user...');

  Sentry.addBreadcrumb({
    message: 'Creating user private membership application document',
    category: 'firestore',
    level: 'info',
    data: {uid: user.id},
  });

  const applicationData: MembershipApplicationDbModel = {
    whyJoin: '',
    graduationYear: null,
    projectId: null,
    opportunityId: null,
  };

  await admin.firestore().collection(User.collectionName).doc(user.id).collection('private').doc(MembershipApplication.docName).set(applicationData);

  Sentry.addBreadcrumb({
    message: 'User creation completed successfully',
    category: 'function',
    level: 'info',
    data: {uid: user.id, email: user.email, universityId: user.universityId},
  });

  console.log('Done!');
  return null;
});

async function fetchInactiveUsers(daysAgo: number) {
  const now = new Date();
  const sixMonthsAgo = new Date(now);
  sixMonthsAgo.setDate(now.getDate() - daysAgo);

  Sentry.addBreadcrumb({
    message: `Fetching users inactive for over ${daysAgo} days`,
    category: 'firestore',
    level: 'info',
    data: {daysAgo, cutoffDate: sixMonthsAgo.toISOString()},
  });

  console.log(`Fetching users who have been inactive for over ${daysAgo} days...`);
  const snapshot = await admin.firestore().collection(User.collectionName).where('lastOnline', '<', sixMonthsAgo).get();
  console.log(`Found ${snapshot.size} users.`);

  Sentry.addBreadcrumb({
    message: `Found ${snapshot.size} inactive users`,
    category: 'firestore',
    level: 'info',
    data: {daysAgo, userCount: snapshot.size},
  });

  return snapshot.docs;
}

export const sendInactiveProfileReminders = onSchedule(
  {
    schedule: 'every day 18:00',
  },
  async () => {
    initSentryForFunction('sendInactiveProfileReminders');

    Sentry.addBreadcrumb({
      message: 'Starting inactive profile reminders job',
      category: 'scheduler',
      level: 'info',
    });

    // Fetch users who haven't been online for over 5 months
    const inactiveUsers = await fetchInactiveUsers(30 * 5);

    Sentry.addBreadcrumb({
      message: `Sending inactive profile reminders to ${inactiveUsers.length} users`,
      category: 'email',
      level: 'info',
      data: {userCount: inactiveUsers.length},
    });

    // Send reminder email to each user
    const promises = [];
    for (const user of inactiveUsers.map((d) => d.data() as UserDbModelSafe)) {
      Sentry.addBreadcrumb({
        message: 'Sending inactive profile reminder',
        category: 'email',
        level: 'info',
        data: {email: user.email, name: user.name},
      });

      promises.push(
        sendEmail(
          user.email,
          new InactiveProfileReminderEmailTemplate({
            username: user.name,
          }),
        ).catch(console.error),
      );
    }

    console.log(`Emailing ${inactiveUsers.length} users inactive profile reminders...`);
    await Promise.all(promises);
    console.log('Done!');

    Sentry.addBreadcrumb({
      message: 'Inactive profile reminders completed',
      category: 'scheduler',
      level: 'info',
      data: {emailsSent: inactiveUsers.length},
    });
  },
);

export const hideInactiveProfiles = onSchedule(
  {
    schedule: 'every day 18:00',
  },
  async () => {
    initSentryForFunction('hideInactiveProfiles');

    Sentry.addBreadcrumb({
      message: 'Starting hide inactive profiles job',
      category: 'scheduler',
      level: 'info',
    });

    // Fetch users who haven't been online for over 6 months
    const inactiveUsers = await fetchInactiveUsers(30 * 6);

    Sentry.addBreadcrumb({
      message: `Hiding ${inactiveUsers.length} inactive users and their projects`,
      category: 'cleanup',
      level: 'info',
      data: {userCount: inactiveUsers.length},
    });

    // Hide profile of inactive users
    const promises = [];
    for (const user of inactiveUsers) {
      Sentry.addBreadcrumb({
        message: 'Hiding inactive user profile',
        category: 'firestore',
        level: 'info',
        data: {userId: user.id},
      });

      promises.push(user.ref.update({hideProfile: true} as Partial<UserDbModelSafe>));
    }

    // Hide associated projects
    let totalProjectsToHide = 0;
    for (const user of inactiveUsers) {
      const userDoc = user.data() as UserDbModelSafe;
      totalProjectsToHide += userDoc.projectIds.length;

      for (const projectId of userDoc.projectIds) {
        Sentry.addBreadcrumb({
          message: 'Hiding project of inactive user',
          category: 'firestore',
          level: 'info',
          data: {userId: user.id, projectId},
        });
      }

      promises.push(...userDoc.projectIds.map((projectId) => admin.firestore().collection(Project.collectionName).doc(projectId).update({hidden: true})));
    }

    console.log(`Hiding ${inactiveUsers.length} inactive users...`);
    await Promise.all(promises);
    console.log('Done!');

    Sentry.addBreadcrumb({
      message: 'Hide inactive profiles completed',
      category: 'scheduler',
      level: 'info',
      data: {
        usersHidden: inactiveUsers.length,
        projectsHidden: totalProjectsToHide,
      },
    });
  },
);
