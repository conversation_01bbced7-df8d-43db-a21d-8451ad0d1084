import {onDocumentCreated, onDocumentDeleted, onDocumentUpdated} from 'firebase-functions/v2/firestore';
import {FieldValue, Transaction} from 'firebase-admin/firestore';
import {getMetricsCollection, initSentryForFunction} from './utils';
import * as admin from 'firebase-admin';
import {getInfluxWriter, newPoint} from './influxdb';
import {Opportunity, OpportunityApplication, OpportunityDbModelSafe, Project, User, UserDbModelSafe} from '@creator-campus/common';
import * as Sentry from '@sentry/node';

export const onOpportunityCreated = onDocumentCreated(
  {
    document: 'opportunities/{oppId}',
    secrets: ['REACT_APP_INFLUX_DB_TOKEN'],
  },
  async (event) => {
    initSentryForFunction('onOpportunityCreated');

    if (!event.data) {
      console.error('Event data is undefined!');
      Sentry.addBreadcrumb({
        message: 'Event data is undefined in onOpportunityCreated',
        category: 'function',
        level: 'error'
      });
      return;
    }

    const data = event.data.data() as OpportunityDbModelSafe;
    const oppId = event.params.oppId;
    console.log(`Detected opportunity ${oppId} created (project ${data.projectId}).`);

    Sentry.addBreadcrumb({
      message: `Opportunity created: ${oppId}`,
      category: 'opportunity',
      level: 'info',
      data: {
        oppId,
        projectId: data.projectId,
        universityId: data.universityId,
        title: data.title,
        compensation: data.compensation,
        location: data.location
      }
    });

    // Update project fields
    Sentry.addBreadcrumb({
      message: 'Syncing opportunities to parent project',
      category: 'opportunity',
      level: 'info',
      data: { oppId, projectId: data.projectId }
    });

    await syncOpportunitiesToParentProject(data.projectId);

    // Update metrics
    Sentry.addBreadcrumb({
      message: 'Updating university metrics for opportunity creation',
      category: 'metrics',
      level: 'info',
      data: { oppId, universityId: data.universityId }
    });

    await getMetricsCollection(data.universityId).update({
      opportunitiesCreated: FieldValue.increment(1),
    });

    Sentry.addBreadcrumb({
      message: 'Writing opportunity creation metric to InfluxDB',
      category: 'metrics',
      level: 'info',
      data: { oppId, universityId: data.universityId }
    });

    const influxToken = String(process.env.REACT_APP_INFLUX_DB_TOKEN);
    const influxWriter = getInfluxWriter(influxToken);

    const point = newPoint().intField('opportunitiesCreated', 1).tag('university', data.universityId);
    influxWriter.writePoint(point);
  },
);

async function syncOpportunitiesToParentProject(projectId: string) {
  Sentry.addBreadcrumb({
    message: 'Starting sync of opportunities to parent project',
    category: 'opportunity',
    level: 'info',
    data: { projectId }
  });

  const opportunitySnapshot = await admin.firestore().collection(Opportunity.collectionName).where('projectId', '==', projectId).get();
  const opportunities = opportunitySnapshot.docs.map((d) => d.data() as OpportunityDbModelSafe);
  const numOpenOpportunities = opportunities.filter((o) => o.fillDate === null).length;
  const opportunityTitles = Array.from(new Set(opportunities.map((o) => o.title)));
  const opportunityDescriptions = Array.from(new Set(opportunities.map((o) => o.description)));
  const compensations = Array.from(new Set(opportunities.map((o) => o.compensation).flat()));
  const locations = Array.from(new Set(opportunities.map((o) => o.location)));

  Sentry.addBreadcrumb({
    message: 'Calculated project opportunity aggregates',
    category: 'opportunity',
    level: 'info',
    data: {
      projectId,
      totalOpportunities: opportunities.length,
      numOpenOpportunities,
      uniqueTitles: opportunityTitles.length,
      uniqueDescriptions: opportunityDescriptions.length,
      uniqueCompensations: compensations.length,
      uniqueLocations: locations.length
    }
  });

  await admin.firestore().collection(Project.collectionName).doc(projectId).update({
    numOpenOpportunities,
    opportunityTitles,
    opportunityDescriptions,
    compensations,
    locations,
  });

  Sentry.addBreadcrumb({
    message: 'Successfully synced opportunities to parent project',
    category: 'firestore',
    level: 'info',
    data: { projectId, numOpenOpportunities }
  });
}

export const onOpportunityUpdated = onDocumentUpdated(
  {
    document: 'opportunities/{oppId}',
    secrets: ['REACT_APP_INFLUX_DB_TOKEN'],
  },
  async (event) => {
    initSentryForFunction('onOpportunityUpdated');

    if (!event.data) {
      console.error('Event data is undefined!');
      Sentry.addBreadcrumb({
        message: 'Event data is undefined in onOpportunityUpdated',
        category: 'function',
        level: 'error'
      });
      return;
    }

    const beforeData = event.data.before.data() as OpportunityDbModelSafe;
    const afterData = event.data.after.data() as OpportunityDbModelSafe;
    const oppId = event.params.oppId;
    console.log(`Detected opportunity ${oppId} updated (project ${afterData.projectId}).`);

    Sentry.addBreadcrumb({
      message: `Opportunity updated: ${oppId}`,
      category: 'opportunity',
      level: 'info',
      data: {
        oppId,
        projectId: afterData.projectId,
        titleChanged: beforeData.title !== afterData.title,
        descriptionChanged: beforeData.description !== afterData.description,
        fillDateChanged: beforeData.fillDate !== afterData.fillDate,
        wasFilled: beforeData.fillDate === null && afterData.fillDate !== null
      }
    });

    // Check if the title or description changed (and we have to update them in the project doc)
    if (beforeData.title !== afterData.title || beforeData.description !== afterData.description) {
      Sentry.addBreadcrumb({
        message: 'Opportunity title or description changed, updating project',
        category: 'opportunity',
        level: 'info',
        data: {
          oppId,
          projectId: afterData.projectId,
          oldTitle: beforeData.title,
          newTitle: afterData.title,
          titleChanged: beforeData.title !== afterData.title,
          descriptionChanged: beforeData.description !== afterData.description
        }
      });

      await admin
        .firestore()
        .collection(Project.collectionName)
        .doc(afterData.projectId)
        .update({
          opportunityTitles: FieldValue.arrayRemove(beforeData.title),
          opportunityDescriptions: FieldValue.arrayRemove(beforeData.description),
        });

      await admin
        .firestore()
        .collection(Project.collectionName)
        .doc(afterData.projectId)
        .update({
          opportunityTitles: FieldValue.arrayUnion(afterData.title),
          opportunityDescriptions: FieldValue.arrayUnion(afterData.description),
        });

      Sentry.addBreadcrumb({
        message: 'Updated project with new opportunity title/description',
        category: 'firestore',
        level: 'info',
        data: { oppId, projectId: afterData.projectId }
      });
    }

    // Check if opportunity just got filled
    if (beforeData.fillDate === null && afterData.fillDate !== null) {
      console.log(`Opportunity just got filled. Updating related project: ${afterData.projectId}`);

      Sentry.addBreadcrumb({
        message: 'Opportunity just got filled',
        category: 'opportunity',
        level: 'info',
        data: {
          oppId,
          projectId: afterData.projectId,
          acceptedApplicantId: afterData.acceptedApplicantId,
          universityId: afterData.universityId
        }
      });

      // Reduce project's numOpenOpportunities
      await admin
        .firestore()
        .collection(Project.collectionName)
        .doc(afterData.projectId)
        .update({
          numOpenOpportunities: FieldValue.increment(-1),
        });

      Sentry.addBreadcrumb({
        message: 'Decremented project numOpenOpportunities',
        category: 'firestore',
        level: 'info',
        data: { oppId, projectId: afterData.projectId }
      });

      console.log('Updating metrics...');

      // Get applicant's university and update their university's metrics too
      const applicantSnapshot = await admin.firestore().collection(User.collectionName).doc(afterData.acceptedApplicantId!).get();
      const applicant = applicantSnapshot.data() as UserDbModelSafe;

      Sentry.addBreadcrumb({
        message: 'Updating metrics for filled opportunity',
        category: 'metrics',
        level: 'info',
        data: {
          oppId,
          applicantId: afterData.acceptedApplicantId,
          applicantUniversity: applicant.universityId,
          opportunityUniversity: afterData.universityId
        }
      });

      await getMetricsCollection(applicant.universityId).update({
        outgoingOppApplicationsAccepted: FieldValue.increment(1),
        unisWhoseOppsWeFill: {
          [afterData.universityId]: FieldValue.increment(1),
        },
      });

      const hoursSpentOpen = (new Date().getTime() - afterData.datePosted.toDate().getTime()) / 1000 / 60 / 60;
      await getMetricsCollection(afterData.universityId).update({
        hoursOppsSpentOpen: FieldValue.increment(hoursSpentOpen),
        unisFillingOurOpps: {
          [applicant.universityId]: FieldValue.increment(1),
        },
      });

      Sentry.addBreadcrumb({
        message: 'Writing opportunity filled metrics to InfluxDB',
        category: 'metrics',
        level: 'info',
        data: {
          oppId,
          hoursSpentOpen,
          applicantUniversity: applicant.universityId,
          opportunityUniversity: afterData.universityId
        }
      });

      const influxToken = String(process.env.REACT_APP_INFLUX_DB_TOKEN);
      const influxWriter = getInfluxWriter(influxToken);

      let point = newPoint().intField('oppApplicationsAccepted', 1).tag('university', afterData.universityId).tag('applicantUniversity', applicant.universityId);
      influxWriter.writePoint(point);

      point = newPoint().floatField('hoursOppsSpentOpen', hoursSpentOpen).tag('university', afterData.universityId);
      influxWriter.writePoint(point);
    }
  },
);

export const onOpportunityDeleted = onDocumentDeleted('opportunities/{oppId}', async (event) => {
  initSentryForFunction('onOpportunityDeleted');

  if (!event.data) {
    console.error('Event data is undefined!');
    Sentry.addBreadcrumb({
      message: 'Event data is undefined in onOpportunityDeleted',
      category: 'function',
      level: 'error'
    });
    return;
  }

  const data = event.data.data();
  const oppId = event.params.oppId;
  console.log(`Detected opportunity ${oppId} deleted (project ${data.projectId}).`);

  Sentry.addBreadcrumb({
    message: `Opportunity deleted: ${oppId}`,
    category: 'opportunity',
    level: 'info',
    data: {
      oppId,
      projectId: data.projectId,
      title: data.title,
      universityId: data.universityId
    }
  });

  const projectRef = admin.firestore().collection(Project.collectionName).doc(data.projectId);
  const project = await projectRef.get();

  if (project.exists) {
    console.log(`Syncing to parent project ${data.projectId}...`);

    Sentry.addBreadcrumb({
      message: 'Syncing opportunities to parent project after deletion',
      category: 'opportunity',
      level: 'info',
      data: { oppId, projectId: data.projectId }
    });

    await syncOpportunitiesToParentProject(data.projectId);
  } else {
    console.log('No project to update as it no longer exists (this is expected behaviour if this Function was triggered by a project deletion).');

    Sentry.addBreadcrumb({
      message: 'Parent project no longer exists (expected if triggered by project deletion)',
      category: 'opportunity',
      level: 'info',
      data: { oppId, projectId: data.projectId }
    });
  }

  // Delete related opportunity applications
  const applicationsSnapshot = await admin.firestore().collection(OpportunityApplication.collectionName).where('opportunityId', '==', oppId).get();
  console.log(`Deleting ${applicationsSnapshot.size} related opportunity applications...`);

  Sentry.addBreadcrumb({
    message: `Deleting ${applicationsSnapshot.size} related opportunity applications`,
    category: 'cleanup',
    level: 'info',
    data: { oppId, applicationCount: applicationsSnapshot.size }
  });

  for (const application of applicationsSnapshot.docs) {
    Sentry.addBreadcrumb({
      message: 'Deleting related opportunity application',
      category: 'cleanup',
      level: 'info',
      data: { oppId, applicationId: application.id }
    });
    await application.ref.delete();
  }

  Sentry.addBreadcrumb({
    message: 'Opportunity deletion cleanup completed',
    category: 'cleanup',
    level: 'info',
    data: { oppId, deletedApplications: applicationsSnapshot.size }
  });
});
