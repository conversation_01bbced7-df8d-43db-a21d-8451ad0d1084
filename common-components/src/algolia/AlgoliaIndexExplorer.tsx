import {AlgoliaSearchBar} from './AlgoliaSearchBar';

import {Configure, InstantSearch} from 'react-instantsearch';

import {Box, Card} from '@mui/joy';
import {AlgoliaHits} from './AlgoliaHits';
import {history} from 'instantsearch.js/es/lib/routers';
import {ReactElement, useState} from 'react';
import * as _ from 'lodash';
import {Clear} from '@mui/icons-material';
import Button from '@mui/joy/Button';
import Typography from '@mui/joy/Typography';
import {algolia} from '@creator-campus/common';
import {AlgoliaFilters, AlgoliaFiltersSheet, UpdateAlgoliaFilters} from './AlgoliaFiltersSheet';

interface Props {
  index: string;
  // Fields used as filters must be listed in creator-campus/common -> algolia.ts and the production index on the Algolia dashboard (configuration -> facets)
  initialFilters?: AlgoliaFilters;
  itemBuilder: (hit: any, i: number, filters: AlgoliaFilters) => ReactElement;
  filterPanel?: (filters: AlgoliaFilters, updateFilters: UpdateAlgoliaFilters) => ReactElement;
  buttonFilters?: (filters: AlgoliaFilters, updateFilters: UpdateAlgoliaFilters) => ReactElement;
  placeholderPrefix?: string;
  searchPlaceholders?: string[];
  placeholderOffset?: number;
  endOfResultsMessage?: string;
  contentOverlay?: (filters: AlgoliaFilters, content: ReactElement) => ReactElement | null | undefined;
}

export function AlgoliaIndexExplorer({index, initialFilters = {}, itemBuilder, filterPanel, buttonFilters, placeholderPrefix, searchPlaceholders, placeholderOffset, endOfResultsMessage, contentOverlay}: Props) {
  const RIGHT_COL_WIDTH = 275;

  const [filters, setFilters] = useState<AlgoliaFilters>(initialFilters);

  function updateFilters(key: string, value: string | string[] | null) {
    setFilters((prev) => {
      const newFilters = {...prev};
      if (value === null) {
        delete newFilters[key]; // Remove filter when toggled off
      } else {
        newFilters[key] = value;
      }
      return newFilters;
    });
  }

  function escapeSpecialChars(str: string) {
    return str.replace(/"/g, '\\"');
  }

  function filtersAsString() {
    if (!filters) {
      return undefined;
    }

    return Object.entries(filters)
      .map(([key, filter]) => {
        if (typeof filter === 'string') {
          return `${key}${filter}`;
        }

        return `(${filter.map((f) => `${key}:"${escapeSpecialChars(f)}"`).join(' OR ')})`;
      })
      .join(' AND ');
  }

  function clearFilters() {
    setFilters(initialFilters);
  }

  const routing = {
    router: history({
      cleanUrlOnDispose: false,
    }),
  };

  const filtering = !_.isEqual(filters, initialFilters);

  function FiltersCard({filterPanel}: {filterPanel: (filters: AlgoliaFilters, updateFilters: UpdateAlgoliaFilters) => ReactElement}) {
    return (
      <Box sx={{position: 'sticky', top: 0}}>
        <Card>{filterPanel(filters, updateFilters)}</Card>
        {filtering && (
          <Button
            color={'neutral'}
            variant={'outlined'}
            onClick={clearFilters}
            sx={{mt: 1}}
          >
            <Clear sx={{height: 20}} />
            <Typography
              level={'body-sm'}
              sx={{ml: 1}}
            >
              Clear filters
            </Typography>
          </Button>
        )}
      </Box>
    );
  }

  return (
    <InstantSearch
      indexName={algolia!.indexConfigs[index].name!}
      searchClient={algolia!.client}
      future={{preserveSharedStateOnUnmount: true}}
      routing={routing}
    >
      <AlgoliaSearchBar
        placeholderPrefix={placeholderPrefix}
        searchPlaceholders={searchPlaceholders}
        placeholderOffset={placeholderOffset}
      />
      {filterPanel && (
        <Box
          sx={{mt: 2}}
          display={{xs: 'block', sm: 'block', lg: 'none'}}
        >
          <AlgoliaFiltersSheet
            filterPanel={filterPanel(filters, updateFilters)}
            filtering={filtering}
            clearFilters={clearFilters}
          />
        </Box>
      )}

      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: 2,
        }}
      >
        {/* Top Row */}
        <Box sx={{display: 'flex', gap: 2}}>
          {/* Top Left */}
          <Box sx={{flex: 1}}>{buttonFilters && buttonFilters(filters, updateFilters)}</Box>

          {/* Top Right */}
          <Box
            sx={{
              width: RIGHT_COL_WIDTH,
              display: {xs: 'none', sm: 'none', lg: 'block'},
            }}
          />
        </Box>

        {/* Bottom Row */}
        <Box sx={{display: 'flex', gap: 2}}>
          {/* Bottom Left */}
          <Box sx={{flex: 1, position: 'relative'}}>
            {contentOverlay ? (
              contentOverlay(
                filters,
                <AlgoliaHits
                  itemBuilder={itemBuilder}
                  filters={filters}
                  filtering={filtering}
                  endOfResultsMessage={endOfResultsMessage}
                />,
              ) || (
                <AlgoliaHits
                  itemBuilder={itemBuilder}
                  filters={filters}
                  filtering={filtering}
                  endOfResultsMessage={endOfResultsMessage}
                />
              )
            ) : (
              <AlgoliaHits
                itemBuilder={itemBuilder}
                filters={filters}
                filtering={filtering}
                endOfResultsMessage={endOfResultsMessage}
              />
            )}
          </Box>

          {/* Bottom Right */}
          {filterPanel && (
            <Box
              sx={{
                width: RIGHT_COL_WIDTH,
                display: {xs: 'none', sm: 'none', lg: 'block'},
              }}
            >
              <FiltersCard filterPanel={filterPanel} />
            </Box>
          )}
        </Box>
      </Box>
      <Configure filters={filtersAsString()} />
    </InstantSearch>
  );
}
