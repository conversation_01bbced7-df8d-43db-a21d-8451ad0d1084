import {Box, Button, ButtonGroup, Typography} from '@mui/joy';
import {useNavigate} from 'react-router-dom';
import {useProfileOnboardingString, ModalShell, ModalHeader} from '@creator-campus/common-components';

interface Props {
  onClose: () => void;
}

export default function ProfileCompleteModal({onClose}: Props) {
  const navigate = useNavigate();

  const image = (
    <img
      src='/undraw_profile.svg'
      alt={'Profile complete'}
      className='responsive-image'
      style={{
        objectFit: 'cover',
        width: '100%',
        height: '90%',
      }}
    />
  );

  const buttons = (
    <ButtonGroup>
      <Button
        onClick={() => {
          onClose();
          navigate('/startups/my-startups');
        }}
        sx={{mt: 2}}
      >
        {useProfileOnboardingString().create_project_button}
      </Button>
      <Button
        onClick={() => {
          onClose();
          navigate('/startups/explore');
        }}
        sx={{mt: 2}}
      >
        {useProfileOnboardingString().browse_project_button}
      </Button>
    </ButtonGroup>
  );

  return (
    <ModalShell
      onClose={onClose}
      closeOnBackgroundClick={true}
      withCloseButton={true}
    >
      <Box sx={{display: {xs: 'none', md: 'flex'}, justifyContent: 'center', alignItems: 'center'}}>
        <Box
          sx={{
            maxWidth: 500,
            minWidth: 300,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            marginRight: 2,
          }}
        >
          {image}
        </Box>
        <Box sx={{display: 'flex', flexDirection: 'column'}}>
          <ModalHeader title={useProfileOnboardingString().profile_created} />
          <Typography
            id='modal-desc'
            textColor='text.tertiary'
          >
            {useProfileOnboardingString().profile_created_subtitle}
          </Typography>
          {buttons}
        </Box>
      </Box>
      <Box sx={{display: {xs: 'flex', md: 'none'}, flexDirection: 'column', justifyContent: 'center', alignItems: 'center'}}>
        <ModalHeader title={useProfileOnboardingString().profile_created} />
        <Box
          sx={{
            maxWidth: 200,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          {image}
        </Box>
        <Typography textColor='text.tertiary'>{useProfileOnboardingString().profile_created_subtitle}</Typography>
        {buttons}
      </Box>
    </ModalShell>
  );
}
