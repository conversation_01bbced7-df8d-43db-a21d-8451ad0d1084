import {useState} from 'react';
import {<PERSON>, <PERSON><PERSON>, Chip, Stack, Typography} from '@mui/joy';
import {Delete, Edit, HowToReg, Login} from '@mui/icons-material';
import {useNavigate} from 'react-router-dom';
import OpportunityApplications from './OpportunityApplications';
import {Opportunity, Project} from '@creator-campus/common';
import {useAuth, useScreenWidth, useSnackbar, useUser} from '@creator-campus/common-components';
import IconButton from '@mui/joy/IconButton';
import {DeleteOpportunityDialog} from './modals/DeleteOpportunityDialog.tsx';
import {CreateEditOpportunityModal} from './modals/CreateEditOpportunityModal.tsx';
import {OpportunityApplicationModal} from './modals/OpportunityApplicationModal.tsx';
import Tooltip from '@mui/joy/Tooltip';
import {ReadMoreText} from './ReadMoreText.tsx';
import {OppCompensationChip} from './OppCompensationChip.tsx';
import {OppLocationChip} from './OppLocationChip.tsx';

interface Props {
  opp: Opportunity;
  project: Project;
  editMode: boolean;
  onOppChanged?: (event: 'edit' | 'remove') => void;
}

export default function OpportunityCard({opp, project, editMode, onOppChanged}: Props) {
  const [justApplied, setJustApplied] = useState<boolean>(false);
  const [applyModalOpen, setApplyModalOpen] = useState<boolean>(false);
  const [editModalOpen, setEditModalOpen] = useState<boolean>(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState<boolean>(false);

  const navigate = useNavigate();
  const screenWidth = useScreenWidth();
  const {currentUser} = useAuth();
  const {user} = useUser();
  const {showSnackbar} = useSnackbar();

  function Chips() {
    return (
      <Stack
        spacing={1}
        direction={'row'}
      >
        {opp.fillDate && (
          <Chip
            key='Filled'
            startDecorator={<HowToReg />}
            color='success'
            variant='soft'
          >
            Filled
          </Chip>
        )}
        <OppLocationChip location={opp.location} />
        {opp.compensation.map((comp) => (
          <OppCompensationChip
            key={comp.id}
            compensation={comp}
          />
        ))}
      </Stack>
    );
  }

  function ActionButtons() {
    if (!currentUser) {
      return (
        <Button
          onClick={() => navigate('/login')}
          size='sm'
          startDecorator={<Login />}
          sx={{minWidth: 100}}
        >
          Log in to apply
        </Button>
      );
    }

    if (editMode) {
      return (
        <Stack
          direction={'row'}
          spacing={1}
        >
          <IconButton
            size='sm'
            variant='soft'
            color='neutral'
            onClick={() => setEditModalOpen(true)}
          >
            <Edit />
          </IconButton>
          {editModalOpen && (
            <CreateEditOpportunityModal
              project={project}
              initialOpp={opp}
              onClose={(saved) => {
                setEditModalOpen(false);
                if (saved) {
                  showSnackbar('Opportunity saved.', 'success');
                }

                if (onOppChanged) {
                  onOppChanged('edit');
                }
              }}
            />
          )}
          <IconButton
            size='sm'
            variant='soft'
            color='danger'
            onClick={() => setDeleteModalOpen(true)}
          >
            <Delete />
          </IconButton>
          {deleteModalOpen && (
            <DeleteOpportunityDialog
              opportunity={opp}
              onClose={() => setDeleteModalOpen(false)}
              onOppRemoved={onOppChanged && (() => onOppChanged('remove'))}
            />
          )}
        </Stack>
      );
    }

    if (currentUser.uid === project.ownerId) {
      return (
        <Button
          sx={{minWidth: 90}}
          variant='outlined'
          color='primary'
          onClick={() => navigate('/startups/my-startups')}
        >
          Manage
        </Button>
      );
    }

    const userAlreadyApplied = justApplied || opp.applicantIds.includes(currentUser.uid);

    return (
      <Tooltip
        title={!user?.hasFullAccessToApp() ? 'Complete your profile to apply for opportunities.' : userAlreadyApplied ? 'You have already applied for this opportunity.' : ''}
        variant={'solid'}
      >
        <Box>
          <Button
            disabled={!user?.hasFullAccessToApp() || userAlreadyApplied}
            sx={{marginLeft: 'auto', minWidth: 80}}
            variant='outlined'
            color='primary'
            onClick={() => setApplyModalOpen(true)}
          >
            Apply
          </Button>
        </Box>
      </Tooltip>
    );
  }

  function Description() {
    return (
      <ReadMoreText
        text={opp.description}
        maxLines={Opportunity.MAX_DESC_LINES}
        onReadMore={() => {
          if (editMode) {
            setEditModalOpen(true);
          } else {
            setApplyModalOpen(true);
          }
        }}
      />
    );
  }

  return (
    <>
      {user && applyModalOpen && (
        <OpportunityApplicationModal
          opp={opp}
          project={project}
          applicant={user}
          onCancel={() => setApplyModalOpen(false)}
          onApply={() => {
            setJustApplied(true);
            setApplyModalOpen(false);
            showSnackbar('Application sent.', 'success');
          }}
        />
      )}

      {screenWidth < 500 || editMode ? (
        <Stack>
          <Stack
            direction={'row'}
            spacing={2}
            alignItems={'center'}
            justifyContent={'space-between'}
          >
            <Typography level='title-md'>{opp.title}</Typography>
            <ActionButtons />
          </Stack>
          <Chips />
          <Description />
          {editMode && user && (
            <OpportunityApplications
              opp={opp}
              user={user}
            />
          )}
        </Stack>
      ) : (
        <Stack
          direction={'row'}
          spacing={3}
          justifyContent={'space-between'}
          alignItems={'start'}
        >
          <Stack spacing={1}>
            <Stack
              direction={'row'}
              spacing={2}
            >
              <Typography level='title-md'>{opp.title}</Typography>
              <Chips />
            </Stack>
            <Description />
            {editMode && user && (
              <OpportunityApplications
                opp={opp}
                user={user}
              />
            )}
          </Stack>
          <Stack
            direction={'row'}
            spacing={0.5}
            alignSelf={editMode ? 'start' : 'center'}
          >
            <ActionButtons />
          </Stack>
        </Stack>
      )}
    </>
  );
}
