import {PartnershipData, SubscriptionTier} from '@creator-campus/common';
import {useUser, useScreenWidth} from '@creator-campus/common-components';
import {Check, Close} from '@mui/icons-material';
import {Box, Button, Card, Chip, List, ListItem, ListItemDecorator, Stack, Typography} from '@mui/joy';
import {useNavigate} from 'react-router-dom';

interface Props {
  tier: SubscriptionTier;
  partnershipData: PartnershipData;
}

export function SubscriptionTierCard({tier, partnershipData}: Props) {
  const {university} = useUser();
  const navigate = useNavigate();
  const screenWidth = useScreenWidth();

  if (!university) {
    return null;
  }

  const uniTier = university.partner || university.almostReachedUserLimit ? SubscriptionTier.PARTNER : SubscriptionTier.INACTIVE;
  const needsToPaySoon = university.almostReachedUserLimit && !university.isPaying();
  const needsToPayNow = university.reachedUserLimit && !university.isPaying();
  const actionButtonEnabled = tier === SubscriptionTier.PARTNER && (!university.partner || needsToPaySoon || needsToPayNow);

  return (
    <Card
      color={tier === uniTier ? 'primary' : 'neutral'}
      sx={{width: Math.min(screenWidth - 50, 350)}}
    >
      <Stack>
        {/* Title and current tier chip */}
        <Stack
          direction={'row'}
          spacing={1}
        >
          <Typography level={'h4'}>{tier.label}</Typography>
          {needsToPayNow ? (
            <Chip
              size={'sm'}
              color={'danger'}
            >
              Reached quota
            </Chip>
          ) : needsToPaySoon ? (
            <Chip
              size={'sm'}
              color={'warning'}
            >
              Approaching quota
            </Chip>
          ) : (
            tier === uniTier && (
              <Chip
                size={'sm'}
                color={'neutral'}
              >
                Current
              </Chip>
            )
          )}
        </Stack>

        {/* Description */}
        <Typography
          level={'body-sm'}
          mt={0.5}
        >
          {needsToPayNow
            ? `You have reached the ${partnershipData.userLimit} user limit for your community. Subscribe to retain partnership access.`
            : needsToPaySoon
              ? `You have almost reached the ${partnershipData.userLimit} user limit for your community. Subscribe to remove this limit and keep your partnership benefits.`
              : tier.description}
        </Typography>

        {tier === SubscriptionTier.PARTNER && (
          <>
            {/* Price */}
            <Box mt={3}>
              {needsToPaySoon ? (
                <Stack
                  direction={'row'}
                  spacing={1}
                  alignItems={'center'}
                >
                  <Typography level={'title-lg'}>{`£${(partnershipData.yearlyPriceGbp / 12).toFixed(2)} / month`}</Typography>
                  <Typography level={'body-sm'}>{`(if billed annually)`}</Typography>
                </Stack>
              ) : university.isPaying() ? (
                <Stack
                  direction={'row'}
                  spacing={1}
                  alignItems={'center'}
                >
                  <Typography level={'title-lg'}>{`£${(partnershipData.priceGbp!).toFixed(2)} / ${partnershipData.paymentInterval === 'monthly' ? 'month' : 'year'}`}</Typography>
                </Stack>
              ) : (
                <Stack
                  direction={'row'}
                  spacing={1}
                  alignItems={'center'}
                >
                  <Typography level={'title-lg'}>{'Free'}</Typography>
                  <Typography level={'body-sm'}>{`(up to ${partnershipData.userLimit} users)`}</Typography>
                </Stack>
              )}
            </Box>

            {/* Activate button */}
            <Button
              disabled={!actionButtonEnabled}
              sx={{width: '100%', mt: 4}}
              onClick={() => {
                navigate(`${location.pathname}?showModal=activateCommunity`);
              }}
            >
              {needsToPaySoon ? 'Subscribe to keep benefits' : tier === uniTier ? 'Current' : 'Activate'}
            </Button>

            {/* Benefits */}
            <Typography
              level={'title-sm'}
              pt={5}
            >
              Benefits
            </Typography>

            <List sx={{'--ListItem-paddingX': '0px', '--ListItemDecorator-size': '30px'}}>
              {tier.benefits.map((benefit) => (
                <ListItem key={benefit}>
                  <ListItemDecorator>
                    <Check />
                  </ListItemDecorator>
                  <Typography level={'body-sm'}>{benefit}</Typography>
                </ListItem>
              ))}

              {tier.missingBenefits.map((benefit) => (
                <ListItem key={benefit}>
                  <ListItemDecorator>
                    <Close />
                  </ListItemDecorator>
                  <Typography level={'body-sm'}>{benefit}</Typography>
                </ListItem>
              ))}
            </List>
          </>
        )}
      </Stack>
    </Card>
  );
}
