import {createContext, ReactNode, useContext, useEffect, useState} from 'react';
import {collection, getCountFromServer, onSnapshot, orderBy, query, where} from 'firebase/firestore';
import {useAuth} from './AuthProvider';
import {firestore, logger, MembershipApplication, University, User } from '@creator-campus/common';

interface UserContextType {
  loadingUser: boolean;
  user: User | null;
  profilePicUrl: string | null;
  university: University | null;
  universityLogoUrl: string | null;
  numUnreadDiscussionPosts: number | null;
  membershipApplication: MembershipApplication | null;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

function UserProvider({children}: {children: ReactNode}) {
  const [loadingUser, setLoadingUser] = useState<boolean>(true);
  const [user, setUser] = useState<User | null>(null);
  const [profilePicUrl, setProfilePicUrl] = useState<string | null>(null);
  const [university, setUniversity] = useState<University | null>(null);
  const [universityLogoUrl, setUniversityLogoUrl] = useState<string | null>(null);
  const [numUnreadDiscussionPosts, setNumUnreadDiscussionPosts] = useState<number | null>(null);
  const [membershipApplication, setMembershipApplication] = useState<MembershipApplication | null>(null);

  const {currentUser, loadingAuth} = useAuth();

  useEffect(() => {
    if (loadingAuth) {
      return;
    }

    if (!currentUser) {
      setUser(null);
      setProfilePicUrl(null);
      setLoadingUser(false);
      setMembershipApplication(null);
      return;
    }

    const unsub = onSnapshot(User.doc(currentUser.uid), (snapshot) => {
      const u = snapshot.data() || null;

      setUser(u);
      setLoadingUser(false);

      if (!u) {
        return;
      }

      if (u.email !== currentUser.email) {
        // Update email in user doc in case it has changed in Firebase Auth.
        // This can happen sometimes when they update their email via the functionality
        // on the profile page so this just corrects any inconsistencies between the emails.
        u.ensureSyncedWithAuthEmail(currentUser.email!).catch(logger.error);
      }

      u.getAvatarUrl()
        .then((url) => {
          setProfilePicUrl(url);
        })
        .catch((e) => {
          logger.error(e);
        });
    });

    return () => unsub();
  }, [currentUser, loadingAuth]);

  useEffect(() => {
    if (!user) {
      setUniversity(null);
      setUniversityLogoUrl(null);
      return;
    }

    const unsub = onSnapshot(University.doc(user.universityId), (docSnap) => {
      // This is null if the user's university isn't partnered with Creator Campus
      const updatedUniversity = docSnap.data() || null;

      if (updatedUniversity) {
        if (university?.lastDiscussionPost !== updatedUniversity?.lastDiscussionPost) {
          getCountFromServer(query(collection(firestore(), University.collectionName, user.universityId, 'discussion'), where('datePosted', '>', user.lastViewedDiscussion), orderBy('datePosted', 'asc'))).then((result) => {
            setNumUnreadDiscussionPosts(result.data().count);
          });
        }
      } else {
        logger.warn(`Could not find university ${user.universityId}`);
      }

      updatedUniversity?.getLogoUrl().then(setUniversityLogoUrl);
      setUniversity(updatedUniversity);
    });

    return () => unsub();
  }, [user]);

  useEffect(() => {
    if (!currentUser) {
      return;
    }

    const unsub = onSnapshot(MembershipApplication.doc(currentUser.uid), (snapshot) => {
      setMembershipApplication(snapshot.data() || null);
    });

    return () => unsub();
  }, [currentUser]);

  const value: UserContextType = {
    loadingUser,
    user,
    profilePicUrl,
    university,
    universityLogoUrl,
    numUnreadDiscussionPosts,
    membershipApplication,
  };

  return <UserContext.Provider value={value}>{children}</UserContext.Provider>;
}

const useUser = () => {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};

export {UserProvider, useUser};
