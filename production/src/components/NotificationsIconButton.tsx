import {Notifications} from '@mui/icons-material';
import IconButton from '@mui/joy/IconButton';
import {Box, Dropdown, Menu, MenuButton, Option} from '@mui/joy';
import Stack from '@mui/joy/Stack';
import Select from '@mui/joy/Select';
import {User} from '@creator-campus/common';
import {useSnackbar} from '@creator-campus/common-components';
import {updateDoc} from 'firebase/firestore';
import Typography from '@mui/joy/Typography';
import {useEffect, useRef, useState} from 'react';
import {notificationsSettings} from './ProfileAccountSettingsTab.tsx';

interface Props {
  user: User;
}

export default function NotificationsIconButton({user}: Props) {
  const [menuOpen, setMenuOpen] = useState(false);

  const dropdownRef = useRef<HTMLDivElement | null>(null);
  const {showErrorSnackbar} = useSnackbar();

  const handleClickAway = (event: MouseEvent) => {
    const target = event.target as Node;
    if (!['BUTTON', 'LI', 'P'].includes(target.nodeName)) {
      setMenuOpen(false);
    }
  };

  useEffect(() => {
    if (menuOpen) {
      document.addEventListener('mousedown', handleClickAway);
    } else {
      document.removeEventListener('mousedown', handleClickAway);
    }

    return () => document.removeEventListener('mousedown', handleClickAway);
  }, [menuOpen]);

  return (
    <Box ref={dropdownRef}>
      <Dropdown open={menuOpen}>
        <MenuButton
          slots={{root: IconButton}}
          slotProps={{root: {variant: 'outlined', color: 'neutral'}}}
          onClick={() => setMenuOpen(!menuOpen)}
        >
          <Notifications />
        </MenuButton>
        <Menu
          placement={'bottom-end'}
          sx={{p: 1.5}}
        >
          <Typography level={'title-md'}>Email notifications</Typography>
          {notificationsSettings.map((item) => (
            <Stack
              key={item.title}
              spacing={2}
              direction={'row'}
              alignItems={'center'}
              justifyContent={'space-between'}
              mt={0.3}
            >
              <Typography level={'body-sm'}>{item.title}</Typography>
              <Select
                variant={'plain'}
                sx={{fontSize: 14}}
                value={item.value(user)}
                onChange={async (_, newValue) => {
                  if (newValue) {
                    await updateDoc(
                      user._doc,
                      //@ts-ignore
                      item.getUpdateObject(newValue),
                    ).catch((e) => showErrorSnackbar(e, 'Error updating preferences.'));
                  }
                }}
              >
                {...item.options.map((o) => (
                  <Option value={o.value}>
                    <Typography level={'body-sm'}>{o.title}</Typography>
                  </Option>
                ))}
              </Select>
            </Stack>
          ))}
        </Menu>
      </Dropdown>
    </Box>
  );
}
