import {UseHitsProps, useInfiniteHits, useInstantSearch} from 'react-instantsearch';
import {Box, Typography} from '@mui/joy';
import {SearchOff} from '@mui/icons-material';
import InfiniteScroll from 'react-infinite-scroll-component';
import { LoadingIndicator } from '../LoadingIndicator';
import {AlertBanner} from '../AlertBanner';
import {AlgoliaFilters} from './AlgoliaFiltersSheet';
import {StaffRole} from '@creator-campus/common';
import {useUser} from '../providers/UserProvider';

interface Props extends UseHitsProps {
  itemBuilder: (hit: any, i: number, filters: AlgoliaFilters) => JSX.Element;
  filters: AlgoliaFilters;
  filtering: boolean;
  endOfResultsMessage?: string;
}

export function AlgoliaHits({itemBuilder, filters, filtering, endOfResultsMessage, ...props}: Props) {
  const {items, isLastPage, showMore} = useInfiniteHits(props);
  const {status} = useInstantSearch();
  const {user, university} = useUser();

  return (
    <InfiniteScroll
      dataLength={items.length}
      next={showMore}
      hasMore={!isLastPage}
      scrollableTarget='pageShell'
      loader={
        <LoadingIndicator
          size={'md'}
          sx={{mt: 3, mb: 3}}
        />
      }
      style={{display: 'flex', flexDirection: 'column'}}
    >
      {status !== 'idle' && items.length === 0 ? (
        <LoadingIndicator sx={{width: '100%', height: 'calc(100vh - 300px)'}} />
      ) : items.length === 0 ? (
        <Box sx={{width: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center', height: '65vh', alignContent: 'center', alignItems: 'center'}}>
          <SearchOff />
          <Typography
            level='body-md'
            sx={{color: 'text.tertiary'}}
          >
            {user?.staffRole === StaffRole.OWNER && university?.partner && filters['universityId'] && !filtering ? 'Start building your community!' : 'No results'}
          </Typography>
        </Box>
      ) : (
        <>
          {items.map((hit, i) => itemBuilder(hit, i, filters))}
          {isLastPage && endOfResultsMessage && (
            <AlertBanner
              text={endOfResultsMessage}
              color={'warning'}
            />
          )}
        </>
      )}
    </InfiniteScroll>
  );
}
