import {LoadingIndicator, useAuth, useUser, useScreenWidth} from '@creator-campus/common-components';
import {Button, FormControl, FormHelperText, Stack} from '@mui/joy';
import Input from '@mui/joy/Input';
import {EMAIL_INPUT_SLOT_PROPS} from '../constants/styles.ts';
import EmailRoundedIcon from '@mui/icons-material/EmailRounded';
import LockRoundedIcon from '@mui/icons-material/LockRounded';
import {useEffect, useState} from 'react';
import {EmailAuthProvider, reauthenticateWithCredential} from 'firebase/auth';

interface Props {
  primaryButtonText: string;
  secondaryButtonText: string;
  tertiaryButtonText?: string;
  primaryButtonAction: () => void;
  secondaryButtonAction: () => void;
  tertiaryButtonAction?: () => void;
}

export function UpdateEmailForm({primaryButtonText, secondaryButtonText, tertiaryButtonText, primaryButtonAction, secondaryButtonAction, tertiaryButtonAction}: Props) {
  const [loading, setLoading] = useState<boolean>(false);
  const [emailError, setEmailError] = useState<string | null>(null);
  const [passwordError, setPasswordError] = useState<string | null>(null);
  const [verificationSentToEmail, setVerificationSentToEmail] = useState<string | null>(null);

  const {currentUser} = useAuth();
  const {user} = useUser();
  const screenWidth = useScreenWidth();
  const isSmallScreen = screenWidth < 400;

  if (!user || !currentUser) {
    return <></>;
  }

  const [email, setEmail] = useState<string>(user.email === user.universityEmail ? '' : user.email);
  const [password, setPassword] = useState<string>('');
  const [nextEnabled, setNextEnabled] = useState<boolean>(user.email !== user.universityEmail);

  async function updateEmail() {
    setLoading(true);
    setEmailError(null);
    setPasswordError(null);

    // Re-authenticate user
    const credential = EmailAuthProvider.credential(user!.email, password);
    const success = await reauthenticateWithCredential(currentUser!, credential)
      .then(() => {
        return true;
      })
      .catch((e) => {
        const error = e.code as string;
        if (error.endsWith('wrong-password')) {
          setPasswordError('Password is incorrect.');
        } else {
          setPasswordError('Sorry, something went wrong.');
        }

        return false;
      });

    if (success) {
      // Proceed to send verification email for update
      const errorMessage = await user!.verifyBeforeUpdateEmail(currentUser!, email);
      setEmailError(errorMessage);

      if (!errorMessage) {
        setVerificationSentToEmail(email);
      }
    }

    setLoading(false);
  }

  useEffect(() => {
    const interval = setInterval(async () => {
      await currentUser.reload();
      if (currentUser.email !== user.email) {
        await user.ensureSyncedWithAuthEmail(currentUser.email!);
        setNextEnabled(true);
      }
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  return (
    <>
      <Stack
        spacing={1}
        sx={{pt: 1, pb: 0.5}}
      >
        <FormControl error={!!passwordError}>
          <Input
            type='password'
            placeholder='Re-enter password'
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            startDecorator={<LockRoundedIcon />}
            size='sm'
            sx={{minHeight: 35}}
          />
          {passwordError && <FormHelperText>{passwordError}</FormHelperText>}
        </FormControl>
        <Stack direction='row'>
          <FormControl
            error={!!emailError}
            sx={{width: '100%'}}
          >
            <Input
              type='email'
              size='sm'
              slotProps={EMAIL_INPUT_SLOT_PROPS}
              startDecorator={<EmailRoundedIcon />}
              placeholder='<EMAIL>'
              value={email}
              disabled={nextEnabled}
              onChange={(event) => setEmail(event.target.value)}
              sx={{
                borderTopRightRadius: 0,
                borderBottomRightRadius: 0,
                minHeight: 35,
              }}
            />
            {emailError && <FormHelperText>{emailError}</FormHelperText>}
          </FormControl>
          <Button
            disabled={email.length === 0 || email === verificationSentToEmail || nextEnabled || loading}
            onClick={updateEmail}
            sx={{
              width: isSmallScreen ? 90 : 150,
              maxHeight: 35,
              borderTopLeftRadius: 0,
              borderBottomLeftRadius: 0,
            }}
          >
            {loading ? <LoadingIndicator size='sm' /> : isSmallScreen ? 'Update' : 'Update email'}
          </Button>
        </Stack>

        {nextEnabled ? <FormHelperText>Thanks for verifying your email, you're good to go!</FormHelperText> : verificationSentToEmail && <FormHelperText>Please click on the verification link we sent to {verificationSentToEmail}. Then check back here!</FormHelperText>}
      </Stack>

      <Stack
        spacing={1}
        direction='row'
        sx={{alignSelf: 'end'}}
      >
        {tertiaryButtonText && tertiaryButtonAction && (
          <Button
            variant='outlined'
            color='neutral'
            onClick={tertiaryButtonAction}
          >
            {tertiaryButtonText}
          </Button>
        )}
        {!nextEnabled && (
          <Button
            variant='outlined'
            color='neutral'
            onClick={secondaryButtonAction}
          >
            {secondaryButtonText}
          </Button>
        )}
        <Button
          variant='solid'
          disabled={!nextEnabled}
          onClick={primaryButtonAction}
        >
          {primaryButtonText}
        </Button>
      </Stack>
    </>
  );
}
