import {algoliasearch, SearchClient} from 'algoliasearch';
import {logger} from './logger';
import {config} from 'dotenv';
import {demoMode} from './firebase';

if (typeof location === 'undefined') {
  config({path: '.env.local'});
}

// Undefined on production because it is provided by .env.local
const devName = process.env.REACT_APP_DEV_NAME;

if (typeof location === 'undefined') {
  config({path: '.env'});
}

export type AlgoliaIndexName = 'people' | 'projects';

export interface IndexConfig {
  name: string;
  page: string;
  searchableAttributes: string[];
  attributesForFaceting: string[];
  customRanking: string[];
}

function getIndexConfigs(): Record<AlgoliaIndexName, IndexConfig> {
  return {
    people: {
      name: getIndexName('people', demoMode),
      page: 'people',
      searchableAttributes: ['objectID', 'name', 'bio'],
      attributesForFaceting: ['applicationStatus', 'fake', 'founder', 'hideProfile', 'openToWork', 'persona', 'profileCompleted', 'role', 'services', 'universityId'],
      customRanking: ['desc(featuredUntil)', 'desc(score)'],
    },
    projects: {
      name: getIndexName('projects', demoMode),
      page: 'startups',
      searchableAttributes: ['objectID', 'name', 'summary', 'description', 'opportunityTitles', 'opportunityDescriptions'],
      attributesForFaceting: ['numOpenOpportunities', 'compensations', 'fake', 'locations', 'tags', 'universityId', 'hidden'],
      customRanking: ['desc(featuredUntil)', 'desc(score)'],
    },
  };
}

function getIndexName(index: AlgoliaIndexName, demoMode: boolean) {
  let name = 'creator_campus_firestore';
  if (demoMode) {
    name += '_demo';
  }

  name += `_${index}`;

  if (devName && !demoMode) {
    name += `_${devName}`;
  }

  return name;
}

export class Algolia {
  readonly indexConfigs: Record<string, IndexConfig>;
  readonly client: SearchClient;

  constructor() {
    this.indexConfigs = getIndexConfigs();
    this.client = algoliasearch(process.env.REACT_APP_ALGOLIA_APP_ID!, devName ? process.env.REACT_APP_ALGOLIA_WRITE_API_KEY! : process.env.REACT_APP_ALGOLIA_API_KEY!);

    logger.debug('Initialised Algolia with index configs:', this.indexConfigs);
  }

  public getQueryUrl(indexName: AlgoliaIndexName, query: string, absolute: boolean = false) {
    const config = this.indexConfigs[indexName];
    const url = `/${config.page}?tab=global&${config.name}%5Bquery%5D=${query}`;
    return absolute ? `${location.origin}${url}` : url;
  }

  public async syncIndexConfigsToAlgolia() {
    const promises = Object.values(this.indexConfigs).map((index) =>
      this.client.setSettings({
        indexName: index.name,
        indexSettings: {
          searchableAttributes: index.searchableAttributes,
          attributesForFaceting: index.attributesForFaceting,
          customRanking: index.customRanking,
        },
      }),
    );

    await Promise.all(promises);
  }

  public async deleteIndexes() {
    const promises = Object.values(this.indexConfigs).map((index) => this.client.deleteIndex({indexName: index.name}));

    await Promise.all(promises);
  }
}

// Undefined for Firebase Functions
let algolia: Algolia | undefined = undefined;
if (process.env.REACT_APP_ALGOLIA_APP_ID) {
  algolia = new Algolia();
}

export {algolia};
