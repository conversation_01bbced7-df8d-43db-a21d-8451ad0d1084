import Link from '@mui/joy/Link';
import Typography from '@mui/joy/Typography';
import Stack from '@mui/joy/Stack';
import {useNavigate, useSearchParams} from 'react-router-dom';
import CreateAccountFromLinkForm from '../components/CreateAccountFromLinkForm.tsx';
import {useEffect, useState} from 'react';
import {ApprovedAlumnus, functions, Role} from '@creator-campus/common';
import {LoadingIndicator} from '@creator-campus/common-components';
import {httpsCallable} from 'firebase/functions';
import {ProductionLandingPageShell} from '../components/ProductionLandingPageShell.tsx';

export default function AlumniSignupPage() {
  const [approvedAlumnus, setApprovedAlumnus] = useState<ApprovedAlumnus | null>(null);

  const [searchParams] = useSearchParams();
  const alumnusId = searchParams.get('alumnusId');
  const token = searchParams.get('token');

  const navigate = useNavigate();

  useEffect(() => {
    if (!alumnusId || !token) {
      navigate('/login');
      return;
    }

    httpsCallable(
      functions(),
      'validateAlumnusToken',
    )({alumnusId, token})
      .then((response) => {
        const data = response.data as any;

        if (data.valid) {
          setApprovedAlumnus(data as ApprovedAlumnus);
        } else {
          navigate('/login');
          return;
        }
      })
      .catch(() => {
        navigate('/login');
        return;
      });
  }, []);

  return (
    <ProductionLandingPageShell>
      <Stack gap={1}>
        <Typography level='h3'>Create alumnus account</Typography>
        <Typography level='body-sm'>
          Already have an account?{' '}
          <Link
            href={'/login'}
            level='title-sm'
          >
            Sign in.
          </Link>
        </Typography>
      </Stack>
      {approvedAlumnus === null ? (
        <LoadingIndicator size='md' />
      ) : (
        <CreateAccountFromLinkForm
          email={approvedAlumnus.email}
          universityId={approvedAlumnus.universityId}
          role={Role.GRADUATE}
        />
      )}
    </ProductionLandingPageShell>
  );
}
