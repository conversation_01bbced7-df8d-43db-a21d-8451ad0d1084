{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true, "defaultBranch": "main"}, "files": {"include": ["admin/src", "common/src", "functions/src", "production/src"]}, "formatter": {"enabled": true, "formatWithErrors": false, "indentStyle": "space", "lineWidth": 320, "attributePosition": "multiline"}, "javascript": {"formatter": {"quoteStyle": "single", "jsxQuoteStyle": "single", "bracketSpacing": false, "semicolons": "always", "arrowParentheses": "always", "indentStyle": "space"}}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true}}}