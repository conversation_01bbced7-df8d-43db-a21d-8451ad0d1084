import BusinessCenter from '@mui/icons-material/BusinessCenter';
import Code from '@mui/icons-material/Code';
import EmojiObjects from '@mui/icons-material/EmojiObjects';
import {ElementType} from 'react';

export type PersonaId = 'technical' | 'business' | 'creative';

export class Persona {
  static readonly TECHNICAL = new Persona('technical', 'Technical', '#EA9A3E', 'warning', Code);
  static readonly BUSINESS = new Persona('business', 'Business', '#1F7A1F', 'success', BusinessCenter);
  static readonly CREATIVE = new Persona('creative', 'Creative', '#C41C1C', 'danger', EmojiObjects);

  private constructor(
    readonly id: PersonaId,
    readonly label: string,
    readonly color: string,
    readonly themedColor: string,
    readonly icon: ElementType,
  ) {}

  static fromId(id: string | null) {
    return this.values().find((c) => c.id === id) || null;
  }

  static values(): Persona[] {
    return Object.values(Persona);
  }
}
