import {createBrowserRouter, Navigate} from 'react-router-dom';
import PrivateRoute from './PrivateRoute.tsx';
import {algolia, demoMode, Project, User, usingEmulators} from '@creator-campus/common';
import {NotFoundPage} from '@creator-campus/common-components';
import OnboardingPage from './pages/OnboardingPage.tsx';
import ProjectsPage from './pages/ProjectsPage.tsx';
import EducationPage from './pages/EducationPage.tsx';
import ProfilePage from './pages/ProfilePage.tsx';
import PeoplePage from './pages/PeoplePage.tsx';
import AdminPage from './pages/AdminPage.tsx';
import DiscussionPage from './pages/DiscussionPage.tsx';
import GodModePage from './pages/GodModePage.tsx';
import LoginPage from './pages/LoginPage.tsx';
import SignupPage from './pages/SignupPage.tsx';
import SharePage from './pages/SharePage.tsx';
import ProjectCard from './components/ProjectCard.tsx';
import Typography from '@mui/joy/Typography';
import ProfileCard from './components/ProfileCard.tsx';
import AlumniSignupPage from './pages/AlumniSignupPage.tsx';
import OnUserVisit from './components/OnUserVisit.tsx';
import {LayoutWrapper} from './components/LayoutWrapper.tsx';
import {StartupShowcasePage} from './pages/StartupShowcasePage.tsx';
import MentorsPage from './pages/MentorsPage.tsx';
import MentorSignupPage from './pages/MentorSignupPage.tsx';
import {ModalController} from './components/ModalController.tsx';

export const router = createBrowserRouter([
  {
    path: '/',
    element: (
      <PrivateRoute>
        <OnUserVisit />
        <LayoutWrapper />
        <ModalController />
      </PrivateRoute>
    ),
    children: [
      {
        index: true,
        element: (
          <Navigate
            replace
            to='/startups/explore'
          />
        ),
      },
      {path: 'onboarding', element: <OnboardingPage />},
      {path: 'startups', element: <ProjectsPage tab='explore' />},
      {
        path: 'startups/explore',
        element: <ProjectsPage tab='explore' />,
      },
      {
        path: 'startups/my-startups',
        element: <ProjectsPage tab='my-startups' />,
      },
      {
        path: 'mentors',
        element: <MentorsPage />,
      },
      {
        path: 'education',
        element: (
          <Navigate
            replace
            to='/education/Co-Founder Guide/Introduction'
          />
        ),
      },
      {
        path: 'education/:article',
        element: <EducationPage />,
      },
      {
        path: 'education/:path/:article',
        element: <EducationPage />,
      },
      {
        path: 'profile',
        element: (
          <Navigate
            replace
            to='/profile/about'
          />
        ),
      },
      {
        path: 'profile/:path',
        element: <ProfilePage />,
      },
      {path: 'people', element: <PeoplePage />},
      {
        path: 'admin',
        element: (
          <Navigate
            replace
            to='/admin/branding'
          />
        ),
      },
      {
        path: 'admin/:tab',
        element: <AdminPage />,
      },
      {path: 'discussion', element: <DiscussionPage />},
      {
        path: 'discussion/everyone',
        element: <DiscussionPage tab='everyone' />,
      },
      {
        path: 'discussion/my-university',
        element: <DiscussionPage tab='my-university' />,
      },
      {
        path: 'discussion/post/:universityId/:postId',
        element: <DiscussionPage />,
      },
      ...(usingEmulators
        ? [
            {
              path: 'godMode',
              element: <GodModePage />,
            },
          ]
        : []),
    ],
  },
  {path: '/login', element: <LoginPage />},
  ...(!demoMode
    ? [
        {
          path: '/signup',
          element: (
            <Navigate
              replace
              to='/signup/students'
            />
          ),
        },
        {
          path: '/signup/:tab',
          element: <SignupPage />,
        },
        {
          path: '/signup/:tab/:referral',
          element: <SignupPage />,
        },
      ]
    : []),
  {
    path: '/share/startup/:id',
    element: (
      <SharePage
        fetchItem={Project.fetch}
        ifLoggedIn={(projectId, navigate) => {
          const link = algolia!.getQueryUrl('projects', projectId);
          navigate(link);
        }}
        content={(project) =>
          project ? (
            <ProjectCard
              projectId={project.id}
              hit={project}
              editMode={false}
              privacyMode={true}
            />
          ) : (
            <Typography>This project no longer exists.</Typography>
          )
        }
      />
    ),
  },
  {
    path: '/share/profile/:id',
    element: (
      <SharePage
        fetchItem={User.fetch}
        ifLoggedIn={(uid, navigate) => {
          const link = algolia!.getQueryUrl('people', uid);
          navigate(link);
        }}
        content={(user) =>
          user ? (
            <ProfileCard
              user={user}
              privacyMode={true}
            />
          ) : (
            <Typography>This profile no longer exists.</Typography>
          )
        }
      />
    ),
  },
  {path: '/create-mentor-account', element: <MentorSignupPage />},
  {path: '/create-alumnus-account', element: <AlumniSignupPage />},
  {path: '/showcase/:universityId/:token', element: <StartupShowcasePage />},
  {path: '*', element: <NotFoundPage />},
]);
