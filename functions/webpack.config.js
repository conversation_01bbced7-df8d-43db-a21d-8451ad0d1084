const path = require('path');
const nodeExternals = require('webpack-node-externals')

module.exports = {
  target: 'node',
  mode: 'production',
  entry: './src/index.ts',
  module: {
    rules: [
      {
        test: /\.tsx?$/,
        loader: 'ts-loader',
        exclude: /node_modules/,
      },
    ],
  },
  optimization: {
    minimize: false, // ensure exports aren’t removed
    concatenateModules: false,
  },
  performance: {
    hints: false,
  },
  devtool: 'nosources-source-map',
  resolve: {
    extensions: ['.tsx', '.ts', '.js', '.json'],
    symlinks: false,
  },
  externals: [
    /^firebase.+$/,
    /^@google.+$/,
    nodeExternals({
      allowlist: ['@creator-campus/common'],
      modulesDir: path.resolve(__dirname, '../node_modules'),
    }),
    nodeExternals({
      allowlist: ['@creator-campus/common'],
    }),
  ],
  output: {
    filename: 'index.js',
    path: path.resolve(__dirname, './webpack/dist'),
    libraryTarget: 'commonjs',
  },
};
