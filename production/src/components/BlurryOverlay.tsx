import {ReactNode} from 'react';
import Box from '@mui/joy/Box';
import Typography from '@mui/joy/Typography';
import Button from '@mui/joy/Button';
import {Stack} from '@mui/joy';
import {useNavigate} from 'react-router-dom';
import {alpha} from '@mui/system';

interface Props {
  children?: ReactNode;
  blur?: string;
  title?: string;
  subtitle?: string;
  button?: 'activate' | {text: string; action: () => void};
}

export function BlurryOverlay({children, blur = '3px', title, subtitle, button}: Props) {
  const navigate = useNavigate();

  const buttonConfig = button === 'activate' ? {text: 'Activate', action: () => navigate(`${location.pathname}?showModal=activateCommunity`)} : button;

  return (
    <>
      <Box sx={{filter: `blur(${blur})`, pointerEvents: 'none'}}>{children}</Box>
      <Box
        sx={{
          position: 'absolute',
          top: -5,
          left: -5,
          right: -5,
          bottom: -5,
          backgroundColor: (theme) => alpha(theme.palette.background.body, 0.75),
          display: 'flex',
          alignItems: 'start',
          justifyContent: 'center',
          fontSize: '1.5rem',
          fontWeight: 500,
          zIndex: 10,
          pointerEvents: 'auto',
        }}
      >
        <Stack
          alignItems={'center'}
          height={'55vh'}
          justifyContent={'center'}
        >
          <Typography
            level='h4'
            textAlign='center'
          >
            {title || 'Activate your community'}
          </Typography>
          {subtitle && (
            <Typography
              level='body-sm'
              textAlign='center'
            >
              {subtitle}
            </Typography>
          )}
          {buttonConfig && (
            <Button
              sx={{width: 110, mt: 1.5}}
              onClick={buttonConfig.action}
            >
              {buttonConfig.text}
            </Button>
          )}
        </Stack>
      </Box>
    </>
  );
}
