import {<PERSON><PERSON><PERSON><PERSON>, Stack, Typography} from '@mui/joy';
import {Persona, University, StaffRole, User} from '@creator-campus/common';
import {AlgoliaCheckboxes, AlgoliaFilters, UpdateAlgoliaFilters, usePeopleString} from '@creator-campus/common-components';
import {useEffect} from 'react';
import {UniversityFilterCardsRow} from './UniversityFilterCardsRow.tsx';

interface Props {
  user: User;
  university: University;
  filters: AlgoliaFilters;
  onFilterChange: UpdateAlgoliaFilters;
  withUniversityFilter?: boolean;
}

export default function PeopleFilterOptions({user, university, filters, onFilterChange, withUniversityFilter = true}: Props) {
  // todo show counts when we have more people (so it looks more impressive)
  // const [facetCounts, setFacetCounts] = useState<Record<string, number>>({});

  useEffect(() => {
    // Fetch number of results for each Persona
    // algolia.client
    //   .search([
    //     {
    //       indexName: algolia.indexConfigs.people.name,
    //       params: {
    //         facets: ['persona'],
    //         hitsPerPage: 0, // No actual results needed, just facets
    //       },
    //     },
    //   ])
    //   .then((response) => {
    //     const results = response.results[0] as Record<string, any>;
    //     const facets = results.facets?.['persona'] || {};
    //     setFacetCounts(facets);
    //   });
  }, []);

  return (
    <Stack
      spacing={1}
      sx={{overflow: 'hidden'}}
    >
      {university.id !== 'University of Oxford' && (university.partner || user.staffRole === StaffRole.OWNER) && withUniversityFilter && (
        <UniversityFilterCardsRow
          universityId={university.id}
          filters={filters}
          onFilterChange={onFilterChange}
        />
      )}
      <FormLabel sx={{typography: 'body-sm', pt: 0.5, mb: 0.5}}>{usePeopleString().persona}</FormLabel>
      <AlgoliaCheckboxes
        attribute={'persona'}
        options={Persona.values()}
        getLabel={(p) => (
          <>
            {<p.icon />}
            <Typography
              level={'body-sm'}
              sx={{pl: 0.4}}
            >
              {p.label}
              {/*todo: uncomment when we have more people*/}
              {/*{' '}({facetCounts[p.label.toLowerCase()]})*/}
            </Typography>
          </>
        )}
        getValue={(p) => p.label.toLowerCase()}
        filters={filters}
        onFilterChange={onFilterChange}
      />
    </Stack>
  );
}
