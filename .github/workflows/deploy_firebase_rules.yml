name: Deploy Firebase Rules

on:
  push:
    branches:
      - main
    paths:
      - 'firestore.rules'
      - 'firestore.indexes.json'
      - 'storage.rules'
      - '.github/workflows/deploy_firebase_rules.yml'

jobs:
  deploy:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        project: [production, demo]
        rule: [firestore, storage]

    steps:
      - uses: actions/checkout@v2

      - name: Deploy Firebase rules
        uses: w9jds/firebase-action@master
        with:
          args: deploy --only ${{ matrix.rule }} --project ${{ matrix.project }}
        env:
          FIREBASE_TOKEN: "${{ secrets.FIREBASE_DEPLOY_TOKEN }}"
