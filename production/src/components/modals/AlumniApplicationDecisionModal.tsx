import {AlumniApplication, functions, University} from '@creator-campus/common';
import {<PERSON><PERSON><PERSON>ndicator, ModalHeader, ModalShell, useSnackbar} from '@creator-campus/common-components';
import Button from '@mui/joy/Button';
import {Stack} from '@mui/joy';
import Typography from '@mui/joy/Typography';
import {httpsCallable} from 'firebase/functions';
import {useState} from 'react';
import Card from '@mui/joy/Card';
import {Numbers, School} from '@mui/icons-material';
import Tooltip from '@mui/joy/Tooltip';

interface DecideAlumniApplicationResponse {
  success: boolean;
  message: string;
}

interface Props {
  application: AlumniApplication;
  university: University;
  onClose: () => void;
}

export function AlumniApplicationDecisionModal({application, university, onClose}: Props) {
  const [loadingAccept, setLoadingAccept] = useState(false);
  const [loadingReject, setLoadingReject] = useState(false);

  const {showSnackbar} = useSnackbar();

  async function handleSubmit(accept: boolean) {
    if (accept) {
      setLoadingAccept(true);
    } else {
      setLoadingReject(true);
    }

    const response = await httpsCallable(
      functions(),
      'decideAlumniApplication',
    )({
      applicationId: application.id,
      universityId: university.id,
      accept,
    });

    const data = response.data as DecideAlumniApplicationResponse;

    setLoadingAccept(false);
    setLoadingReject(false);

    if (data.success) {
      onClose();
    } else {
      showSnackbar(data.message, 'danger');
    }
  }

  return (
    <ModalShell
      onClose={onClose}
      withCloseButton={true}
      closeOnBackgroundClick={true}
    >
      <ModalHeader
        title='Alumnus Application'
        subtitle={`${application.name} is applying to join ${university.name} as an Alumni. If you recognise their details, you can approve them to join Creator Campus.`}
      />
      <Stack sx={{mt: 2}}>
        <Card sx={{width: 400, alignSelf: 'center'}}>
          <Typography
            level='title-md'
            sx={{alignSelf: 'center'}}
          >
            {application.name}
          </Typography>
          <Stack
            direction='row'
            spacing={4}
            justifyContent='center'
          >
            <Stack
              direction='row'
              spacing={1}
              alignItems='center'
            >
              <Tooltip
                enterTouchDelay={0}
                title='Student number'
                variant='solid'
              >
                <Numbers />
              </Tooltip>
              <Typography level='body-sm'>{application.studentNumber}</Typography>
            </Stack>
            <Stack
              direction='row'
              spacing={1}
              alignItems='center'
            >
              <Tooltip
                enterTouchDelay={0}
                title='Graduation year'
                variant='solid'
              >
                <School />
              </Tooltip>
              <Typography level='body-sm'>{application.graduationYear}</Typography>
            </Stack>
          </Stack>
        </Card>
        <Stack
          direction='row'
          spacing={1}
          justifyContent='end'
          sx={{mt: 2}}
        >
          <Button
            disabled={loadingAccept || loadingReject}
            variant='soft'
            color='danger'
            onClick={() => handleSubmit(false)}
          >
            {loadingReject ? <LoadingIndicator size='sm' /> : 'Reject'}
          </Button>
          <Button
            disabled={loadingAccept || loadingReject}
            variant='soft'
            color='success'
            onClick={() => handleSubmit(true)}
          >
            {loadingAccept ? <LoadingIndicator size='sm' /> : 'Approve'}
          </Button>
        </Stack>
      </Stack>
    </ModalShell>
  );
}
