import {collection, doc, FieldValue, type FirestoreDataConverter, getDoc, getDocs, onSnapshot, orderBy, query, QueryDocumentSnapshot, type SnapshotOptions, Timestamp, updateDoc, where, type WhereFilterOp, type WithFieldValue} from 'firebase/firestore';
import {firestore, storage, usingEmulators} from '../../firebase';
import {AlumniApplication, AlumniApplicationConverter} from '../alumnus/alumniApplication';
import {User, UserConverter} from '../user/user';
import {UniversityExternalLink} from './universityExternalLink';
import {ApprovedMentor} from '../mentor/approvedMentor';
import {Role} from '../user/role';
import {NonexistentUniversity} from './nonexistentUniversity';
import {UniversityBranding, UniversityBrandingNoDates, UniversityBrandingSafe} from './universityBranding';
import {getDownloadURL, ref, type StorageReference, uploadBytes} from 'firebase/storage';
import {PartnershipData} from './partnershipData';

type PaymentState = 'notPaying' | 'pending' | 'paying';

export class University {
  static readonly collectionName = 'universities';
  readonly _logoStorage: StorageReference;

  static get col() {
    return collection(firestore(), University.collectionName).withConverter(new UniversityConverter());
  }

  constructor(
    readonly id: string,
    readonly almostReachedUserLimit: boolean,
    readonly branding: UniversityBranding | null, // Partner universities only
    readonly dateJoined: Date,
    readonly domain: string,
    readonly hasMentors: boolean,
    readonly lastDiscussionPost: Date,
    readonly name: string,
    readonly partner: boolean,
    readonly partnerSince: Date | null, // Partner universities only
    readonly paymentState: PaymentState,
    readonly pinnedPosts: string[],
    readonly reachedUserLimit: boolean,
    readonly selfManagedAlumniApplications: boolean,
    readonly showcaseEnabled: boolean,
    readonly sidebarLinks: UniversityExternalLink[] | null, // Partner universities only (may still be null for partners)
  ) {
    this._logoStorage = University.logoStorage(id);
  }

  public static doc(id: string) {
    return doc(firestore(), University.collectionName, id).withConverter(new UniversityConverter());
  }

  public static privateShowcaseDoc(id: string) {
    return doc(firestore(), University.collectionName, id, 'private', 'startupShowcase');
  }

  public async fetchPartnershipData() {
    return await PartnershipData.fetchForUniversity(this.id);
  }

  public static logoStorage(uniId: string) {
    return ref(storage(), `/universities/${uniId}/logo`);
  }

  public doc() {
    return University.doc(this.id);
  }

  public static defaults(id: string, domain: string) {
    const name = id;
    return new University(id, false, null, new Date(), domain, false, new Date(), name, false, null, 'notPaying', [], false, false, false, []);
  }

  public static where(field: keyof UniversityDbModel, opStr: WhereFilterOp, value: UniversityDbModel[keyof UniversityDbModel]) {
    return where(field, opStr, value);
  }

  public static async fetchAll() {
    const snapshot = await getDocs(University.col);
    return snapshot.docs.map((d) => d.data());
  }

  public static async fetchFromFirestore(uniId: string) {
    const uniDoc = await getDoc(doc(firestore(), University.collectionName, uniId).withConverter(new UniversityConverter()));
    return uniDoc.data();
  }

  private static async fetchUniversitiesJson() {
    const response = await fetch('/uk_universities.json');
    const data = (await response.json()) as {name: string; domains: string[]; country: string}[];

    // Filter out non-UK universities
    const ukUniversities = data.filter((u) => u.country === 'United Kingdom');

    // Filter out duplicate universities
    return Array.from(new Map(ukUniversities.map((item) => [item.name, item])).values());
  }

  public static async idFromDomain(domain: string): Promise<string | NonexistentUniversity> {
    const data = await University.fetchUniversitiesJson();

    const uniData = data.find((uni) => uni.domains.some((d) => d === domain || domain.endsWith(`.${d}`)));
    return uniData?.name || new NonexistentUniversity();
  }

  public static async fetchFullList(): Promise<{id: string; name: string}[]> {
    const data = await University.fetchUniversitiesJson();
    const emulatorsUniversities = ['University of Creators', 'University of Innovators', 'University of Entrepreneurs'];

    return [...data.map((d) => ({id: d.name, name: d.name})), ...(usingEmulators ? emulatorsUniversities.map((id) => ({id, name: id})) : [])];
  }

  public static async fromId(id: string) {
    const snapshot = await getDoc(University.doc(id));
    return snapshot.data() || null;
  }

  public async getStaff(sorted: boolean = false) {
    const q = query(collection(firestore(), User.collectionName), where('universityId', '==', this.id), where('staffRole', '!=', null)).withConverter(new UserConverter());

    const snapshot = await getDocs(q);
    const staff = snapshot.docs.map((d) => d.data());

    return sorted ? staff.sort((a, b) => (a.staffRole!.rank > b.staffRole!.rank ? -1 : 1)) : staff;
  }

  public async getMentors() {
    const q = query(collection(firestore(), User.collectionName), where('universityId', '==', this.id), where('role', '==', Role.MENTOR.id)).withConverter(new UserConverter());

    const snapshot = await getDocs(q);
    const mentors = snapshot.docs.map((d) => d.data());

    return mentors.sort((a, b) => (a.dateJoined > b.dateJoined ? -1 : 1));
  }

  public async getPendingMentors() {
    const q = query(collection(firestore(), 'approvedMentors'), where('universityId', '==', this.id));

    const snapshot = await getDocs(q);
    const mentors = snapshot.docs.map((d) => d.data() as ApprovedMentor);

    return mentors.sort((a, b) => (a.expiresAt > b.expiresAt ? -1 : 1));
  }

  public listenToAlumniApplications(sortField: string = 'ts', sortAscending: boolean = false, onNext: (apps: AlumniApplication[]) => void) {
    const q = query(collection(firestore(), University.collectionName, this.id, 'alumniApplications'), orderBy(sortField, sortAscending ? 'asc' : 'desc')).withConverter(new AlumniApplicationConverter());

    return onSnapshot(q, (docSnap) => {
      onNext(docSnap.docs.map((d) => d.data()));
    });
  }

  public static async getLogoUrl(universityId: string) {
    return await getDownloadURL(University.logoStorage(universityId));
  }

  public async getLogoUrl() {
    if (!this.branding?.logoUpdatedAt) {
      // Logo has not been uploaded yet
      return null;
    }

    return University.getLogoUrl(this.id);
  }

  public async updateLogo(logo: File) {
    await uploadBytes(this._logoStorage, logo);
    // Wait to make sure the file has uploaded before we notify listeners
    await new Promise((f) => setTimeout(f, 50));
    await this.notifyLogoUpdated();
  }

  private async notifyLogoUpdated() {
    await updateDoc(this.doc(), {
      'branding.logoUpdatedAt': new Date(),
    });
  }

  public getShowcaseUrl(token: string) {
    return `${usingEmulators ? 'localhost:5173' : 'https://app.creatorcampus.io'}/showcase/${this.id.replace(/ /g, '-')}/${token}`;
  }

  /**
   * The threshold when the university has almost reached its free user limit.
   * @param userLimit
   */
  public static getUserLimitWarningThreshold(userLimit: number) {
    return userLimit - 50;
  }

  public isPaying() {
    return this.paymentState === 'paying';
  }

  public shouldActivateCommunity() {
    return !this.partner || (this.almostReachedUserLimit && !this.isPaying());
  }
}

export interface UniversityDbModelNoDates {
  almostReachedUserLimit: boolean;
  branding: UniversityBrandingNoDates | null;
  domain: string;
  hasMentors: boolean;
  name: string;
  partner: boolean;
  paymentState: PaymentState;
  pinnedPosts: string[];
  reachedUserLimit: boolean;
  selfManagedAlumniApplications: boolean;
  showcaseEnabled: boolean;
  sidebarLinks: UniversityExternalLink[] | null;
}

export interface UniversityDbModel extends UniversityDbModelNoDates {
  branding: UniversityBranding | null; // The UniversityBranding object has dates
  dateJoined: Timestamp;
  lastDiscussionPost: Timestamp;
  partnerSince: Timestamp | null;
}

export interface UniversityDbModelSafe extends UniversityDbModelNoDates {
  branding: UniversityBrandingSafe | null; // The UniversityBranding object has dates
  dateJoined: any;
  lastDiscussionPost: any;
  partnerSince: any | null;
}

export class UniversityConverter implements FirestoreDataConverter<University, UniversityDbModel> {
  toFirestore(uni: WithFieldValue<University>): WithFieldValue<UniversityDbModel> {
    return {
      almostReachedUserLimit: uni.almostReachedUserLimit,
      branding: uni.branding,
      dateJoined: uni.dateJoined instanceof FieldValue ? uni.dateJoined : Timestamp.fromDate(uni.dateJoined as Date),
      domain: uni.domain,
      hasMentors: uni.hasMentors,
      lastDiscussionPost: uni.lastDiscussionPost instanceof FieldValue ? uni.lastDiscussionPost : Timestamp.fromDate(uni.lastDiscussionPost as Date),
      name: uni.name,
      partner: uni.partner,
      partnerSince: uni.partnerSince === null ? null : uni.partnerSince instanceof FieldValue ? uni.partnerSince : Timestamp.fromDate(uni.partnerSince as Date),
      paymentState: uni.paymentState,
      pinnedPosts: uni.pinnedPosts,
      reachedUserLimit: uni.reachedUserLimit,
      selfManagedAlumniApplications: uni.selfManagedAlumniApplications,
      showcaseEnabled: uni.showcaseEnabled,
      sidebarLinks: uni.sidebarLinks,
    };
  }

  fromFirestore(snapshot: QueryDocumentSnapshot, options: SnapshotOptions): University {
    const data = snapshot.data(options) as UniversityDbModel;
    return new University(
      snapshot.id,
      data.almostReachedUserLimit,
      data.branding,
      data.dateJoined.toDate(),
      data.domain,
      data.hasMentors,
      data.lastDiscussionPost.toDate(),
      data.name,
      data.partner,
      data.partnerSince?.toDate() || null,
      data.paymentState,
      data.pinnedPosts,
      data.reachedUserLimit,
      data.selfManagedAlumniApplications,
      data.showcaseEnabled,
      data.sidebarLinks,
    );
  }
}
