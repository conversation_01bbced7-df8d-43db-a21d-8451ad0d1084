{
  "compilerOptions": {
    "target": "ES2022",
    "strict": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "resolveJsonModule": true,
    "moduleResolution": "Node",
    "jsx": "react-jsx",
    "lib": ["es2022", "dom", "dom.iterable"],
    "declaration": true,
    "declarationMap": true,
    "allowJs": true,
    "sourceMap": true,
    "declarationDir": "dist/types",
    "rootDir": "src",
    "outDir": "dist",
  },
  "include": ["src/**/*"],
  "exclude": ["dist", "node_modules"]
}