import {StaffRole, University, User, UserConverter} from '@creator-campus/common';
import {ReactElement} from 'react';
import {AdminBrandingTab} from '../components/AdminBrandingTab';
import EditProfileForm from '../components/EditProfileForm';
import {VerifyEmailForm} from '../components/VerifyEmailForm.tsx';

export type OnboardingStageKey = 'verifyEmail' | 'adminBranding' | 'editProfile';

export interface OnboardingStageConfig {
  key: OnboardingStageKey;
  condition: (user: User, university: University) => boolean;
  render: (buttonText: string, onNext?: () => void) => ReactElement<{buttonText?: string; onNext?: () => void}>;
  contentOptions: OnboardingStageContentOptions;
}

export interface OnboardingStageContentOptions {
  centred: boolean;
}

export const onboardingStages: OnboardingStageConfig[] = [
  {
    key: 'verifyEmail',
    condition: (user) => !user.emailVerified,
    render: (buttonText, onNext) => (
      <VerifyEmailForm
        buttonText={buttonText}
        onNext={onNext}
      />
    ),
    contentOptions: {centred: true},
  },
  {
    key: 'adminBranding',
    condition: (user, university) => user.staffRole === StaffRole.OWNER && !university.branding,
    render: (buttonText, onNext) => (
      <AdminBrandingTab
        buttonText={buttonText}
        buttonPosition='end'
        onNext={onNext}
      />
    ),
    contentOptions: {centred: false},
  },
  {
    key: 'editProfile',
    condition: (user) => user.staffRole === StaffRole.OWNER && !User.isProfileComplete(new UserConverter().toFirestore(user)),
    render: (buttonText, _onNext) => (
      <EditProfileForm
        buttonText={buttonText}
        withRevertChanges={false}
        withNavigationBlocking={false}
      />
    ),
    contentOptions: {centred: false},
  },
];

export function getOnboardingStages(user: User, university: University) {
  return onboardingStages.filter((stage) => stage.condition(user, university));
}
