import {Box, CircularProgress, Typography} from '@mui/joy';
import {DashboardRounded} from '@mui/icons-material';
import Card from '@mui/joy/Card';
import {useEffect, useState} from 'react';
import {collection, getCountFromServer, query, where} from 'firebase/firestore';
import {AdminPieChart} from './AdminPieChart';
import {AdminLineChart} from './AdminLineChart';
import {firestore, PieChartData, Project, ProjectTag} from '@creator-campus/common';

interface Props {
  universityId: string;
  setUniversityId: (uni: string) => void;
  numProjectsData: PieChartData[] | null;
}

export function ProjectInsightsCard({universityId, setUniversityId, numProjectsData}: Props) {
  const [categoryData, setCategoryData] = useState<PieChartData[] | null>(null);

  const numActiveStartups = numProjectsData === null ? <CircularProgress size='sm' /> : universityId === 'All' ? numProjectsData.reduce((n, {value}) => n + value, 0) : numProjectsData.find((d) => d.name === universityId)!.value;

  useEffect(() => {
    Promise.all(
      ProjectTag.values().map(async (tag) => {
        const count = await getCountFromServer(query(collection(firestore(), Project.collectionName), ...(universityId === 'All' ? [] : [where('universityId', '==', universityId)]), where('tags', 'array-contains', tag.label), where('fake', '==', false), where('hidden', '==', false)));
        return {name: tag.label, value: count.data().count, colour: undefined};
      }),
    ).then((categoryArray) => {
      setCategoryData(categoryArray);
    });
  }, [universityId]);

  return (
    <>
      <Card sx={{flex: 1}}>
        <Box sx={{display: 'flex', alignItems: 'center', mb: 2}}>
          <Typography
            startDecorator={<DashboardRounded />}
            level='h4'
          >
            Startups
          </Typography>
        </Box>

        <Box sx={{display: 'flex', flexDirection: 'row', alignItems: 'center', alignSelf: 'center', mb: 2}}>
          <Typography
            level='h1'
            sx={{fontWeight: 'bold', mr: 2}}
          >
            {numActiveStartups}
          </Typography>
          <Typography level='body-md'>Active Startup{numActiveStartups === 1 ? '' : 's'}</Typography>
        </Box>

        {universityId === 'All' && (
          <Box sx={{alignSelf: 'center'}}>
            <AdminPieChart
              data={numProjectsData}
              showEmptyLegendItems={false}
              onSegmentClick={(data: any) => {
                setUniversityId(data.name);
              }}
            />
          </Box>
        )}

        <Box sx={{alignSelf: 'center'}}>
          <AdminLineChart
            influxField={'projectsCreated'}
            label={'New Startups'}
            universityId={universityId === 'All' ? undefined : universityId}
          />
        </Box>

        <Typography
          level='title-lg'
          sx={{textAlign: 'center'}}
        >
          Startup Categories
        </Typography>
        <Box sx={{alignSelf: 'center'}}>
          <AdminPieChart
            data={categoryData}
            showEmptyLegendItems={false}
          />
        </Box>
      </Card>
    </>
  );
}
