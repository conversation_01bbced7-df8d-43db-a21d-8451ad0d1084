import {FormEvent, useState} from 'react';
import Button from '@mui/joy/Button';
import Textarea from '@mui/joy/Textarea';
import ButtonGroup from '@mui/joy/ButtonGroup';

import {logger, User, UserHit} from '@creator-campus/common';
import {LoadingIndicator, useConnectModalString, useSnackbar, ModalHeader, ModalShell} from '@creator-campus/common-components';

interface FormElements extends HTMLFormControlsCollection {
  message: HTMLInputElement;
}

interface ConnectFormElement extends HTMLFormElement {
  readonly elements: FormElements;
}

interface Props {
  open: boolean;
  onClose: () => void;
  me: User;
  user: UserHit;
}

export default function ConnectModal({open, onClose, me, user}: Props) {
  const [loading, setLoading] = useState<boolean>(false);

  const {showErrorSnackbar, showSnackbar} = useSnackbar();
  const strings = useConnectModalString();

  async function handleSubmit(message: string) {
    setLoading(true);
    await me
      .sendConnectionRequest(user, message)
      .then(() => {
        showSnackbar('Connection email sent.', 'success');

        onClose();
      })
      .catch((e) => {
        logger.error(e);
        showErrorSnackbar(e, 'Error sending connection request.');
      });

    setLoading(false);
  }

  return (
    <>
      {open && (
        <ModalShell onClose={onClose}>
          <ModalHeader
            title={strings.header}
            subtitle={strings.body}
          />
          <form
            onSubmit={async (event: FormEvent<ConnectFormElement>) => {
              event.preventDefault();
              const formData = event.currentTarget.elements;
              await handleSubmit(formData.message.value.trim());
            }}
          >
            <Textarea
              required
              name='message'
              placeholder='Outline the reason why you would like to connect - keep it clear!'
              minRows={3}
              maxRows={5}
              variant='outlined'
              color='neutral'
              slotProps={{textarea: {maxLength: 5000}}}
              sx={{mt: 2}}
            />
            <ButtonGroup
              spacing={1}
              aria-label='spacing button group'
              sx={{mt: 2}}
            >
              <Button
                disabled={loading}
                color='primary'
                variant='solid'
                type='submit'
              >
                {loading ? <LoadingIndicator size='sm' /> : strings.send}
              </Button>
              <Button onClick={() => onClose()}>{strings.cancel}</Button>
            </ButtonGroup>
          </form>
        </ModalShell>
      )}
    </>
  );
}
