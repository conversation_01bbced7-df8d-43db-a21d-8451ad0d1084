import {ReactNode} from 'react';
import Modal from '@mui/joy/Modal';
import {ModalClose, ModalOverflow} from '@mui/joy';
import Sheet from '@mui/joy/Sheet';

interface Props {
  children: ReactNode;
  onClose: () => void;
  withCloseButton?: boolean;
  closeOnBackgroundClick?: boolean;
  withPadding?: boolean;
}

export function ModalShell({children, onClose, withCloseButton = false, closeOnBackgroundClick = false, withPadding = true}: Props) {
  function handleClose(_: string, reason: string) {
    if (withCloseButton && reason === 'closeClick') {
      onClose();
    } else if (closeOnBackgroundClick && (reason === 'backdropClick' || reason === 'escapeKeyDown')) {
      onClose();
    }
  }

  return (
    <Modal
      open={true}
      onClose={handleClose}
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backdropFilter: 'blur(6px)',
        zIndex: 990,
      }}
    >
      <ModalOverflow
        sx={{
          display: 'flex',
          alignItems: 'center',
          flexDirection: 'column',
          justifyContent: 'flex-start',
          zIndex: 1000,
        }}
      >
        <Sheet
          variant='outlined'
          sx={{
            minWidth: 300,
            borderRadius: 'md',
            p: withPadding ? 4 : 0,
            maxWidth: {lg: 700, md: '70%', sm: '80%', xs: '90%'},
            margin: 'auto',
          }}
        >
          {withCloseButton && <ModalClose variant='plain' />}
          {children}
        </Sheet>
      </ModalOverflow>
    </Modal>
  );
}
