import Switch, {switchClasses} from '@mui/joy/Switch';
import {AlgoliaFilters, UpdateAlgoliaFilters} from './AlgoliaFiltersSheet';
import {useLocation} from 'react-router-dom';
import {useUser} from '../providers/UserProvider';
import {influxWriter, newAppMetric} from '@creator-campus/common';

interface Props {
  attribute: string;
  activeValue: string;
  filters: AlgoliaFilters;
  onFilterChange: UpdateAlgoliaFilters;
}

export function AlgoliaSwitch({attribute, activeValue, filters, onFilterChange}: Props) {
  const {pathname} = useLocation();
  const {user} = useUser();

  return (
    <Switch
      checked={Object.keys(filters).includes(attribute)}
      onChange={(e) => {
        onFilterChange(attribute, e.target.checked ? activeValue : null);

        if (user) {
          const metric = newAppMetric(user).intField('filterToggleCount', 1).tag('value', `${e.target.checked}`).tag('page', pathname).tag('filterType', 'switch').tag('filterAttribute', attribute);
          influxWriter.writePoint(metric);
        }
      }}
      sx={{
        [`& .${switchClasses.thumb}`]: {
          transition: 'width 0.2s, left 0.2s',
        },
        '--Switch-thumbSize': '17px',
        '--Switch-trackWidth': '40px',
        '--Switch-trackHeight': '22px',
      }}
    />
  );
}
