import Chip from '@mui/joy/Chip';
import Stack from '@mui/joy/Stack';
import Tooltip from '@mui/joy/Tooltip';
import {ReactNode} from 'react';

interface Props {
  tooltip: string;
  icon?: ReactNode;
  content?: ReactNode;
  size?: 'sm' | 'md' | 'lg';
  color?: 'primary' | 'neutral' | 'success' | 'warning' | 'danger';
  variant?: 'plain' | 'soft' | 'outlined' | 'solid';
  spacing?: number;
  enterDelay?: number;
  enterNextDelay?: number;
}

export function TooltipChip({tooltip, icon, content, size, color, variant, spacing, enterDelay = 700, enterNextDelay = 200}: Props) {
  return (
    <Tooltip
      enterDelay={enterDelay}
      enterNextDelay={enterNextDelay}
      title={tooltip}
      variant={'solid'}
    >
      <Chip
        size={size}
        color={color}
        variant={variant}
      >
        <Stack
          direction={'row'}
          alignItems={'center'}
          spacing={spacing}
        >
          {icon}
          {content}
        </Stack>
      </Chip>
    </Tooltip>
  );
}
