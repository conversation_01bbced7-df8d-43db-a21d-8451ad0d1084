# Creator Campus

This repo contains the frontend code, serverless backend logic and Firebase connection for the Creator Campus web app.

## Contributing guide

TODO.

## Running the code

### Install dependencies:
```
npm install
npm install @mui/joy @emotion/react @emotion/styled
```
You'll need to create a `.env` file and add the Algolia and Firebase API keys and credentials.
### Run the development server locally:
```
npm run dev
```
### To deploy cloud functions:
```
firebase deploy --only functions
```
To deploy to production, `git push` to the `main` branch.
