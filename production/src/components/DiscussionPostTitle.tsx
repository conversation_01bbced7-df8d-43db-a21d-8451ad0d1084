import Box from '@mui/joy/Box';
import Avatar from '@mui/joy/Avatar';
import Typography from '@mui/joy/Typography';
import {Skeleton, Stack} from '@mui/joy';
import Link from '@mui/joy/Link';
import {useNavigate} from 'react-router-dom';
import {algolia, NonexistentUser, User} from '@creator-campus/common';
import StaffChip from './StaffChip.tsx';
import PersonaChip from './PersonaChip.tsx';
import KarmaChip from './KarmaChip.tsx';

interface Props {
  author: User | NonexistentUser | null;
  profilePicUrl: string | null;
}

export default function DiscussionPostTitle({author, profilePicUrl}: Props) {
  const navigate = useNavigate();

  return (
    <Box sx={{display: 'flex', alignItems: 'center', gap: 1.5, mx: 1, mt: 1}}>
      {author instanceof NonexistentUser ? (
        <>
          <Avatar
            src=''
            alt='-'
          />
          <Typography
            level='body-md'
            fontWeight='bold'
          >
            [Deleted user]
          </Typography>
        </>
      ) : author && profilePicUrl ? (
        <>
          <Link onClick={() => navigate(algolia!.getQueryUrl('people', author.id))}>
            <Avatar
              src={profilePicUrl}
              alt={author.name}
            />
          </Link>
          <Stack>
            <Stack
              direction={'row'}
              spacing={1}
              alignItems={'center'}
            >
              <Link onClick={() => navigate(algolia!.getQueryUrl('people', author.id))}>
                <Typography
                  level='body-md'
                  fontWeight='bold'
                >
                  {author.name}
                </Typography>
              </Link>
              {author.staffRole && <StaffChip />}
            </Stack>
            <Stack
              direction={'row'}
              spacing={0.5}
              alignItems={'center'}
            >
              <KarmaChip karma={author.karma} />
              <PersonaChip
                persona={author.persona!}
                size={'sm'}
              />
            </Stack>
          </Stack>
        </>
      ) : (
        <>
          <Skeleton
            variant='circular'
            width={40}
            height={40}
          />
          <Stack>
            <Skeleton
              variant='text'
              width={100}
            />
            <Stack
              direction={'row'}
              spacing={0.5}
              alignItems={'center'}
            >
              <Skeleton
                variant={'text'}
                width={30}
              />
              <Skeleton
                variant={'text'}
                width={75}
              />
            </Stack>
          </Stack>
        </>
      )}
    </Box>
  );
}
