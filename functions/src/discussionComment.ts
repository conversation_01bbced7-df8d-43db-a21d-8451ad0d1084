import {onDocumentCreated, onDocumentDeleted, onDocumentUpdated} from 'firebase-functions/v2/firestore';
import {DocumentReference, FieldValue} from 'firebase-admin/firestore';
import * as admin from 'firebase-admin';
import {DiscussionComment, DiscussionCommentDbModelSafe, DiscussionPostBase, DiscussionPostDbModelSafe, DiscussionPostReplyEmailTemplate, University, User, UserDbModelSafe} from '@creator-campus/common';
import {deletePostAttachments, movePendingAttachments, sendEmail, initSentryForFunction} from './utils';
import * as Sentry from '@sentry/node';

async function processCreatedComment(comment: DiscussionCommentDbModelSafe, commentId: string, postId: string, universityId?: string) {
  Sentry.addBreadcrumb({
    message: `Processing created comment: ${commentId}`,
    category: 'discussion',
    level: 'info',
    data: {
      commentId,
      postId,
      universityId: universityId || 'global',
      authorId: comment.authorId,
      hasAttachments: comment.attachments.length > 0
    }
  });

  const discussionDoc = universityId ? admin.firestore().collection(University.collectionName).doc(universityId) : admin.firestore().collection('global').doc('discussion');
  const postDoc = discussionDoc.collection('discussion').doc(postId);
  await postDoc.update({
    numComments: FieldValue.increment(1),
  });

  Sentry.addBreadcrumb({
    message: 'Incremented post comment count',
    category: 'firestore',
    level: 'info',
    data: { commentId, postId, universityId: universityId || 'global' }
  });

  // Check if we should notify the author of the post that a comment was made
  const postSnapshot = await postDoc.get();
  const post = postSnapshot.data() as DiscussionPostDbModelSafe;
  const postAuthorSnapshot = await admin.firestore().collection(User.collectionName).doc(post.authorId).get();
  const postAuthor = postAuthorSnapshot.data() as UserDbModelSafe;

  if (postAuthor.notifications.discussionPostReplies === 'realTime' && comment.authorId !== post.authorId) {
    const commenterSnapshot = await admin.firestore().collection(User.collectionName).doc(comment.authorId).get();
    const commenter = commenterSnapshot.data() as UserDbModelSafe;

    Sentry.addBreadcrumb({
      message: 'Sending post reply notification email',
      category: 'email',
      level: 'info',
      data: {
        commentId,
        postId,
        postAuthorId: post.authorId,
        commenterId: comment.authorId,
        postAuthorEmail: postAuthor.email
      }
    });

    await sendEmail(
      postAuthor.email,
      new DiscussionPostReplyEmailTemplate({
        commentText: comment.text,
        commenterName: commenter.name,
        postCommentsLink: `https://app.creatorcampus.io/discussion/post/${universityId || 'global'}/${postId}`,
      }),
    ).catch(console.error);
  } else {
    Sentry.addBreadcrumb({
      message: 'Skipping post reply notification',
      category: 'email',
      level: 'info',
      data: {
        commentId,
        postId,
        reason: postAuthor.notifications.discussionPostReplies !== 'realTime' ? 'notifications disabled' : 'self comment',
        isOwnComment: comment.authorId === post.authorId
      }
    });
  }

  // Move any pending attachments from temporary storage to the created comment's storage folder
  const originFolder = DiscussionPostBase.getPendingUploadsFolder(universityId);
  const destinationFolder = DiscussionComment.getUploadsFolder(postId, commentId, universityId);

  Sentry.addBreadcrumb({
    message: 'Moving pending attachments for comment',
    category: 'storage',
    level: 'info',
    data: {
      commentId,
      postId,
      attachmentCount: comment.attachments.length,
      originFolder,
      destinationFolder
    }
  });

  await movePendingAttachments(comment, originFolder, destinationFolder);
}

export const onUniDiscussionCommentCreated = onDocumentCreated('universities/{universityId}/discussion/{postId}/comments/{commentId}', async (event) => {
  initSentryForFunction('onUniDiscussionCommentCreated');

  if (!event.data) {
    console.error('Event data was undefined!');
    Sentry.addBreadcrumb({
      message: 'Event data is undefined in onUniDiscussionCommentCreated',
      category: 'function',
      level: 'error'
    });
    return;
  }

  const comment = event.data.data() as DiscussionCommentDbModelSafe;
  const commentId = event.params.commentId;
  const postId = event.params.postId;
  const universityId = event.params.universityId;

  Sentry.addBreadcrumb({
    message: `University discussion comment created: ${commentId}`,
    category: 'discussion',
    level: 'info',
    data: { commentId, postId, universityId }
  });

  await processCreatedComment(comment, commentId, postId, universityId);
});

export const onGlobalDiscussionCommentCreated = onDocumentCreated('global/discussion/discussion/{postId}/comments/{commentId}', async (event) => {
  initSentryForFunction('onGlobalDiscussionCommentCreated');

  if (!event.data) {
    console.error('Event data was undefined!');
    Sentry.addBreadcrumb({
      message: 'Event data is undefined in onGlobalDiscussionCommentCreated',
      category: 'function',
      level: 'error'
    });
    return;
  }

  const comment = event.data.data() as DiscussionCommentDbModelSafe;
  const commentId = event.params.commentId;
  const postId = event.params.postId;

  Sentry.addBreadcrumb({
    message: `Global discussion comment created: ${commentId}`,
    category: 'discussion',
    level: 'info',
    data: { commentId, postId, universityId: 'global' }
  });

  await processCreatedComment(comment, commentId, postId);
});

async function processUpdatedComment(comment: DiscussionCommentDbModelSafe, commentRef: DocumentReference) {
  const upVotesBefore = comment.numUpVotes;
  const upVotesAfter = comment.upVotes.length;

  Sentry.addBreadcrumb({
    message: 'Processing updated comment',
    category: 'discussion',
    level: 'info',
    data: {
      commentId: commentRef.id,
      authorId: comment.authorId,
      upVotesBefore,
      upVotesAfter,
      upVotesChanged: upVotesBefore !== upVotesAfter
    }
  });

  if (upVotesBefore !== upVotesAfter) {
    const karmaChange = upVotesAfter - upVotesBefore;

    Sentry.addBreadcrumb({
      message: 'Comment upvotes changed, updating author karma',
      category: 'discussion',
      level: 'info',
      data: {
        commentId: commentRef.id,
        authorId: comment.authorId,
        karmaChange,
        newUpVoteCount: upVotesAfter
      }
    });

    // Update author's karma
    await admin
      .firestore()
      .collection(User.collectionName)
      .doc(comment.authorId)
      .update({
        karma: FieldValue.increment(karmaChange),
      });

    // Update comment's numLikes field
    return commentRef.update({
      numUpVotes: upVotesAfter,
    });
  }

  return null;
}

export const onUniDiscussionCommentUpdated = onDocumentUpdated('universities/{universityId}/discussion/{postId}/comments/{commentId}', async (event) => {
  initSentryForFunction('onUniDiscussionCommentUpdated');

  if (!event.data) {
    console.error('Event data was undefined!');
    Sentry.addBreadcrumb({
      message: 'Event data is undefined in onUniDiscussionCommentUpdated',
      category: 'function',
      level: 'error'
    });
    return;
  }

  const comment = event.data.after.data() as DiscussionCommentDbModelSafe;
  const commentId = event.params.commentId;
  const postId = event.params.postId;
  const universityId = event.params.universityId;

  Sentry.addBreadcrumb({
    message: `University discussion comment updated: ${commentId}`,
    category: 'discussion',
    level: 'info',
    data: { commentId, postId, universityId }
  });

  return await processUpdatedComment(comment, event.data.after.ref);
});

export const onGlobalDiscussionCommentUpdated = onDocumentUpdated('global/discussion/discussion/{postId}/comments/{commentId}', async (event) => {
  initSentryForFunction('onGlobalDiscussionCommentUpdated');

  if (!event.data) {
    console.error('Event data was undefined!');
    Sentry.addBreadcrumb({
      message: 'Event data is undefined in onGlobalDiscussionCommentUpdated',
      category: 'function',
      level: 'error'
    });
    return;
  }

  const comment = event.data.after.data() as DiscussionCommentDbModelSafe;
  const commentId = event.params.commentId;
  const postId = event.params.postId;

  Sentry.addBreadcrumb({
    message: `Global discussion comment updated: ${commentId}`,
    category: 'discussion',
    level: 'info',
    data: { commentId, postId, universityId: 'global' }
  });

  return await processUpdatedComment(comment, event.data.after.ref);
});

async function processDeletedComment(comment: DiscussionCommentDbModelSafe, postId: string, commentId: string, universityId?: string) {
  Sentry.addBreadcrumb({
    message: `Processing deleted comment: ${commentId}`,
    category: 'discussion',
    level: 'info',
    data: {
      commentId,
      postId,
      universityId: universityId || 'global',
      authorId: comment.authorId,
      hasAttachments: comment.attachments.length > 0
    }
  });

  const discussionDoc = universityId ? admin.firestore().collection(University.collectionName).doc(universityId) : admin.firestore().collection('global').doc('discussion');

  await discussionDoc
    .collection('discussion')
    .doc(postId)
    .update({
      numComments: FieldValue.increment(-1),
    });

  Sentry.addBreadcrumb({
    message: 'Decremented post comment count',
    category: 'firestore',
    level: 'info',
    data: { commentId, postId, universityId: universityId || 'global' }
  });

  const storageFolder = DiscussionComment.getUploadsFolder(postId, commentId, universityId);

  Sentry.addBreadcrumb({
    message: 'Deleting comment attachments',
    category: 'storage',
    level: 'info',
    data: {
      commentId,
      postId,
      attachmentCount: comment.attachments.length,
      storageFolder
    }
  });

  await deletePostAttachments(comment, postId, storageFolder);
}

export const onUniDiscussionCommentDeleted = onDocumentDeleted('universities/{universityId}/discussion/{postId}/comments/{commentId}', async (event) => {
  initSentryForFunction('onUniDiscussionCommentDeleted');

  if (!event.data) {
    console.error('Event data was undefined!');
    Sentry.addBreadcrumb({
      message: 'Event data is undefined in onUniDiscussionCommentDeleted',
      category: 'function',
      level: 'error'
    });
    return;
  }

  const comment = event.data.data() as DiscussionCommentDbModelSafe;
  const commentId = event.params.commentId;
  const postId = event.params.postId;
  const universityId = event.params.universityId;

  Sentry.addBreadcrumb({
    message: `University discussion comment deleted: ${commentId}`,
    category: 'discussion',
    level: 'info',
    data: { commentId, postId, universityId }
  });

  await processDeletedComment(comment, postId, commentId, universityId);
});

export const onGlobalDiscussionCommentDeleted = onDocumentDeleted('global/discussion/discussion/{postId}/comments/{commentId}', async (event) => {
  initSentryForFunction('onGlobalDiscussionCommentDeleted');

  if (!event.data) {
    console.error('Event data was undefined!');
    Sentry.addBreadcrumb({
      message: 'Event data is undefined in onGlobalDiscussionCommentDeleted',
      category: 'function',
      level: 'error'
    });
    return;
  }

  const comment = event.data.data() as DiscussionCommentDbModelSafe;
  const commentId = event.params.commentId;
  const postId = event.params.postId;

  Sentry.addBreadcrumb({
    message: `Global discussion comment deleted: ${commentId}`,
    category: 'discussion',
    level: 'info',
    data: { commentId, postId, universityId: 'global' }
  });

  await processDeletedComment(comment, postId, commentId);
});
