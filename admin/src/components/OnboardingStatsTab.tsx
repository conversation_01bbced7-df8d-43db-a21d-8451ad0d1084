import Stack from '@mui/joy/Stack';
import {useEffect, useState} from 'react';
import {collection, getCountFromServer, query, where} from 'firebase/firestore';
import {firestore, User} from '@creator-campus/common';
import {AdminPieChart, LoadingIndicator} from '@creator-campus/common-components';
import Card from '@mui/joy/Card';
import Typography from '@mui/joy/Typography';
import {DataUsage} from '@mui/icons-material';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import Tooltip from '@mui/joy/Tooltip';

export function OnboardingStatsTab() {
  const [conversionRateOverall, setConversionRateOverall] = useState<number | null>(null);
  const [conversionRateLastTwoWeeks, setConversionRateLastTwoWeeks] = useState<number | null>(null);
  const [conversionRateLastTwoMonths, setConversionRateLastTwoMonths] = useState<number | null>(null);

  const now = Date.now();
  const twoWeeksAgo = new Date(now - 1000 * 60 * 60 * 24 * 14);
  const twoMonthsAgo = new Date(now - 1000 * 60 * 60 * 24 * 60);

  useEffect(() => {
    const promises = [
      getCountFromServer(query(collection(firestore(), User.collectionName), where('applicationStatus', '==', 'inProgress'))).then((snapshot) => {
        return snapshot.data().count;
      }),
      getCountFromServer(query(collection(firestore(), User.collectionName), where('applicationStatus', '!=', 'inProgress'))).then((snapshot) => {
        return snapshot.data().count;
      }),
      getCountFromServer(query(collection(firestore(), User.collectionName), where('applicationStatus', '==', 'inProgress'), where('dateJoined', '>', twoWeeksAgo))).then((snapshot) => {
        return snapshot.data().count;
      }),
      getCountFromServer(query(collection(firestore(), User.collectionName), where('applicationStatus', '!=', 'inProgress'), where('dateJoined', '>', twoWeeksAgo))).then((snapshot) => {
        return snapshot.data().count;
      }),
      getCountFromServer(query(collection(firestore(), User.collectionName), where('applicationStatus', '==', 'inProgress'), where('dateJoined', '>', twoMonthsAgo))).then((snapshot) => {
        return snapshot.data().count;
      }),
      getCountFromServer(query(collection(firestore(), User.collectionName), where('applicationStatus', '!=', 'inProgress'), where('dateJoined', '>', twoMonthsAgo))).then((snapshot) => {
        return snapshot.data().count;
      }),
    ];

    Promise.all(promises).then(([unconvertedOverall, convertedOverall, unconvertedLastTwoWeeks, convertedLastTwoWeeks, unconvertedLastTwoMonths, convertedLastTwoMonths]) => {
      const totalOverall = unconvertedOverall + convertedOverall;
      setConversionRateOverall(totalOverall === 0 ? -1 : Math.round((convertedOverall / totalOverall) * 100));
      const totalLastTwoWeeks = unconvertedLastTwoWeeks + convertedLastTwoWeeks;
      setConversionRateLastTwoWeeks(totalLastTwoWeeks === 0 ? -1 : Math.round((convertedLastTwoWeeks / totalLastTwoWeeks) * 100));
      const totalLastTwoMonths = unconvertedLastTwoMonths + convertedLastTwoMonths;
      setConversionRateLastTwoMonths(totalLastTwoMonths === 0 ? -1 : Math.round((convertedLastTwoMonths / totalLastTwoMonths) * 100));
    });
  }, []);

  return (
    <Stack
      direction={'row'}
      spacing={1}
    >
      <Card>
        <Stack>
          <Stack
            direction={'row'}
            spacing={0.8}
            alignItems={'center'}
          >
            <DataUsage />
            <Typography level={'title-md'}>Conversion rate (last 2 weeks)</Typography>
            <Tooltip
              enterTouchDelay={0}
              title={'What proportion of users have submitted their application after signing up.'}
              variant='solid'
            >
              <InfoOutlinedIcon />
            </Tooltip>
          </Stack>
          {conversionRateLastTwoWeeks === null ? (
            <LoadingIndicator size={'sm'} />
          ) : conversionRateLastTwoWeeks === -1 ? (
            <AdminPieChart data={[]} />
          ) : (
            <AdminPieChart
              data={[
                {name: 'Converted', value: conversionRateLastTwoWeeks, colour: '#1F7A1F'},
                {name: 'Unconverted', value: 100 - conversionRateLastTwoWeeks, colour: '#C41C1C'},
              ]}
            />
          )}
        </Stack>
      </Card>
      <Card>
        <Stack>
          <Stack
            direction={'row'}
            spacing={0.8}
            alignItems={'center'}
          >
            <DataUsage />
            <Typography level={'title-md'}>Conversion rate (last 2 months)</Typography>
            <Tooltip
              enterTouchDelay={0}
              title={'What proportion of users have submitted their application after signing up.'}
              variant='solid'
            >
              <InfoOutlinedIcon />
            </Tooltip>
          </Stack>
          {conversionRateLastTwoMonths === null ? (
            <LoadingIndicator size={'sm'} />
          ) : conversionRateLastTwoMonths === -1 ? (
            <AdminPieChart data={[]} />
          ) : (
            <AdminPieChart
              data={[
                {name: 'Converted', value: conversionRateLastTwoMonths, colour: '#1F7A1F'},
                {name: 'Unconverted', value: 100 - conversionRateLastTwoMonths, colour: '#C41C1C'},
              ]}
            />
          )}
        </Stack>
      </Card>
      <Card>
        <Stack>
          <Stack
            direction={'row'}
            spacing={0.8}
            alignItems={'center'}
          >
            <DataUsage />
            <Typography level={'title-md'}>Overall conversion rate</Typography>
            <Tooltip
              enterTouchDelay={0}
              title={'What proportion of users have submitted their application after signing up.'}
              variant='solid'
            >
              <InfoOutlinedIcon />
            </Tooltip>
          </Stack>
          {conversionRateOverall === null ? (
            <LoadingIndicator size={'sm'} />
          ) : (
            <AdminPieChart
              data={[
                {name: 'Converted', value: conversionRateOverall, colour: '#1F7A1F'},
                {name: 'Unconverted', value: 100 - conversionRateOverall, colour: '#C41C1C'},
              ]}
            />
          )}
        </Stack>
      </Card>
    </Stack>
  );
}
