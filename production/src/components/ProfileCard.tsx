import Avatar from '@mui/joy/Avatar';
import Box from '@mui/joy/Box';
import Card from '@mui/joy/Card';
import IconButton from '@mui/joy/IconButton';
import Typography from '@mui/joy/Typography';
import {Edit, Login, OpenInNew, Share} from '@mui/icons-material';
import PersonaChip from './PersonaChip';
import ConnectModal from './modals/ConnectModal.tsx';
import {useEffect, useState} from 'react';
import {useNavigate} from 'react-router-dom';
import {Button, Link, Tooltip} from '@mui/joy';
import ProjectChip from './ProjectChip';
import {logger, OnboardingPathway, Role, User, UserHit} from '@creator-campus/common';
import {useSnackbar} from '@creator-campus/common-components';
import UserSocials from './UserSocials.tsx';
import Stack from '@mui/joy/Stack';
import KarmaChip from './KarmaChip.tsx';
import StaffChip from './StaffChip.tsx';
import CalendlyModal from './modals/CalendlyModal.tsx';
import {OnboardingPathwayChip} from './OnboardingPathwayChip.tsx';
import {FeatureContentButton} from './FeatureContentButton.tsx';
import {FeaturedChip} from './FeaturedChip.tsx';
import {UniversityChip} from './UniversityChip.tsx';
import {RoleChip} from './RoleChip.tsx';
import Chip from '@mui/joy/Chip';
import {ReadMoreText} from './ReadMoreText.tsx';

interface Props {
  me?: User;
  user: UserHit;
  privacyMode?: boolean;
  withFeatureButton?: boolean;
  withCard?: boolean;
  withActionButton?: boolean;
  withStaffChip?: boolean;
}

export default function ProfileCard({me, user, privacyMode = false, withFeatureButton = false, withCard = true, withActionButton = true, withStaffChip = true}: Props) {
  const [profilePicUrl, setProfilePicUrl] = useState<string>('');
  const [connectModalOpen, setConnectModalOpen] = useState<boolean>(false);
  const [calendlyModalOpen, setCalendlyModalOpen] = useState<boolean>(false);

  const navigate = useNavigate();
  const {showErrorSnackbar, showSnackbar} = useSnackbar();

  function NameRow() {
    return (
      <Stack
        direction={'row'}
        spacing={1}
        alignItems={'center'}
        mb={0.5}
        ml={1.3}
      >
        <Typography
          level='title-lg'
          sx={{ml: '10px'}}
        >
          {privacyMode ? user.getFirstName() : user.name}
        </Typography>
        {!privacyMode && user.staffRole && withStaffChip && <StaffChip />}
        {user.openToWork && <OnboardingPathwayChip role={OnboardingPathway.TALENT} />}
        {user.founder && <OnboardingPathwayChip role={OnboardingPathway.FOUNDER} />}
        {!user.founder && !user.openToWork && <OnboardingPathwayChip role={OnboardingPathway.SUPPORTER} />}
      </Stack>
    );
  }

  function ShareIconButton() {
    if (privacyMode) {
      return null;
    }

    return (
      <IconButton
        variant='plain'
        onClick={handleCopy}
        sx={{marginLeft: 'auto'}}
      >
        <Share />
      </IconButton>
    );
  }

  function ActionButton() {
    if (!me) {
      return (
        <Button
          onClick={() => navigate('/login')}
          size='sm'
          startDecorator={<Login />}
          sx={{minWidth: 100}}
        >
          Log in to connect
        </Button>
      );
    }

    if (me.id === user.id) {
      return (
        <Button
          sx={{ml: 'auto', mr: 'auto', width: '100%'}}
          variant='outlined'
          onClick={() => navigate('/profile/')}
          size='sm'
          startDecorator={<Edit />}
        >
          Edit
        </Button>
      );
    }

    if (user.role === Role.MENTOR) {
      return (
        <>
          <Tooltip title={me?.hasFullAccessToApp() ? '' : 'Complete your profile to book mentorship sessions.'}>
            <Box width={'100%'}>
              <Button
                variant='outlined'
                color='primary'
                onClick={() => setCalendlyModalOpen(true)}
                fullWidth
                disabled={!me.hasFullAccessToApp()}
              >
                Book meeting
              </Button>
            </Box>
          </Tooltip>
          {calendlyModalOpen && (
            <CalendlyModal
              onClose={() => setCalendlyModalOpen(false)}
              calendlyUrl={user.website!}
            />
          )}
        </>
      );
    }

    return (
      <>
        <Tooltip title={me?.hasFullAccessToApp() ? '' : 'Complete your profile to message people.'}>
          <Box width={'100%'}>
            <Button
              variant='outlined'
              color='primary'
              onClick={() => setConnectModalOpen(true)}
              fullWidth
              disabled={!me.hasFullAccessToApp()}
            >
              Message
            </Button>
          </Box>
        </Tooltip>
        <ConnectModal
          open={connectModalOpen}
          onClose={() => setConnectModalOpen(false)}
          me={me}
          user={user}
        />
      </>
    );
  }

  function ChipsRow({variant}: {variant: 'desktop' | 'mobile'}) {
    return (
      <Stack
        direction={'row'}
        spacing={0.5}
        gap={0.5}
        flexWrap={'wrap'}
        mb={0.5}
      >
        {variant === 'desktop' && (
          <>
            {user.featuredUntil && <FeaturedChip />}
            <PersonaChip persona={user.persona!} />
          </>
        )}
        <RoleChip role={user.role!} />
        <UniversityChip
          universityId={user.universityId}
          variant={'outlined'}
        />
        <ProjectChip
          userHit={user}
          withLink={!privacyMode}
        />
      </Stack>
    );
  }

  function DesktopProfileCard() {
    return (
      <>
        <Stack
          spacing={1}
          alignItems={'center'}
          mr={1}
        >
          <Avatar
            src={profilePicUrl}
            size='lg'
            sx={{'--Avatar-size': {sm: '80px'}}}
          />
          {!privacyMode && <KarmaChip karma={user.karma || 0} />}
          {user.website && ( // && user.role !== Role.MENTOR
            <Link onClick={() => window.open(user.website!, '_blank')}>
              <Chip
                size={'sm'}
                variant={'plain'}
                color={'primary'}
                endDecorator={<OpenInNew />}
              >
                Portfolio
              </Chip>
            </Link>
          )}
        </Stack>
        <Box>
          <Box sx={{display: 'flex', justifyContent: 'flex-start', alignItems: 'flex-start'}}>
            <Box sx={{display: 'flex', justifyContent: 'flex-start', alignItems: 'center'}}>
              <Box>
                <NameRow />
                <Box ml={1}>
                  <ChipsRow variant={'desktop'} />
                </Box>
              </Box>
            </Box>
            <Box sx={{marginLeft: 'auto', display: 'flex'}}>
              {user.hasAnySocials() && !privacyMode && <UserSocials user={user} />}
              <ShareIconButton />
            </Box>
          </Box>
          <Box sx={{display: 'flex', ml: 1}}>
            <ReadMoreText
              text={user.bio}
              maxLines={User.MAX_BIO_LINES}
              startsExpanded={!withActionButton}
            />
            {withActionButton && (
              <Box
                sx={{
                  display: 'flex',
                  gap: 2,
                  height: '40px',
                  justifyContent: 'space-between',
                  maxWidth: '200px',
                  minWidth: '200px',
                  ml: 2,
                }}
              >
                <ActionButton />
              </Box>
            )}
          </Box>
        </Box>
      </>
    );
  }

  function MobileProfileCard() {
    return (
      <>
        <Box sx={{display: 'flex', justifyContent: 'flex-start', alignItems: 'flex-start'}}>
          <Box sx={{display: 'flex', justifyContent: 'flex-start', alignItems: 'center'}}>
            <Avatar
              src={profilePicUrl}
              size='lg'
              sx={{'--Avatar-size': '60px'}}
            />
            <Box>
              <NameRow />
              <Stack
                ml={1}
                direction={'row'}
                spacing={0.5}
                gap={0.5}
                alignItems={'center'}
                flexWrap={'wrap'}
              >
                {user.featuredUntil && <FeaturedChip />}
                <KarmaChip karma={user.karma || 0} />
                <PersonaChip persona={user.persona!} />
              </Stack>
            </Box>
          </Box>
          <Box sx={{marginLeft: 'auto'}}>
            <ShareIconButton />
          </Box>
        </Box>
        <Box sx={{mt: 1, mb: 1}}>
          <ChipsRow variant={'mobile'} />
          <ReadMoreText
            text={user.bio}
            maxLines={User.MAX_BIO_LINES}
          />
        </Box>
        <Box
          sx={{
            display: 'flex',
            gap: 2,
            height: '40px',
            justifyContent: 'space-between',
          }}
        >
          <ActionButton />
          {user.hasAnySocials() && !privacyMode && (
            <Box
              sx={{
                display: 'flex',
                gap: 2,
                '& > button': {borderRadius: '2rem'},
                height: '40px',
                marginLeft: 'auto',
              }}
            >
              <UserSocials user={user} />
            </Box>
          )}
        </Box>
      </>
    );
  }

  useEffect(() => {
    if (!privacyMode) {
      user
        .getAvatarUrl()
        .then((url) => {
          setProfilePicUrl(url);
        })
        .catch((e) => {
          logger.error(e);
        });
    }
  }, [user._avatarStorage, privacyMode]);

  async function handleCopy() {
    await navigator.clipboard
      .writeText(user.getLink(true))
      .then(() => {
        showSnackbar('Profile link copied to clipboard.');
      })
      .catch((e) => {
        logger.error(e);
        showErrorSnackbar(e, 'Error copying profile link.');
      });
  }

  function CardContents() {
    return (
      <>
        <Box sx={{display: {xs: 'block', sm: 'none'}}}>
          <MobileProfileCard />
        </Box>

        <Box sx={{display: {xs: 'none', sm: 'flex'}}}>
          <DesktopProfileCard />
        </Box>
      </>
    );
  }

  return (
    <>
      {withCard ? (
        <Card
          color={user.featuredUntil ? 'primary' : 'neutral'}
          variant={user.featuredUntil ? 'soft' : 'outlined'}
          sx={{
            maxWidth: '100%',
            overflowWrap: 'break-word',
            wordBreak: 'break-word',
          }}
        >
          {withFeatureButton && me?.isCreatorCampusAdmin() && (
            <Box sx={{position: 'absolute', top: -16, right: 8}}>
              <FeatureContentButton content={user} />
            </Box>
          )}
          <CardContents />
        </Card>
      ) : (
        <CardContents />
      )}
    </>
  );
}
