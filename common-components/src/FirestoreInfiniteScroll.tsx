import InfiniteScroll from 'react-infinite-scroll-component';
import {ReactNode, useEffect, useState} from 'react';
import {CollectionReference, FirestoreDataConverter, getDocs, limit, onSnapshot, orderBy, query, QueryFieldFilterConstraint, QueryStartAtConstraint, startAfter} from 'firebase/firestore';
import {LoadingIndicator} from './LoadingIndicator';
import {logger} from '@creator-campus/common';

interface SortConfig<T> {
  sortField: keyof T;
  direction?: 'asc' | 'desc';
}

interface Props<T extends {id: string | number}> {
  col: CollectionReference;
  converter: FirestoreDataConverter<T>;
  itemBuilder: (item: T, i: number, total: number) => JSX.Element;
  // At least one field in the order must contain unique values
  order: SortConfig<T> | SortConfig<T>[];
  filters?: QueryFieldFilterConstraint | QueryFieldFilterConstraint[];
  liveUpdates?: 'fullCollection' | 'newItemsOnly';
  batchSize?: number;
  itemHeight?: number;
  height?: string;
  inverse?: boolean;
  onFetchMore?: (newItems: T[], allItems: T[]) => void;
  onItemAdded?: (newItem: T, allItems: T[]) => void;
  noItemsPlaceholder?: ReactNode;
  loadingIndicator?: ReactNode;
  scrollFullPage?: boolean;
  sx?: object;
}

export function FirestoreInfiniteScroll<T extends {id: string | number}>({
                                                                           col,
                                                                           converter,
                                                                           itemBuilder,
                                                                           liveUpdates = 'newItemsOnly',
                                                                           batchSize,
                                                                           itemHeight,
                                                                           height,
                                                                           inverse = false,
                                                                           onFetchMore,
                                                                           onItemAdded,
                                                                           noItemsPlaceholder,
                                                                           loadingIndicator,
                                                                           scrollFullPage = false,
                                                                           sx,
                                                                           ...props
                                                                         }: Props<T>) {
  const [initialLoad, setInitialLoad] = useState<boolean>(true);
  const [batch, setBatch] = useState<number>(batchSize || 10);
  const [items, setItems] = useState<T[]>([]);
  const [hasMore, setHasMore] = useState<boolean>(true);

  const order = Array.isArray(props.order) ? props.order.map(sortConfigToConstraint) : [sortConfigToConstraint(props.order)];
  const filters = props.filters === undefined ? [] : Array.isArray(props.filters) ? props.filters : [props.filters];

  function sortConfigToConstraint(sortConfig: SortConfig<T>) {
    return orderBy(sortConfig.sortField as string, sortConfig.direction || 'asc');
  }

  function updateBatchSize() {
    const viewportHeight = window.innerHeight;
    const childrenPerScreen = Math.floor(viewportHeight / itemHeight!);
    logger.debug(`Current batch size: ${childrenPerScreen}`);
    setBatch(childrenPerScreen);
  }

  useEffect(() => {
    fetchMore().then(() => {
      setInitialLoad(false);
    });
  }, []);

  useEffect(() => {
    if (initialLoad) {
      return;
    }

    let firstBatchLoaded = items.length === 0;

    const unsubscribe =
      liveUpdates === 'fullCollection'
        ? onSnapshot(query(col, ...filters, ...order).withConverter(converter), (snapshot) => {
          if (!firstBatchLoaded) {
            firstBatchLoaded = true;
            return;
          }

          for (const change of snapshot.docChanges()) {
            const changeData = change.doc.data();
            if (change.type === 'added') {
              onItemAdded?.(changeData, [...items, changeData]);
              setItems((prevItems) => [changeData, ...prevItems]);
            } else if (change.type === 'removed') {
              setItems((prevItems) => prevItems.filter((item) => item.id !== changeData.id));
            } else {
              setItems((prevItems) => prevItems.map((item) => (item.id === changeData.id ? changeData : item)));
            }
          }
        })
        : onSnapshot(query(col, ...filters, ...order, limit(1)).withConverter(converter), (snapshot) => {
          if (!firstBatchLoaded) {
            firstBatchLoaded = true;
            return;
          }

          for (const change of snapshot.docChanges()) {
            if (change.type === 'added') {
              const newItem = change.doc.data();
              onItemAdded?.(newItem, [...items, newItem]);
              setItems((prevItems) => [newItem, ...prevItems]);
            }
          }
        });

    return () => unsubscribe();
  }, [initialLoad, items.length, order]);

  useEffect(() => {
    if (!!batchSize === !!itemHeight) {
      throw 'Exactly one of `batchSize` or `childHeight` must be provided.';
    }

    updateBatchSize();
    window.addEventListener('resize', updateBatchSize);

    return () => {
      window.removeEventListener('resize', updateBatchSize);
    };
  }, [itemHeight, batchSize]);

  async function fetchMore() {
    logger.debug('Fetching more items...');

    let startAfterFilter: QueryStartAtConstraint[] = [];
    if (items.length > 0) {
      const lastItem = Array.isArray(props.order) ? [...props.order.map((o) => items[items.length - 1][o.sortField])] : [items[items.length - 1][props.order.sortField]];
      startAfterFilter = [startAfter(...lastItem)];
    }

    const q = query(
      col,
      ...filters,
      ...order,
      ...startAfterFilter,
      limit(batch),
    ).withConverter(converter);

    const snapshot = await getDocs(q);
    logger.debug(`Fetched ${snapshot.size} item(s).`);

    if (snapshot.size < batch) {
      setHasMore(false);
    }

    const fetchedItems = snapshot.docs.map((docSnap) => docSnap.data());

    const allItems = [...items, ...fetchedItems];
    setItems(allItems);
    onFetchMore?.(fetchedItems, allItems);
  }

  if (initialLoad) {
    return loadingIndicator || <LoadingIndicator size={'md'} />;
  }

  if (items.length === 0 && noItemsPlaceholder) {
    return noItemsPlaceholder;
  }

  return (
    <InfiniteScroll
      next={fetchMore}
      hasMore={hasMore}
      loader={
        <LoadingIndicator
          size={'md'}
          sx={{mt: 3, mb: 3}}
        />
      }
      dataLength={items.length}
      height={height}
      inverse={inverse}
      scrollableTarget={scrollFullPage ? 'pageShell' : undefined}
      style={sx}
    >
      {items.map((item, i) => itemBuilder(item, i, items.length))}
    </InfiniteScroll>
  );
}
