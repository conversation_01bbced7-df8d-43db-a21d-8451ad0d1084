#!/bin/bash

# Fetch secrets from .env.example
SECRETS=$(grep -o '^[^#=]*' .env.example | sed '/^$/d')
NUM_SECRETS=$(echo "$SECRETS" | wc -l | xargs)

# Clear .env files
> .env
> admin/.env
> common/.env
> common-components/.env
> production/.env

# Prepare secrets list for GitHub workflows
echo "Fetching $NUM_SECRETS secrets from Google Secret Manager..."
for SECRET in $SECRETS
do
    # Fetch the secret from Google Secret Manager and write to .env files
    VALUE=$(gcloud secrets versions access latest --secret="$SECRET" --project creator-campus-app)
    echo "$SECRET=$VALUE" >> .env
    echo "$SECRET=$VALUE" >> admin/.env
    echo "$SECRET=$VALUE" >> common/.env
    echo "$SECRET=$VALUE" >> common-components/.env
    echo "$SECRET=$VALUE" >> production/.env
done

echo "Secrets synced to .env file."

# Check if common/.env.local exists, and if so, whether REACT_APP_DEV_NAME is already set
if [[ -f common/.env.local ]]; then
    EXISTING_DEV_NAME=$(grep -E "^REACT_APP_DEV_NAME=" common/.env.local | cut -d'=' -f2)
else
    EXISTING_DEV_NAME=""
fi

# If REACT_APP_DEV_NAME is already set, use it. Otherwise, get the user's input
if [[ -n $EXISTING_DEV_NAME ]]; then
    echo "Using existing developer name: $EXISTING_DEV_NAME"
    DEV_NAME=$EXISTING_DEV_NAME
else
    echo "Please enter your developer name (3-20 chars). Make sure it's something unique: "
    while true; do
        read DEV_NAME

        # Check if the length of the input is within the required range
        if [[ ${#DEV_NAME} -ge 3 && ${#DEV_NAME} -le 20 ]]; then
            break
        else
            echo "Must be between 3 and 20 characters. Try again: "
        fi
    done

    echo ""
    echo "Thanks, $DEV_NAME!"
    echo "Updating common .env.local file..."

    # Clear .env.local file
    > common/.env.local
    echo "REACT_APP_DEV_NAME=$DEV_NAME" >> common/.env.local
fi

# Update value of ALGOLIA_INDEX_NAME in extensions configurations
# (each developer uses a different Algolia index when running locally)
echo "Updating extension configurations..."
> extensions/firestore-algolia-search-people.env.local
echo "ALGOLIA_INDEX_NAME=creator_campus_firestore_people_$DEV_NAME" >> extensions/firestore-algolia-search-people.env.local
> extensions/firestore-algolia-search-projects.env.local
echo "ALGOLIA_INDEX_NAME=creator_campus_firestore_projects_$DEV_NAME" >> extensions/firestore-algolia-search-projects.env.local

cp common/.env.local admin/.env.local
cp common/.env.local production/.env.local

echo ""
echo "Your environment is good to go!"
