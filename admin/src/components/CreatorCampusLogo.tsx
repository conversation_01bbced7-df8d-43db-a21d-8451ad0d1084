import {useTheme} from '@mui/material/styles';
import LogoLight from '../assets/logo-light.png';
import LogoDark from '../assets/logo-dark.png';

interface Props {
  size?: string;
}

export function CreatorCampusLogo({size = '45px'}: Props) {
  return (
    <a
      href='https://www.creatorcampus.io/'
      target='_blank'
    >
      <img
        src={useTheme().palette.mode === 'light' ? LogoLight : LogoDark}
        alt='Orange Creator Campus Logo'
        style={{height: size}}
      />
    </a>
  );
}
