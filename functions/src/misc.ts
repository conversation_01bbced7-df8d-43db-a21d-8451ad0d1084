import {onCall} from 'firebase-functions/v2/https';
import {config} from 'dotenv';
import urlMetadata from 'url-metadata';
import {onSchedule} from 'firebase-functions/v2/scheduler';
import * as admin from 'firebase-admin';
import {Timestamp} from 'firebase-admin/firestore';
import {ProjectDbModelSafe, UserDbModelSafe} from '@creator-campus/common';
import {initSentryForFunction} from './utils';
import * as Sentry from '@sentry/node';

// Load .env if running locally
const usingEmulators = !!process.env.FIRESTORE_EMULATOR_HOST;
if (usingEmulators) {
  config();
}

interface GetUrlMetadataPayload {
  url: string;
}

export const getUrlMetadata = onCall<GetUrlMetadataPayload, Promise<object | null>>({minInstances: 1}, async (request) => {
  initSentryForFunction('getUrlMetadata');

  const {url} = request.data;

  Sentry.addBreadcrumb({
    message: 'Fetching URL metadata',
    category: 'function',
    level: 'info',
    data: {
      url,
      authUid: request.auth?.uid
    }
  });

  // Parse the URL to extract hostname
  let parsedUrl: URL;
  let hostName: string;

  try {
    parsedUrl = new URL(url);
    hostName = parsedUrl.hostname; // e.g., "example.com"

    Sentry.addBreadcrumb({
      message: 'Successfully parsed URL',
      category: 'validation',
      level: 'info',
      data: {
        url,
        hostname: hostName,
        protocol: parsedUrl.protocol
      }
    });
  } catch (parseError) {
    Sentry.addBreadcrumb({
      message: 'Failed to parse URL',
      category: 'validation',
      level: 'error',
      data: {
        url,
        error: parseError instanceof Error ? parseError.message : 'Unknown error'
      }
    });

    console.error('URL parsing error:', parseError);
    return null;
  }

  try {
    Sentry.addBreadcrumb({
      message: 'Fetching metadata from URL',
      category: 'external',
      level: 'info',
      data: { url, hostname: hostName }
    });

    const metadata = await urlMetadata(url);

    const result = {
      title: metadata.title || null,
      description: metadata.description || metadata.title || null,
      image: metadata.image || metadata['og:image'] || '',
      siteName: metadata['og:site_name'] || metadata.title || null,
      hostname: hostName,
    };

    Sentry.addBreadcrumb({
      message: 'Successfully fetched URL metadata',
      category: 'external',
      level: 'info',
      data: {
        url,
        hostname: hostName,
        hasTitle: !!result.title,
        hasDescription: !!result.description,
        hasImage: !!result.image,
        hasSiteName: !!result.siteName
      }
    });

    return result;
  } catch (e) {
    console.error(e);

    Sentry.addBreadcrumb({
      message: 'Failed to fetch URL metadata',
      category: 'external',
      level: 'error',
      data: {
        url,
        hostname: hostName,
        error: e instanceof Error ? e.message : 'Unknown error'
      }
    });

    return null;
  }
});

export const cleanUpFeaturedContent = onSchedule('every day 00:00', async () => {
  initSentryForFunction('cleanUpFeaturedContent');

  Sentry.addBreadcrumb({
    message: 'Starting cleanup of expired featured content',
    category: 'scheduler',
    level: 'info'
  });

  let totalProcessed = 0;
  let totalUnfeatured = 0;
  let totalRemaining = 0;

  for (const col of ['projects', 'people']) {
    console.log(`Checking ${col}...`);

    Sentry.addBreadcrumb({
      message: `Processing featured content in ${col} collection`,
      category: 'cleanup',
      level: 'info',
      data: { collection: col }
    });

    const snapshot = await admin.firestore().collection(col).where('featuredUntil', '!=', null).get();
    console.log(`Found ${snapshot.size} featured docs to check.`);

    Sentry.addBreadcrumb({
      message: `Found ${snapshot.size} featured documents to check`,
      category: 'cleanup',
      level: 'info',
      data: { collection: col, featuredCount: snapshot.size }
    });

    const now = new Date();
    let collectionUnfeatured = 0;
    let collectionRemaining = 0;

    for (const doc of snapshot.docs) {
      const data = doc.data() as UserDbModelSafe | ProjectDbModelSafe;
      const featuredUntil = data.featuredUntil as Timestamp;

      if (featuredUntil.toDate() < now) {
        console.log(`${doc.id} will no longer featured.`);

        Sentry.addBreadcrumb({
          message: `Unfeaturing expired content: ${doc.id}`,
          category: 'cleanup',
          level: 'info',
          data: {
            collection: col,
            documentId: doc.id,
            featuredUntil: featuredUntil.toDate().toISOString(),
            expired: true
          }
        });

        await doc.ref.update({
          featuredUntil: null,
        });

        collectionUnfeatured++;
      } else {
        console.log(`${doc.id} will remain featured.`);

        Sentry.addBreadcrumb({
          message: `Content remains featured: ${doc.id}`,
          category: 'cleanup',
          level: 'info',
          data: {
            collection: col,
            documentId: doc.id,
            featuredUntil: featuredUntil.toDate().toISOString(),
            expired: false
          }
        });

        collectionRemaining++;
      }
    }

    totalProcessed += snapshot.size;
    totalUnfeatured += collectionUnfeatured;
    totalRemaining += collectionRemaining;

    Sentry.addBreadcrumb({
      message: `Completed processing ${col} collection`,
      category: 'cleanup',
      level: 'info',
      data: {
        collection: col,
        totalChecked: snapshot.size,
        unfeatured: collectionUnfeatured,
        remaining: collectionRemaining
      }
    });
  }

  Sentry.addBreadcrumb({
    message: 'Completed cleanup of expired featured content',
    category: 'scheduler',
    level: 'info',
    data: {
      totalProcessed,
      totalUnfeatured,
      totalRemaining,
      collections: ['projects', 'people']
    }
  });
});
