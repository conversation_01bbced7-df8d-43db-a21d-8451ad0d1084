import Box from '@mui/joy/Box';
import {PageHeader, PageShell, Tabs, useUser} from '@creator-campus/common-components';
import {useParams} from 'react-router-dom';
import ProfileAccountSettingsTab from '../components/ProfileAccountSettingsTab.tsx';
import ProfileAboutYouTab from '../components/ProfileAboutYouTab.tsx';
import {Person, Settings} from '@mui/icons-material';

export default function ProfilePage() {
  const {user} = useUser();
  const params = useParams();

  if (!user) {
    return <></>;
  }

  return (
    <>
      <PageShell banners={{activateCommunity: {hideChildrenIfShown: false}}}>
        <Box
          sx={{
            px: {xs: 2, md: 6},
          }}
        >
          <PageHeader
            title='My Profile'
            subtitle={user.hasFullAccessToApp() ? '' : 'Complete your profile to unlock full access to the platform.'}
          />
        </Box>
        <Tabs
          value={`/profile/${params.path}`}
          tabs={[
            {
              leadingIcon: <Person />,
              displayName: 'Profile',
              path: '/profile/about',
              content: <ProfileAboutYouTab user={user} />,
            },
            {
              leadingIcon: <Settings />,
              displayName: 'Settings',
              path: '/profile/account',
              content: <ProfileAccountSettingsTab />,
            },
          ]}
        />
      </PageShell>
    </>
  );
}
