import * as React from 'react';
import {useState} from 'react';
import Button from '@mui/joy/Button';
import Typography from '@mui/joy/Typography';
import FormControl from '@mui/joy/FormControl';
import FormLabel from '@mui/joy/FormLabel';
import Tooltip from '@mui/joy/Tooltip';
import Link from '@mui/joy/Link';

import {isUrlValid, logger, Project, ProjectTag, User} from '@creator-campus/common';
import {LoadingIndicator, useNewProjectModalString, useSnackbar, useUser} from '@creator-campus/common-components';
import Textarea from '@mui/joy/Textarea';
import {Autocomplete, FormHelperText, Input, Stack} from '@mui/joy';
import FileUploadButton from './FileUploadButton.tsx';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import {normaliseUrl} from '../utils.tsx';
import {arrayUnion, updateDoc} from 'firebase/firestore';

interface Props {
  initialProject?: Project;
  onSave: (event: 'add' | 'edit', createdProject: Project | null) => Promise<void> | void;
  onClose: () => void;
  hiddenProject?: boolean;
}

export default function CreateEditProjectForm({initialProject, onSave, onClose, hiddenProject = false}: Props) {
  const [name, setName] = useState(initialProject?.name || '');
  const [summary, setSummary] = useState(initialProject?.summary || '');
  const [description, setDescription] = useState(initialProject?.description || '');
  const [tag, setTag] = useState<ProjectTag | null>(initialProject?.tags[0] || null);
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoFileChosen, setLogoFileChosen] = useState<boolean>(!!initialProject);
  const [website, setWebsite] = useState(initialProject?.website || '');

  const [nameOkay, setNameOkay] = useState(true);
  const [summaryOkay, setSummaryOkay] = useState(true);
  const [descriptionOkay, setDescriptionOkay] = useState(true);
  const [tagOkay, setTagOkay] = useState(true);
  const [logoOkay, setLogoOkay] = useState(true);
  const [websiteOkay, setWebsiteOkay] = useState(true);

  const [loading, setLoading] = useState(false);
  const [hasBeenEdited, setHasBeenEdited] = useState(false);

  const {showErrorSnackbar} = useSnackbar();
  const {user} = useUser();

  function handleNameChange(event: React.ChangeEvent<HTMLInputElement>) {
    setName(event.target.value);
    setNameOkay(true);
    setHasBeenEdited(true);
  }

  function handleSummaryChange(event: React.ChangeEvent<HTMLInputElement>) {
    setSummary(event.target.value);
    setSummaryOkay(true);
    setHasBeenEdited(true);
  }

  function handleDescriptionChange(event: React.ChangeEvent<HTMLTextAreaElement>) {
    setDescription(event.target.value);
    setDescriptionOkay(true);
    setHasBeenEdited(true);
  }

  function handleTagChange(value: ProjectTag | null) {
    if (value) {
      setTag(value);
      setTagOkay(true);
      setHasBeenEdited(true);
    }
  }

  function handleWebsiteChange(event: React.ChangeEvent<HTMLInputElement>) {
    setWebsite(event.target.value);
    setDescriptionOkay(true);
    setHasBeenEdited(true);
  }

  function checkFormValid() {
    const isNameOkay = name.length > 0 && name.length <= Project.MAX_NAME_LEN;
    const isSummaryOkay = summary.length > 0 && summary.length <= Project.MAX_SUMMARY_LEN;
    const isDescriptionOkay = description.length > 0 && description.length <= Project.MAX_DESC_LEN;
    const areTagsOkay = tag !== null;
    const isLogoOkay = logoFileChosen;
    const isWebsiteOkay = website === '' || isUrlValid(website);

    setNameOkay(isNameOkay);
    setSummaryOkay(isSummaryOkay);
    setDescriptionOkay(isDescriptionOkay);
    setTagOkay(areTagsOkay);
    setLogoOkay(isLogoOkay);
    setWebsiteOkay(isWebsiteOkay);

    return isNameOkay && isSummaryOkay && isDescriptionOkay && areTagsOkay && isLogoOkay && isWebsiteOkay;
  }

  async function uploadLogo(project: Project, file: File) {
    return await project
      .updateLogo(file)
      .then(() => {
        return true;
      })
      .catch((e) => {
        showErrorSnackbar(e, 'Error uploading logo.');
        return false;
      });
  }

  async function handleUpdateProject(initialProject: Project, websiteUrl: string) {
    const success = await initialProject
      .update(name, summary, description, [tag!], websiteUrl)
      .then(() => {
        return true;
      })
      .catch((e) => {
        logger.error(e);
        showErrorSnackbar(e, 'Error updating project.');
        return false;
      });

    if (!success) {
      return;
    }

    if (logoFile) {
      const uploadSuccess = await uploadLogo(initialProject, logoFile);
      if (!uploadSuccess) {
        return;
      }
    }

    setHasBeenEdited(false);
    onSave('edit', null);
    onClose();
  }

  async function handleCreateProject(websiteUrl: string) {
    const project = await Project.create(user!, name, summary, description, [tag!], websiteUrl, hiddenProject).catch((e) => {
      logger.error(e);
      showErrorSnackbar(e, 'Error creating project.');
      return null;
    });

    if (project) {
      const uploadSuccess = await uploadLogo(project, logoFile!);

      await updateDoc(User.doc(user!.id), {
        projectIds: arrayUnion(project.id),
      });

      // Wait to ensure logo is uploaded and shows up when modal closes
      await new Promise((f) => setTimeout(f, 100));

      if (uploadSuccess) {
        setHasBeenEdited(false);
        onSave('add', project);
        onClose();
      } else {
        await project.delete();
      }
    }
  }

  async function handleSubmit() {
    if (!hasBeenEdited) {
      onClose();
      return;
    }

    if (checkFormValid()) {
      const normalisedWebsiteUrl = normaliseUrl(website)!;

      if (initialProject) {
        await handleUpdateProject(initialProject, normalisedWebsiteUrl);
      } else {
        await handleCreateProject(normalisedWebsiteUrl);
      }
    }
  }

  return (
    <>
      <FormControl
        sx={{mt: 3}}
        error={!nameOkay}
      >
        <FormLabel>{useNewProjectModalString().project_name}</FormLabel>
        <Input
          placeholder='Name'
          variant='outlined'
          onChange={(event) => handleNameChange(event)}
          defaultValue={name}
          endDecorator={
            <Typography
              level='body-xs'
              sx={{ml: 'auto'}}
            >
              {name.length} / {Project.MAX_NAME_LEN}
            </Typography>
          }
        />
        {nameOkay ? <></> : <FormHelperText sx={{mt: 0.75, fontSize: 'xs'}}>Name must be between 1 and {Project.MAX_NAME_LEN} characters.</FormHelperText>}
      </FormControl>
      <FormControl
        sx={{mt: 1}}
        error={!summaryOkay}
      >
        <FormLabel>{useNewProjectModalString().short_summary}</FormLabel>
        <Input
          placeholder='A one sentence description of your project.'
          variant='outlined'
          onChange={(event) => handleSummaryChange(event)}
          defaultValue={summary}
          endDecorator={
            <Typography
              level='body-xs'
              sx={{ml: 'auto'}}
            >
              {summary.length} / {Project.MAX_SUMMARY_LEN}
            </Typography>
          }
        />
        {summaryOkay ? <></> : <FormHelperText sx={{mt: 0.75, fontSize: 'xs'}}>Summary must be between 1 and {Project.MAX_SUMMARY_LEN} characters.</FormHelperText>}
      </FormControl>
      <FormControl
        sx={{mt: 1}}
        error={!descriptionOkay}
      >
        <FormLabel>
          {useNewProjectModalString().longer_description}
          <Tooltip
            enterTouchDelay={0}
            title={useNewProjectModalString().description_guidance}
            variant='solid'
          >
            <InfoOutlinedIcon fontSize='large' />
          </Tooltip>
        </FormLabel>
        <Textarea
          placeholder='Description'
          variant='outlined'
          minRows={3}
          maxRows={5}
          defaultValue={description}
          onChange={(event) => handleDescriptionChange(event)}
          endDecorator={
            <Typography
              level='body-xs'
              sx={{ml: 'auto'}}
            >
              {description.length} / {Project.MAX_DESC_LEN}
            </Typography>
          }
        />
        {descriptionOkay ? <></> : <FormHelperText sx={{mt: 0.75, fontSize: 'xs'}}>Description must be between 1 and {Project.MAX_DESC_LEN} characters.</FormHelperText>}
      </FormControl>
      <FormControl
        sx={{mt: 1}}
        error={!websiteOkay}
      >
        <FormLabel>{useNewProjectModalString().website_optional}</FormLabel>
        <Input
          placeholder='Website URL'
          variant='outlined'
          defaultValue={website}
          onChange={(event) => handleWebsiteChange(event)}
        />
        {!websiteOkay ? <FormHelperText>{useNewProjectModalString().invalid_url}</FormHelperText> : <></>}
      </FormControl>
      <FormControl
        sx={{mt: 1}}
        error={!tagOkay}
      >
        <FormLabel sx={{mb: 1}}>{useNewProjectModalString().category}</FormLabel>
        <Autocomplete
          placeholder='Select category'
          options={ProjectTag.values()}
          defaultValue={tag}
          value={tag}
          onChange={(_, value) => handleTagChange(value)}
        />
        {!tagOkay ? <FormHelperText>You must select a category.</FormHelperText> : <></>}
      </FormControl>
      <FormControl
        sx={{mt: 2, mb: 2}}
        error={!logoOkay}
      >
        <FileUploadButton
          alreadyChosen={logoFileChosen}
          onFileChosen={(file) => {
            setLogoFileChosen(true);
            setLogoFile(file);
            setHasBeenEdited(true);
            setLogoOkay(true);
          }}
        />
        <Typography level='body-sm'>
          {' '}
          If you don't have a logo you can use{' '}
          <Link
            href='https://www.adobe.com/express/create/logo'
            target='_blank'
          >
            Adobe Logo Generator
          </Link>{' '}
          to create something temporary, or visit{' '}
          <Link
            href='https://unsplash.com/'
            target='_blank'
          >
            Unsplash.
          </Link>
        </Typography>
        {!logoOkay ? <FormHelperText>You must upload a logo.</FormHelperText> : <></>}
      </FormControl>
      <Stack
        direction={'row'}
        spacing={1}
        sx={{mt: 2, justifyContent: 'start'}}
      >
        <Button
          disabled={loading}
          color='primary'
          variant='solid'
          onClick={async () => {
            setLoading(true);
            await handleSubmit();
            setLoading(false);
          }}
        >
          {loading ? <LoadingIndicator size='sm' /> : 'Save'}
        </Button>
        <Button
          variant='outlined'
          color='neutral'
          onClick={() => {
            if (!hasBeenEdited || confirm(`Are you sure you want to cancel? Any unsaved changes will be lost.`)) {
              onClose();
            }
          }}
        >
          Cancel
        </Button>
      </Stack>
    </>
  );
}
