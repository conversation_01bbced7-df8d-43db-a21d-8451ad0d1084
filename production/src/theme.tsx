import type {PaletteRange} from '@mui/joy/styles';
import {extendTheme} from '@mui/joy/styles';

declare module '@mui/joy/styles' {
  interface ColorPalettePropOverrides {
    // apply to all Joy UI components that support `color` prop
    custom_blue: true;
    karma_green: true;
    karma_blue: true;
    white: true;
    primary_soft: true;
  }

  interface Palette {
    // this will make the node `secondary` configurable in `extendTheme`
    // and add `secondary` to the theme's palette.
    custom_blue: PaletteRange;
    karma_green: PaletteRange;
    karma_blue: PaletteRange;
    white: PaletteRange;
    primary_soft: PaletteRange;
  }
}

const theme = extendTheme({
  components: {
    JoyInput: {
      styleOverrides: {
        root: ({theme}) => ({
          [theme.breakpoints.only('xs')]: {
            fontSize: 16,
          },
          height: 40,
          [theme.breakpoints.up('sm')]: {
            height: 30,
          },
        }),
      },
    },
    JoyTextarea: {
      styleOverrides: {
        root: () => ({
          [theme.breakpoints.only('xs')]: {
            fontSize: 16,
          },
        }),
      },
    },
    JoySelect: {
      styleOverrides: {
        root: ({theme}) => ({
          [theme.breakpoints.only('xs')]: {
            fontSize: 16,
          },
          height: 40,
          [theme.breakpoints.up('sm')]: {
            height: 30,
          },
        }),
      },
    },
  },
  colorSchemes: {
    light: {
      palette: {
        background: {
          body: '#ffffff',
        },
        primary: {
          '50': '#fff1eb',
          '100': '#fac5af',
          '200': '#faa27d',
          '300': '#f79363',
          '400': '#f5773b',
          '500': '#f6651f',
          '600': '#db4e09',
          '700': '#ab3b05',
          '800': '#702601',
          '900': '#3d1400',
        },
        primary_soft: {
          '50': '#fff7f4',
          '100': '#fde4dc',
          '200': '#fbc9b7',
          '300': '#fab69d',
          '400': '#f79d7a',
          '500': '#f58962',
          '600': '#d96e49',
          '700': '#ab5536',
          '800': '#723822',
          '900': '#3f1d10',
          solidBg: 'var(--joy-palette-primary_soft-400)',
          solidActiveBg: 'var(--joy-palette-primary_soft-500)',
          outlinedBorder: 'var(--joy-palette-primary_soft-500)',
          outlinedColor: 'var(--joy-palette-primary_soft-700)',
          outlinedActiveBg: 'var(--joy-palette-primary_soft-100)',
          softColor: 'var(--joy-palette-primary_soft-800)',
          softBg: 'var(--joy-palette-primary_soft-200)',
          softActiveBg: 'var(--joy-palette-primary_soft-300)',
          plainColor: 'var(--joy-palette-primary_soft-700)',
          plainActiveBg: 'var(--joy-palette-primary_soft-100)',
        },
        custom_blue: {
          // Credit:
          // https://github.com/tailwindlabs/tailwindcss/blob/master/src/public/colors.js
          '50': '#ecfdf5',
          '100': '#d1fae5',
          '200': '#a7f3d0',
          '300': '#6ee7b7',
          '400': '#34d399',
          '500': '#10b981',
          '600': '#059669',
          '700': '#047857',
          '800': '#065f46',
          '900': '#064e3b',
          // Adjust the global variant tokens as you'd like.
          // The tokens should be the same for all color schemes.
          solidBg: 'var(--joy-palette-custom_blue-400)',
          solidActiveBg: 'var(--joy-palette-custom_blue-500)',
          outlinedBorder: 'var(--joy-palette-custom_blue-500)',
          outlinedColor: 'var(--joy-palette-custom_blue-700)',
          outlinedActiveBg: 'var(--joy-palette-custom_blue-100)',
          softColor: 'var(--joy-palette-custom_blue-800)',
          softBg: 'var(--joy-palette-custom_blue-200)',
          softActiveBg: 'var(--joy-palette-custom_blue-300)',
          plainColor: 'var(--joy-palette-custom_blue-700)',
          plainActiveBg: 'var(--joy-palette-custom_blue-100)',
        },
        karma_green: {
          '50': '#f5fef9',
          '100': '#e6fcf0',
          '200': '#c3f9dc',
          '300': '#99f2c7',
          '400': '#67e8ae',
          '500': '#34d69a',
          '600': '#2cc986',
          '700': '#22b173',
          '800': '#19945f',
          '900': '#13754b',
          solidBg: 'var(--joy-palette-karma_green-400)',
          solidActiveBg: 'var(--joy-palette-karma_green-500)',
          outlinedBorder: 'var(--joy-palette-karma_green-500)',
          outlinedColor: 'var(--joy-palette-karma_green-700)',
          outlinedActiveBg: 'var(--joy-palette-karma_green-100)',
          softColor: 'var(--joy-palette-karma_green-800)',
          softBg: 'var(--joy-palette-karma_green-200)',
          softActiveBg: 'var(--joy-palette-karma_green-300)',
          plainColor: 'var(--joy-palette-karma_green-700)',
          plainActiveBg: 'var(--joy-palette-karma_green-100)',
        },
        karma_blue: {
          '50': '#eff6ff',
          '100': '#dbeafe',
          '200': '#bfdbfe',
          '300': '#93c5fd',
          '400': '#60a5fa',
          '500': '#3b82f6',
          '600': '#2563eb',
          '700': '#1d4ed8',
          '800': '#1e40af',
          '900': '#1e3a8a',
          solidBg: 'var(--joy-palette-karma_blue-400)',
          solidActiveBg: 'var(--joy-palette-karma_blue-500)',
          outlinedBorder: 'var(--joy-palette-karma_blue-500)',
          outlinedColor: 'var(--joy-palette-karma_blue-700)',
          outlinedActiveBg: 'var(--joy-palette-karma_blue-100)',
          softColor: 'var(--joy-palette-karma_blue-800)',
          softBg: 'var(--joy-palette-karma_blue-200)',
          softActiveBg: 'var(--joy-palette-karma_blue-300)',
          plainColor: 'var(--joy-palette-karma_blue-700)',
          plainActiveBg: 'var(--joy-palette-karma_blue-100)',
        },
        white: {
          '50': '#fff',
          solidBg: '#fff', // White background for solid variant
          solidActiveBg: '#f0f0f0', // Slightly darker white for active state
          outlinedBorder: '#fff', // Border color for outlined variant
          outlinedColor: '#000', // Text color for outlined variant
          outlinedActiveBg: '#f9f9f9', // Slightly darker for active outlined
          softColor: '#000', // Text color for soft variant
          softBg: '#f9f9f9', // Slightly gray background for soft variant
          softActiveBg: '#f0f0f0', // Active state for soft variant
          plainColor: '#000', // Text color for plain variant
          plainActiveBg: '#f9f9f9', // Active state for plain variant
        },
      },
    },
    dark: {
      palette: {
        primary: {
          '50': '#fff1eb',
          '100': '#fac5af',
          '200': '#faa27d',
          '300': '#f79363',
          '400': '#f5773b',
          '500': '#f6651f',
          '600': '#db4e09',
          '700': '#ab3b05',
          '800': '#702601',
          '900': '#3d1400',
        },
        background: {
          body: '#1a1a1a',
          surface: '#242424',
        },
        neutral: {
          '800': '#242424',
        },
        custom_blue: {
          // Credit:
          // https://github.com/tailwindlabs/tailwindcss/blob/master/src/public/colors.js
          '50': '#ecfdf5',
          '100': '#d1fae5',
          '200': '#a7f3d0',
          '300': '#6ee7b7',
          '400': '#34d399',
          '500': '#10b981',
          '600': '#059669',
          '700': '#047857',
          '800': '#065f46',
          '900': '#064e3b',
          solidBg: 'var(--joy-palette-custom_blue-400)',
          solidActiveBg: 'var(--joy-palette-custom_blue-500)',
          outlinedBorder: 'var(--joy-palette-custom_blue-500)',
          outlinedColor: 'var(--joy-palette-custom_blue-700)',
          outlinedActiveBg: 'var(--joy-palette-custom_blue-100)',
          softColor: 'var(--joy-palette-custom_blue-800)',
          softBg: 'var(--joy-palette-custom_blue-200)',
          softActiveBg: 'var(--joy-palette-custom_blue-300)',
          plainColor: 'var(--joy-palette-custom_blue-700)',
          plainActiveBg: 'var(--joy-palette-custom_blue-100)',
        },
        karma_blue: {
          '50': '#eff6ff',
          '100': '#dbeafe',
          '200': '#bfdbfe',
          '300': '#93c5fd',
          '400': '#60a5fa',
          '500': '#3b82f6',
          '600': '#2563eb',
          '700': '#1d4ed8',
          '800': '#1e40af',
          '900': '#1e3a8a',
          solidBg: 'var(--joy-palette-karma_blue-400)',
          solidActiveBg: 'var(--joy-palette-karma_blue-500)',
          outlinedBorder: 'var(--joy-palette-karma_blue-500)',
          outlinedColor: 'var(--joy-palette-karma_blue-700)',
          outlinedActiveBg: 'var(--joy-palette-karma_blue-100)',
          softColor: 'var(--joy-palette-karma_blue-800)',
          softBg: 'var(--joy-palette-karma_blue-200)',
          softActiveBg: 'var(--joy-palette-karma_blue-300)',
          plainColor: 'var(--joy-palette-karma_blue-700)',
          plainActiveBg: 'var(--joy-palette-karma_blue-100)',
        },
        white: {
          '50': '#fff',
          solidBg: '#fff',
          solidActiveBg: '#f0f0f0',
          solidColor: '#fff',
          outlinedBorder: '#fff',
          outlinedColor: '#000',
          outlinedActiveBg: '#333',
          softColor: '#fff',
          softBg: '#444',
          softActiveBg: '#555',
          plainColor: '#fff',
          plainActiveBg: '#444',
        },
      },
    },
  },
});

export default theme;
