import Avatar from '@mui/joy/Avatar';
import Box from '@mui/joy/Box';
import {Button, Textarea} from '@mui/joy';
import Typography from '@mui/joy/Typography';
import {ChosenDiscussionAttachment, DiscussionAttachment, DiscussionAttachmentType, DiscussionComment, DiscussionPost, generateRandomId} from '@creator-campus/common';
import {LoadingIndicator, useSnackbar, useUser} from '@creator-campus/common-components';
import FormControl from '@mui/joy/FormControl';
import {ChangeEvent, KeyboardEvent, useRef, useState} from 'react';
import IconButton from '@mui/joy/IconButton';
import {AddPhotoAlternate, AttachFile} from '@mui/icons-material';
import {VisuallyHiddenInput} from './FileUploadButton.tsx';
import Stack from '@mui/joy/Stack';
import DiscussionAttachmentCard from './DiscussionAttachmentCard.tsx';
import SendIcon from '@mui/icons-material/Send';
import KarmaChip from './KarmaChip.tsx';
import {discussionFeedWidth} from './DiscussionContent.tsx';
import Tooltip from '@mui/joy/Tooltip';

interface Props {
  profilePicUrl?: string;
  post?: DiscussionPost;
  universityId?: string;
}

export default function DiscussionInput({profilePicUrl, post, universityId}: Props) {
  const maxPostLength = post ? 800 : 1200;
  const fileLimits: Record<DiscussionAttachmentType, number> = {
    image: 10,
    attachment: 10,
  };

  const [inputText, setInputText] = useState<string>('');
  const [sendLoading, setSendLoading] = useState<boolean>(false);

  const [chosenAttachments, setChosenAttachments] = useState<ChosenDiscussionAttachment[]>([]);
  const [uploadedAttachmentIds, setUploadedAttachmentIds] = useState<string[]>([]);

  const inputElementRef = useRef<HTMLTextAreaElement>(null);
  const {showErrorSnackbar, showSnackbar} = useSnackbar();
  const {user, university} = useUser();

  if (!user || !university) {
    return <></>;
  }

  const allAttachmentsUploaded = chosenAttachments.every((a) => uploadedAttachmentIds.includes(a.id));
  const sendDisabled = sendLoading || !allAttachmentsUploaded || !user.hasFullAccessToApp() || (!!universityId && !university.partner);

  function handleFilesChosen(e: ChangeEvent<HTMLInputElement>, fileType: DiscussionAttachmentType) {
    const newFiles = e.target.files;
    if (!newFiles || newFiles.length === 0) {
      return;
    }

    // Enforce file limit
    const limit = fileLimits[fileType];
    if (newFiles.length + chosenAttachments.length > limit) {
      showSnackbar(`Max ${limit} ${fileType}s per post.`, 'danger');
      return;
    }

    // Update state with the new files
    const filesToAdd: ChosenDiscussionAttachment[] = [...newFiles].map((file) => ({
      id: generateRandomId(),
      name: file.name,
      file,
      type: fileType,
    }));

    setChosenAttachments((prev) => [...prev, ...filesToAdd]);
  }

  async function handleSend() {
    const text = inputText.trim();
    if (text.length === 0 && chosenAttachments.length === 0) {
      return;
    }

    setSendLoading(true);

    const attachments: DiscussionAttachment[] = chosenAttachments.map((a) => ({id: a.id, name: a.name, type: a.type}));

    const creationPromise = post ? post.addComment(DiscussionComment.create(user!, post.id, text, attachments, universityId)) : DiscussionPost.addNew(DiscussionPost.create(user!, text, attachments, universityId));

    await creationPromise
      .then(() => {
        setInputText('');
        setChosenAttachments([]);
        setUploadedAttachmentIds([]);
      })
      .catch((e) => showErrorSnackbar(e, 'Error adding post.'))
      .finally(() => setSendLoading(false));
  }

  async function handleInputKeyDown(event: KeyboardEvent) {
    const isCommandOrControl = event.metaKey || event.ctrlKey;
    const isEnterKey = event.key === 'Enter';

    if (isCommandOrControl && isEnterKey) {
      await handleSend();
    }
  }

  return (
    <FormControl sx={{display: 'flex', flexDirection: 'row', mt: 3, maxWidth: post ? undefined : discussionFeedWidth - 50, width: '100%', justifySelf: 'center'}}>
      <Stack
        spacing={1}
        alignItems={'center'}
      >
        <Avatar
          src={profilePicUrl}
          alt={user.name}
          size='lg'
        />
        <KarmaChip karma={user.karma} />
      </Stack>
      <Box sx={{flex: 1, ml: 2, mr: 2, zIndex: 500, textAlign: 'end'}}>
        <Textarea
          placeholder={post ? 'Add comment...' : 'Share something...'}
          value={inputText}
          onClick={() => inputElementRef.current?.focus()}
          onKeyDown={handleInputKeyDown}
          slotProps={{textarea: {ref: inputElementRef}}}
          onChange={(e) => {
            const newValue = e.target.value;
            if (newValue.length <= maxPostLength) {
              setInputText(newValue);
            }
          }}
          endDecorator={
            <Stack spacing={1}>
              {/*Image and attachment cards*/}
              {['image', 'attachment'].map((attachmentType) => (
                <Stack
                  key={attachmentType}
                  direction={'row'}
                  flexWrap={'wrap'}
                  gap={0.5}
                  alignItems={'center'}
                >
                  {chosenAttachments
                    .filter((a) => a.type === attachmentType)
                    .map((a) => (
                      <DiscussionAttachmentCard
                        key={a.id}
                        attachmentId={a.id}
                        fileType={a.type}
                        universityId={universityId}
                        uploadable={{
                          file: a.file,
                          removeFile: () => {
                            setChosenAttachments((prev) => prev.filter((p) => p.id !== a.id));
                            setUploadedAttachmentIds((prev) => prev.filter((id) => id !== a.id));
                          },
                          onUploadSuccess: () => {
                            setUploadedAttachmentIds((prev) => [...prev, a.id]);
                          },
                        }}
                      />
                    ))}
                </Stack>
              ))}

              {/*Attach image/file buttons*/}
              <Stack direction={'row'}>
                <IconButton component='label'>
                  <AddPhotoAlternate />
                  <VisuallyHiddenInput
                    type='file'
                    accept='image/*'
                    multiple
                    onChange={(e) => {
                      handleFilesChosen(e, 'image');
                      e.target.value = '';
                    }}
                  />
                </IconButton>
                <IconButton component='label'>
                  <AttachFile />
                  <VisuallyHiddenInput
                    type='file'
                    multiple
                    onChange={(e) => {
                      handleFilesChosen(e, 'attachment');
                      e.target.value = '';
                    }}
                  />
                </IconButton>
              </Stack>
            </Stack>
          }
        />
        <Typography
          level='body-xs'
          sx={{ml: 'auto'}}
        >
          {inputText.length} / {maxPostLength}
        </Typography>
      </Box>
      {post ? (
        <IconButton
          disabled={sendDisabled}
          color='primary'
          onClick={handleSend}
          sx={{width: 50, height: 37}}
        >
          {sendLoading ? <LoadingIndicator size='sm' /> : <SendIcon />}
        </IconButton>
      ) : (
        <Tooltip
          title={!user.hasFullAccessToApp() ? 'Complete your profile to post.' : ''}
          placement={'top'}
        >
          <Box>
            <Button
              color='primary'
              disabled={sendDisabled}
              onClick={handleSend}
              sx={{width: 75, height: 37}}
            >
              {sendLoading ? (
                <LoadingIndicator size='sm' />
              ) : (
                <Typography
                  color='white'
                  padding={0.9}
                >
                  Post
                </Typography>
              )}
            </Button>
          </Box>
        </Tooltip>
      )}
    </FormControl>
  );
}
