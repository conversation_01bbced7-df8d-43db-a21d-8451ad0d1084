import Input, {InputProps} from '@mui/joy/Input';
import Chip from '@mui/joy/Chip';
import {ReactNode} from 'react';
import {useScreenWidth} from '@creator-campus/common-components';

interface Props extends InputProps {
  chipIcon: ReactNode;
  chipText: string;
}

export function InputWithChip({chipIcon, chipText, ...inputProps}: Props) {
  const screenWidth = useScreenWidth();

  return (
    <Input
      {...inputProps}
      sx={{
        '--Input-paddingInline': '0px',
        maxWidth: screenWidth - 32,
      }}
      startDecorator={
        <Chip
          variant='soft'
          color='neutral'
          startDecorator={chipIcon}
          sx={{'--Chip-radius': '8px', height: '100%'}}
        >
          {chipText}
        </Chip>
      }
    />
  );
}
