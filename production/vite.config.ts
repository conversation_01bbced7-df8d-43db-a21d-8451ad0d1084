import {defineConfig, loadEnv} from 'vite';
import react from '@vitejs/plugin-react';
import {sentryVitePlugin} from '@sentry/vite-plugin';

const runningInDevMode = !!process.env.REACT_APP_DEV_NAME;

export default defineConfig(({mode}) => {
  const env = loadEnv(mode, process.cwd(), '');
  return {
    build: {
      sourcemap: true,
    },
    define: {
      'process.env': env,
    },
    plugins: [
      react(),
      sentryVitePlugin({
        authToken: process.env.SENTRY_AUTH_TOKEN,
        org: 'creator-campus',
        project: 'production',
        telemetry: false,
        disable: runningInDevMode,
      }),
    ],
  };
});
