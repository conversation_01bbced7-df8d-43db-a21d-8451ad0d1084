import Card from '@mui/joy/Card';
import Typography from '@mui/joy/Typography';
import Box from '@mui/joy/Box';
import {Stack} from '@mui/joy';
import {firestore, University, User} from '@creator-campus/common';
import {useSnackbar} from '@creator-campus/common-components';
import {useEffect, useState} from 'react';
import {collection, getCountFromServer, query, where} from 'firebase/firestore';
import UniversityFormModal from './UniversityFormModal.tsx';
import Chip from '@mui/joy/Chip';
import {Handshake, Palette, Payment, People, Person} from '@mui/icons-material';

interface Props {
  university: University;
}

export default function UniversityCard({university}: Props) {
  const [totalUsers, setTotalUsers] = useState<number | null>(null);
  const [showUniversityFormModal, setShowUniversityFormModal] = useState<boolean>(false);

  const {showSnackbar} = useSnackbar();

  useEffect(() => {
    getCountFromServer(query(collection(firestore(), User.collectionName), where('universityId', '==', university.id))).then((snapshot) => {
      setTotalUsers(snapshot.data().count);
    });
  }, []);

  return (
    <>
      {showUniversityFormModal && (
        <UniversityFormModal
          initialUniId={university.id}
          onSave={() => {
            showSnackbar('University saved.', 'success');
            setShowUniversityFormModal(false);
          }}
          onCancel={() => setShowUniversityFormModal(false)}
        />
      )}
      <Card
        onClick={() => setShowUniversityFormModal(true)}
        sx={{
          cursor: 'pointer',
          transition: 'background-color 0.2s',
          '&:hover': {
            backgroundColor: 'rgba(0, 0, 0, 0.05)',
          },
        }}
      >
        <Box sx={{display: 'flex', justifyContent: 'flex-start', alignItems: 'flex-start'}}>
          <Box sx={{display: 'flex', justifyContent: 'flex-start', alignItems: 'center'}}>
            <Stack sx={{ml: '10px'}}>
              <Typography level='title-lg'>{university.name}</Typography>
              <Stack
                direction={'row'}
                spacing={1}
              >
                <Typography level='body-sm'>Status:</Typography>
                {university.isPaying() ? (
                  <Chip
                    size={'sm'}
                    color={'success'}
                    startDecorator={<Payment />}
                  >
                    Subscribed
                  </Chip>
                ) : university.partner && university.reachedUserLimit ? (
                  <Chip
                    size={'sm'}
                    color={'danger'}
                    startDecorator={<People />}
                  >
                    Reached quota
                  </Chip>
                ) : university.partner && university.almostReachedUserLimit ? (
                  <Chip
                    size={'sm'}
                    color={'warning'}
                    startDecorator={<Person />}
                  >
                    Approaching quota
                  </Chip>
                ) : university.partner ? (
                  <Chip
                    size={'sm'}
                    color={'neutral'}
                    startDecorator={<Handshake />}
                  >
                    Free partner
                  </Chip>
                ) : university.branding ? (
                  <Chip
                    size={'sm'}
                    color={'neutral'}
                    startDecorator={<Palette />}
                  >
                    Branding is set up
                  </Chip>
                ) : (
                  <Chip
                    size={'sm'}
                    color={'neutral'}
                  >
                    Not partnered
                  </Chip>
                )}
              </Stack>
              <Typography level='body-sm'>{totalUsers !== null ? `${totalUsers} user${totalUsers === 1 ? '' : 's'}` : '...'}</Typography>
            </Stack>
          </Box>
        </Box>
      </Card>
    </>
  );
}
