import {onCall} from 'firebase-functions/v2/https';
import * as admin from 'firebase-admin';
import {onDocumentUpdated} from 'firebase-functions/v2/firestore';
import {AdminEmailTemplate, MaintenanceModeDbModelSafe, CREATOR_CAMPUS_ADMINS} from '@creator-campus/common';
import {Timestamp} from 'firebase-admin/firestore';
import {onSchedule} from 'firebase-functions/v2/scheduler';
import {emailCreatorCampusAdmins, sendEmail} from './utils';

interface ToggleMaintenanceModePayload {
  userEmail: string;
  enabled: boolean;
}

export const toggleMaintenanceMode = onCall<ToggleMaintenanceModePayload>({cors: true}, async (request) => {
  const requestAuth = request.auth?.token;

  if (!requestAuth?.email || !requestAuth?.email_verified || !CREATOR_CAMPUS_ADMINS.includes(requestAuth.email)) {
    console.log(`Unauthorized email: ${requestAuth?.email}`);
    return 'Unauthorized.';
  }

  const data = request.data;

  await admin.firestore().collection('config').doc('maintenanceMode').update({
    enabled: data.enabled,
  });
});

export const onMaintenanceModeUpdated = onDocumentUpdated('config/maintenanceMode', (event) => {
  if (!event.data) {
    console.error('Event data is undefined!');
    return;
  }

  const beforeData = event.data.before.data() as MaintenanceModeDbModelSafe;
  const afterData = event.data.after.data() as MaintenanceModeDbModelSafe;

  const justToggled = beforeData.enabled !== afterData.enabled;

  if (justToggled) {
    return event.data.after.ref.update({
      lastToggledTs: Timestamp.now(),
    });
  }

  return null;
});

export const monitorMaintenanceMode = onSchedule('every 2 hours', async () => {
  const snapshot = await admin.firestore().collection('config').doc('maintenanceMode').get();
  const maintenanceMode = snapshot.data() as MaintenanceModeDbModelSafe;

  if (maintenanceMode.enabled) {
    const now = new Date();
    const twoHoursAgo = new Date(now);
    twoHoursAgo.setTime(now.getTime() - 1000 * 60 * 60 * 2);

    if (maintenanceMode.lastToggledTs < Timestamp.fromDate(twoHoursAgo)) {
      // Maintenance has been enabled for over 2 hours.
      // Notify Creator Campus admins by email in case this is accidental.

      console.log('Maintenance mode has been enabled for over 2 hours. Emailing Creator Campus admins...');

      await emailCreatorCampusAdmins(
        'Reminder: Maintenance Mode is Enabled',
        'Maintenance mode has been enabled on Creator Campus production (app.creatorcampus.io) for over two hours. If this is a mistake, you can disable it from the admin app (admin.creatorcampus.io). Otherwise you can safely ignore this email.\n\nReminders will continue to be sent every 2 hours until maintenance mode is turned off.',
      );
    }
  }
});
