import {Box, Typography} from '@mui/joy';
import {ReactNode} from 'react';

interface Props {
  onClick?: () => void;
  children?: ReactNode;
  sx?: object;
  disabled?: boolean;
}

export function DashedClickableBox({onClick, children, sx = {}, disabled = false}: Props) {
  return (
    <Box
      onClick={!disabled ? onClick : undefined}
      sx={{
        border: '2px dashed',
        borderColor: 'neutral.outlinedBorder',
        borderRadius: 'md',
        padding: 2,
        cursor: disabled ? 'not-allowed' : 'pointer',
        opacity: disabled ? 0.5 : 1,
        pointerEvents: disabled ? 'none' : 'auto',
        '&:hover': {
          backgroundColor: disabled ? 'transparent' : 'neutral.softBg',
        },
        transition: 'background-color 0.2s ease-in-out',
        ...sx,
      }}
    >
      {children ?? <Typography level='body-sm'>Click here</Typography>}
    </Box>
  );
}
