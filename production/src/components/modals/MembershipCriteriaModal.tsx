import {List, ListItem, Typography} from '@mui/joy';
import {ModalShell, ModalHeader} from '@creator-campus/common-components';

interface Props {
  onClose: () => void;
}

export function MembershipCriteriaModal({onClose}: Props) {
  return (
    <ModalShell
      onClose={onClose}
      withCloseButton={true}
      closeOnBackgroundClick={true}
    >
      <ModalHeader title={'Membership criteria'} />
      <Typography level={'body-md'}>Creator Campus is designed specifically for students, recent graduates, and early-career professionals who are in the formative stages of their entrepreneurial journey.</Typography>
      <Typography
        level={'body-md'}
        mt={3}
      >
        For that reason we only accept applications for:
      </Typography>
      <List marker={'disc'}>
        <ListItem>Current students (and staff)</ListItem>
        <ListItem>Recent graduates up to two years post graduation</ListItem>
        <ListItem>Any graduate under the age of 30</ListItem>
        <ListItem>Community members (aspiring entrepreneurs under 30 without a degree, mentors, and supporters)</ListItem>
      </List>
      <Typography
        level={'body-md'}
        mt={3}
      >
        Currently, we only operate in the <Typography level={'title-md'}>United Kingdom</Typography>.
      </Typography>
    </ModalShell>
  );
}
