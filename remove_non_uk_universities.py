import json

# Load the data from the input JSON file
with open('world_universities_and_domains.json', 'r', encoding='utf-8') as infile:
    data = json.load(infile)

# Filter the list to include only objects with 'country' == 'United Kingdom'
uk_universities = [entry for entry in data if entry.get('country') == 'United Kingdom']

# Write the filtered list to a new JSON file
with open('uk_universities.json', 'w', encoding='utf-8') as outfile:
    json.dump(uk_universities, outfile, indent=2, ensure_ascii=False)

print(f"Filtered {len(uk_universities)} universities from the UK and saved to 'uk_universities.json'.")
