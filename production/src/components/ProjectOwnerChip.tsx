import {<PERSON><PERSON>, Box, Chip, Link} from '@mui/joy';
import {useEffect, useState} from 'react';
import {useNavigate} from 'react-router-dom';
import {logger, User} from '@creator-campus/common';

interface Props {
  owner: User;
}

export default function ProjectOwnerChip({owner}: Props) {
  const [picUrl, setPicUrl] = useState('');

  const navigate = useNavigate();

  useEffect(() => {
    owner
      .getAvatarUrl()
      .then((url) => {
        setPicUrl(url);
      })
      .catch((e) => logger.error(e));
  }, []);

  return (
    <Link
      onClick={() => {
        navigate(`/${owner.getLink(false)}`, {replace: true});
      }}
    >
      <Box sx={{display: 'flex', justifyContent: 'flex-start', alignItems: 'center'}}>
        <Chip
          color='neutral'
          size='md'
          variant='soft'
          startDecorator={
            <Avatar
              size='md'
              src={picUrl}
            />
          }
        >
          {owner.name}
        </Chip>
      </Box>
    </Link>
  );
}
