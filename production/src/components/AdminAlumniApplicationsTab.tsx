import {Grid, Stack} from '@mui/joy';
import {useEffect, useState} from 'react';
import {AlumniApplication} from '@creator-campus/common';
import {LoadingIndicator, useUser} from '@creator-campus/common-components';
import Card from '@mui/joy/Card';
import Button from '@mui/joy/Button';
import Box from '@mui/joy/Box';
import Typography from '@mui/joy/Typography';
import {AlumniApplicationDecisionModal} from './modals/AlumniApplicationDecisionModal.tsx';
import Divider from '@mui/joy/Divider';
import {ArrowDownward, ArrowUpward, DateRange, MenuOpen, Numbers, PeopleAlt} from '@mui/icons-material';
import Select from '@mui/joy/Select';
import IconButton from '@mui/joy/IconButton';
import Option from '@mui/joy/Option';

export default function AdminAlumniApplicationsTab() {
  const sortFields = {ts: 'Application date', studentNumber: 'Student number', name: 'Name'};

  const [applications, setApplications] = useState<AlumniApplication[] | null>(null);
  const [selectedApplication, setSelectedApplication] = useState<AlumniApplication | null>(null);
  // const [loadingSwitch, setLoadingSwitch] = useState(false);
  const [sortField, setSortField] = useState(Object.keys(sortFields)[0]);
  const [sortAscending, setSortAscending] = useState(false);

  const {university} = useUser();

  useEffect(() => {
    if (!university) {
      return;
    }

    const unsub = university.listenToAlumniApplications(sortField, sortAscending, (apps) => {
      setApplications(apps);
    });

    return () => unsub();
  }, [university, sortField, sortAscending]);

  if (!applications || !university) {
    return (
      <Box sx={{height: '60vh', alignContent: 'center'}}>
        <LoadingIndicator size='md' />
      </Box>
    );
  }

  return (
    <>
      {selectedApplication && (
        <AlumniApplicationDecisionModal
          application={selectedApplication}
          university={university}
          onClose={() => setSelectedApplication(null)}
        />
      )}
      <Stack
        spacing={3}
        mt={1}
      >
        <Stack
          direction={{xs: 'column', sm: 'row'}}
          spacing={3}
          alignItems={'center'}
          justifyContent={'space-between'}
        >
          <Stack
            spacing={1}
            direction='row'
            alignItems='center'
          >
            <Typography level={'body-sm'}>Sort by:</Typography>
            <Select
              value={sortField}
              onChange={(_, e) => e && setSortField(e)}
              size='sm'
              sx={{mr: 1, width: 160}}
            >
              {Object.entries(sortFields).map(([field, displayName]) => (
                <Option
                  key={field}
                  value={field}
                >
                  {displayName}
                </Option>
              ))}
            </Select>
            <IconButton
              size={'sm'}
              onClick={() => setSortAscending((prev) => !prev)}
            >
              {sortAscending ? <ArrowUpward /> : <ArrowDownward />}
            </IconButton>
          </Stack>
          {/*<Stack direction='row' alignItems={'center'} spacing={1}>*/}
          {/*  <Typography level={'body-sm'}>*/}
          {/*    Allow new alumni applications*/}
          {/*  </Typography>*/}
          {/*  <Switch*/}
          {/*    sx={{*/}
          {/*      [`& .${switchClasses.thumb}`]: {*/}
          {/*        transition: 'width 0.2s, left 0.2s',*/}
          {/*      },*/}
          {/*      '--Switch-thumbSize': '17px',*/}
          {/*      '--Switch-trackWidth': '40px',*/}
          {/*      '--Switch-trackHeight': '22px',*/}
          {/*    }}*/}
          {/*    checked={university.selfManagedAlumniApplications}*/}
          {/*    disabled={loadingSwitch}*/}
          {/*    onChange={async (event) => {*/}
          {/*      setLoadingSwitch(true);*/}

          {/*      await updateDoc(university.doc(), {*/}
          {/*        selfManagedAlumniApplications: event.target.checked,*/}
          {/*      });*/}

          {/*      setLoadingSwitch(false);*/}
          {/*    }}*/}
          {/*  />*/}
          {/*</Stack>*/}
        </Stack>
        <Divider />
        {applications.length === 0 && (
          <Stack
            spacing={0.5}
            height={'calc(100vh - 400px)'}
            sx={{justifyContent: 'center', alignItems: 'center', textAlign: 'center'}}
          >
            <PeopleAlt />
            <Typography level='title-md'>{university.selfManagedAlumniApplications ? 'No pending alumni applications' : 'Alumni applications are disabled'}</Typography>
            <Typography
              level={'body-sm'}
              maxWidth={570}
            >
              {university.selfManagedAlumniApplications ? `People applying to join ${university.name} as an alumnus user will show up here. Check their details against your records to decide whether to approve each request.` : 'Enable them using the switch above if you want to start seeing new applications here.'}
            </Typography>
          </Stack>
        )}
        <Grid
          container
          spacing={2}
          sx={{flexGrow: 1, mt: 2}}
        >
          {applications.map((application) => (
            // 12 = occupy full viewport width
            // 6 = occupy half viewport width
            <Grid
              key={application.id}
              xs={12}
              sm={12}
              md={12}
              lg={6}
              xl={6}
            >
              <Card>
                <Stack
                  direction='row'
                  justifyContent='space-between'
                  alignItems='center'
                >
                  <Stack>
                    <Typography>{application.name}</Typography>
                    <Stack
                      direction={{xs: 'column', sm: 'row'}}
                      spacing={{xs: 0, sm: 2}}
                    >
                      <Stack
                        direction={'row'}
                        spacing={0.6}
                        alignItems={'center'}
                      >
                        {/*@ts-ignore*/}
                        <DateRange fontSize={'sm'} />
                        <Typography level='body-sm'>{application.ts.toDateString()}</Typography>
                      </Stack>
                      <Stack
                        direction={'row'}
                        spacing={0.3}
                        alignItems={'center'}
                      >
                        {/*@ts-ignore*/}
                        <Numbers fontSize={'sm'} />
                        <Typography level='body-sm'>{application.studentNumber}</Typography>
                      </Stack>
                    </Stack>
                  </Stack>
                  <Button
                    variant='outlined'
                    onClick={() => setSelectedApplication(application)}
                  >
                    <Typography
                      level={'body-sm'}
                      color={'white'}
                      sx={{mr: 1}}
                    >
                      View details
                    </Typography>
                    <MenuOpen />
                  </Button>
                </Stack>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Stack>
    </>
  );
}
