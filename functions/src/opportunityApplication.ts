import {onDocumentCreated, onDocumentUpdated} from 'firebase-functions/v2/firestore';
import {FieldValue} from 'firebase-admin/firestore';
import {getMetricsCollection, initSentryForFunction} from './utils';
import * as admin from 'firebase-admin';
import {getInfluxWriter, newPoint} from './influxdb';
import {Opportunity, User, OpportunityApplicationDbModelSafe} from '@creator-campus/common';
import * as Sentry from '@sentry/node';

export const onOppApplicationCreated = onDocumentCreated(
  {
    document: 'opportunityApplications/{applicationId}',
    secrets: ['REACT_APP_INFLUX_DB_TOKEN'],
  },
  async (event) => {
    initSentryForFunction('onOppApplicationCreated');

    if (!event.data) {
      console.error('Event data is undefined!');
      Sentry.addBreadcrumb({
        message: 'Event data is undefined in onOppApplicationCreated',
        category: 'function',
        level: 'error'
      });
      return;
    }

    const data = event.data.data() as OpportunityApplicationDbModelSafe;
    const applicationId = event.params.applicationId;
    console.log(`Detected opportunity application ${applicationId} created.`);

    Sentry.addBreadcrumb({
      message: `Opportunity application created: ${applicationId}`,
      category: 'application',
      level: 'info',
      data: {
        applicationId,
        opportunityId: data.opportunityId,
        applicantId: data.applicantId,
        projectOwnerId: data.projectOwnerId,
        applicantUniversityId: data.applicantUniversityId,
        opportunityUniversityId: data.opportunityUniversityId
      }
    });

    // Update the hasUnreadOppApplications field in the owner's document
    console.log(`Updating document of project owner ${data.projectOwnerId}...`);

    Sentry.addBreadcrumb({
      message: 'Marking project owner as having unread applications',
      category: 'application',
      level: 'info',
      data: { applicationId, projectOwnerId: data.projectOwnerId }
    });

    await admin.firestore().collection(User.collectionName).doc(data.projectOwnerId).update({
      hasUnreadOppApplications: true,
    });

    // Update the opportunity document
    console.log(`Updating document of opportunity ${data.opportunityId}...`);

    Sentry.addBreadcrumb({
      message: 'Adding applicant to opportunity applicant list',
      category: 'application',
      level: 'info',
      data: { applicationId, opportunityId: data.opportunityId, applicantId: data.applicantId }
    });

    await admin
      .firestore()
      .collection(Opportunity.collectionName)
      .doc(data.opportunityId)
      .update({
        applicantIds: FieldValue.arrayUnion(data.applicantId),
      });

    // Update metrics
    console.log('Updating metrics...');

    Sentry.addBreadcrumb({
      message: 'Updating university metrics for opportunity application',
      category: 'metrics',
      level: 'info',
      data: {
        applicationId,
        applicantUniversityId: data.applicantUniversityId,
        opportunityUniversityId: data.opportunityUniversityId
      }
    });

    await getMetricsCollection(data.applicantUniversityId).update({
      outgoingOppApplications: FieldValue.increment(1),
    });

    await getMetricsCollection(data.opportunityUniversityId).update({
      incomingOppApplications: FieldValue.increment(1),
    });

    Sentry.addBreadcrumb({
      message: 'Writing opportunity application metrics to InfluxDB',
      category: 'metrics',
      level: 'info',
      data: {
        applicationId,
        applicantUniversityId: data.applicantUniversityId,
        opportunityUniversityId: data.opportunityUniversityId
      }
    });

    const influxToken = String(process.env.REACT_APP_INFLUX_DB_TOKEN);
    const influxWriter = getInfluxWriter(influxToken);

    let point = newPoint().intField('oppApplicationsSent', 1).tag('university', data.applicantUniversityId);
    influxWriter.writePoint(point);

    point = newPoint().intField('oppApplicationsReceived', 1).tag('university', data.opportunityUniversityId);
    influxWriter.writePoint(point);
  },
);

export const onOppApplicationUpdated = onDocumentUpdated(
  {
    document: 'opportunityApplications/{applicationId}',
    secrets: ['REACT_APP_INFLUX_DB_TOKEN'],
  },
  async (event) => {
    initSentryForFunction('onOppApplicationUpdated');

    if (!event.data) {
      console.error('Event data is undefined!');
      Sentry.addBreadcrumb({
        message: 'Event data is undefined in onOppApplicationUpdated',
        category: 'function',
        level: 'error'
      });
      return;
    }

    const beforeData = event.data.before.data() as OpportunityApplicationDbModelSafe;
    const afterData = event.data.after.data() as OpportunityApplicationDbModelSafe;
    const applicationId = event.params.applicationId;
    console.log(`Detected opportunity application ${applicationId} updated.`);

    Sentry.addBreadcrumb({
      message: `Opportunity application updated: ${applicationId}`,
      category: 'application',
      level: 'info',
      data: {
        applicationId,
        opportunityId: afterData.opportunityId,
        applicantId: afterData.applicantId,
        wasAccepted: !beforeData.dateAccepted && afterData.dateAccepted,
        dateAcceptedChanged: beforeData.dateAccepted !== afterData.dateAccepted
      }
    });

    // Check if the application just got accepted
    if (!beforeData.dateAccepted && afterData.dateAccepted) {
      console.log(`Opportunity application ${applicationId} just got accepted.`);

      Sentry.addBreadcrumb({
        message: 'Opportunity application just got accepted',
        category: 'application',
        level: 'info',
        data: {
          applicationId,
          opportunityId: afterData.opportunityId,
          applicantId: afterData.applicantId,
          applicantUniversityId: afterData.applicantUniversityId
        }
      });

      console.log(`Updating related opportunity ${afterData.opportunityId}...`);

      Sentry.addBreadcrumb({
        message: 'Marking opportunity as filled',
        category: 'application',
        level: 'info',
        data: {
          applicationId,
          opportunityId: afterData.opportunityId,
          acceptedApplicantId: afterData.applicantId
        }
      });

      await admin.firestore().collection(Opportunity.collectionName).doc(afterData.opportunityId).update({
        fillDate: new Date(),
        acceptedApplicantId: afterData.applicantId,
      });

      console.log('Updating metrics...');

      Sentry.addBreadcrumb({
        message: 'Writing successful applicant metric to InfluxDB',
        category: 'metrics',
        level: 'info',
        data: {
          applicationId,
          applicantUniversityId: afterData.applicantUniversityId
        }
      });

      const influxToken = String(process.env.REACT_APP_INFLUX_DB_TOKEN);
      const influxWriter = getInfluxWriter(influxToken);

      const point = newPoint().intField('numSuccessfulOppApplicants', 1).tag('university', afterData.applicantUniversityId);
      influxWriter.writePoint(point);
    }
  },
);
