import {useState} from 'react';
import {Button} from '@mui/joy';
import {Check, UploadFile, Delete} from '@mui/icons-material';
import {logger, User} from '@creator-campus/common';
import {VisuallyHiddenInput} from './FileUploadButton.tsx';
import {LoadingIndicator, useSnackbar} from '@creator-campus/common-components';
import ConfirmRemoveCvModal from './modals/ConfirmRemoveCvModal.tsx';

interface Props {
  user: User;
}

export function UploadCVButton({user}: Props) {
  const [cvLoading, setCvLoading] = useState(false);
  const [hovered, setHovered] = useState(false);
  const [showConfirmRemoveModal, setShowConfirmRemoveModal] = useState(false);

  const {showSnackbar, showErrorSnackbar} = useSnackbar();

  return (
    <>
      {showConfirmRemoveModal && (
        <ConfirmRemoveCvModal
          onClose={(cvRemoved) => {
            setShowConfirmRemoveModal(false);
            setCvLoading(false);

            if (cvRemoved) {
              showSnackbar('CV removed.', 'success');
            }
          }}
          user={user}
        />
      )}
      <Button
        variant={user.cvUploaded ? 'soft' : 'outlined'}
        color={user.cvUploaded ? (hovered ? 'danger' : 'success') : 'neutral'}
        size='sm'
        fullWidth
        startDecorator={user.cvUploaded ? hovered ? <Delete /> : <Check /> : <UploadFile />}
        component={user.cvUploaded ? 'button' : 'label'}
        onMouseEnter={() => setHovered(true)}
        onMouseLeave={() => setHovered(false)}
        disabled={cvLoading}
        onClick={async () => {
          if (user.cvUploaded && hovered) {
            setCvLoading(true);
            setShowConfirmRemoveModal(true);
          }
        }}
      >
        {cvLoading ? (
          <LoadingIndicator size='sm' />
        ) : (
          <>
            {user.cvUploaded ? (hovered ? 'Remove CV' : 'CV uploaded') : 'Upload CV'}
            {!user.cvUploaded && (
              <VisuallyHiddenInput
                type='file'
                accept='application/pdf'
                disabled={cvLoading}
                onChange={async (event) => {
                  const files = event.target.files;
                  if (files && files.length > 0) {
                    setCvLoading(true);

                    await user
                      .uploadCv(files[0])
                      .then(() => {
                        showSnackbar('CV uploaded!', 'success');
                      })
                      .catch((e) => {
                        logger.error(e);
                        showErrorSnackbar(e, 'Error uploading CV.');
                      });

                    setCvLoading(false);
                    event.target.value = '';
                  }
                }}
              />
            )}
          </>
        )}
      </Button>
    </>
  );
}
