name: Deploy Firebase Extensions

on:
  push:
    branches:
      - main
    paths:
      - 'extensions/**'
      - '!extensions/*.local'
      - '.github/workflows/deploy_firebase_extensions.yml'

jobs:
  deploy:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        project: [production, demo]

    steps:
      - uses: actions/checkout@v2

      - name: Deploy Firebase extensions
        uses: w9jds/firebase-action@master
        with:
          args: deploy --only extensions --project ${{ matrix.project }} --force
        env:
          FIREBASE_TOKEN: "${{ secrets.FIREBASE_DEPLOY_TOKEN }}"
