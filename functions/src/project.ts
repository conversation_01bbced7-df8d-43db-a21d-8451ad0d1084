import {FieldValue} from 'firebase-admin/firestore';
import {onDocumentCreated, onDocumentDeleted, onDocumentUpdated} from 'firebase-functions/v2/firestore';
import * as admin from 'firebase-admin';
import {getInfluxWriter, newPoint} from './influxdb';
import {Opportunity, ProjectDbModelSafe, User} from '@creator-campus/common';
import {calculateProjectScore, initSentryForFunction} from './utils';
import * as Sentry from '@sentry/node';

export const onProjectCreated = onDocumentCreated(
  {
    document: 'projects/{projectId}',
    secrets: ['REACT_APP_INFLUX_DB_TOKEN'],
  },
  async (event) => {
    initSentryForFunction('onProjectCreated');

    if (!event.data) {
      console.error('Event data is undefined!');
      Sentry.addBreadcrumb({
        message: 'Event data is undefined in onProjectCreated',
        category: 'function',
        level: 'error'
      });
      return;
    }

    const data = event.data.data();
    const projectId = event.params.projectId;
    console.log(`Detected project ${projectId} created.`);

    Sentry.addBreadcrumb({
      message: `Project created: ${projectId}`,
      category: 'project',
      level: 'info',
      data: {
        projectId,
        ownerId: data.ownerId,
        universityId: data.universityId,
        title: data.title,
        hidden: data.hidden
      }
    });

    // Update metrics
    console.log('Updating metrics...');

    Sentry.addBreadcrumb({
      message: 'Writing project creation metric to InfluxDB',
      category: 'metrics',
      level: 'info',
      data: { projectId, universityId: data.universityId }
    });

    const influxToken = String(process.env.REACT_APP_INFLUX_DB_TOKEN);
    const influxWriter = getInfluxWriter(influxToken);

    const point = newPoint().intField('projectsCreated', 1).tag('university', data.universityId);
    influxWriter.writePoint(point);
  },
);

export const onProjectUpdated = onDocumentUpdated(
  {
    document: 'projects/{projectId}',
  },
  async (event) => {
    initSentryForFunction('onProjectUpdated');

    if (!event.data) {
      console.error('Event data is undefined!');
      Sentry.addBreadcrumb({
        message: 'Event data is undefined in onProjectUpdated',
        category: 'function',
        level: 'error'
      });
      return;
    }

    const beforeData = event.data.before.data() as ProjectDbModelSafe;
    const afterData = event.data.after.data() as ProjectDbModelSafe;
    const projectId = event.params.projectId;
    console.log(`Detected project ${projectId} updated.`);

    Sentry.addBreadcrumb({
      message: `Project updated: ${projectId}`,
      category: 'project',
      level: 'info',
      data: {
        projectId,
        ownerId: afterData.ownerId,
        nameChanged: beforeData.name !== afterData.name,
        descriptionChanged: beforeData.description !== afterData.description,
        hiddenChanged: beforeData.hidden !== afterData.hidden,
        numOpenOpportunitiesChanged: beforeData.numOpenOpportunities !== afterData.numOpenOpportunities
      }
    });

    const scoreUpdated = beforeData.numOpenOpportunities !== afterData.numOpenOpportunities || beforeData.description !== afterData.description || beforeData.summary !== afterData.summary || beforeData.website !== afterData.website;

    if (scoreUpdated) {
      Sentry.addBreadcrumb({
        message: 'Project score needs recalculation',
        category: 'project',
        level: 'info',
        data: {
          projectId,
          oldScore: beforeData.score,
          numOpenOpportunitiesChanged: beforeData.numOpenOpportunities !== afterData.numOpenOpportunities,
          descriptionChanged: beforeData.description !== afterData.description,
          summaryChanged: beforeData.summary !== afterData.summary,
          websiteChanged: beforeData.website !== afterData.website
        }
      });

      const newScore = await calculateProjectScore(projectId, afterData);

      console.log(`Project score changed from ${beforeData.score} to ${newScore}. Updating...`);

      Sentry.addBreadcrumb({
        message: 'Updated project score',
        category: 'firestore',
        level: 'info',
        data: { projectId, oldScore: beforeData.score, newScore }
      });

      return event.data.after.ref.update({
        score: newScore,
      });
    }
  },
);

export const onProjectDeleted = onDocumentDeleted('projects/{projectId}', async (event) => {
  initSentryForFunction('onProjectDeleted');

  if (!event.data) {
    console.error('Event data is undefined!');
    Sentry.addBreadcrumb({
      message: 'Event data is undefined in onProjectDeleted',
      category: 'function',
      level: 'error'
    });
    return;
  }

  const data = event.data.data();
  const projectId = event.params.projectId;
  console.log(`Detected project ${projectId} deleted.`);

  Sentry.addBreadcrumb({
    message: `Project deleted: ${projectId}`,
    category: 'project',
    level: 'info',
    data: {
      projectId,
      ownerId: data.ownerId,
      title: data.title,
      universityId: data.universityId
    }
  });

  // Remove project ID from user
  console.log(`Detaching project from owner: ${data.ownerId}...`);

  Sentry.addBreadcrumb({
    message: 'Removing project ID from user',
    category: 'cleanup',
    level: 'info',
    data: { projectId, ownerId: data.ownerId }
  });

  await admin
    .firestore()
    .collection(User.collectionName)
    .doc(data.ownerId)
    .update({
      projectIds: FieldValue.arrayRemove(projectId),
    })
    .catch(() => {
      console.log('Could not remove project ID from user. This is expected behaviour if this Function was triggered by a user deletion.');
      Sentry.addBreadcrumb({
        message: 'Could not remove project ID from user (expected if triggered by user deletion)',
        category: 'cleanup',
        level: 'info',
        data: { projectId, ownerId: data.ownerId }
      });
    });

  // Delete related opportunities
  const opportunitiesSnapshot = await admin.firestore().collection(Opportunity.collectionName).where('projectId', '==', projectId).get();
  console.log(`Deleting ${opportunitiesSnapshot.size} related opportunities...`);

  Sentry.addBreadcrumb({
    message: `Deleting ${opportunitiesSnapshot.size} related opportunities`,
    category: 'cleanup',
    level: 'info',
    data: { projectId, opportunityCount: opportunitiesSnapshot.size }
  });

  for (const opp of opportunitiesSnapshot.docs) {
    Sentry.addBreadcrumb({
      message: 'Deleting related opportunity',
      category: 'cleanup',
      level: 'info',
      data: { projectId, opportunityId: opp.id }
    });
    await opp.ref.delete();
  }

  // Delete project files in Firebase Storage
  Sentry.addBreadcrumb({
    message: 'Deleting project files from Firebase Storage',
    category: 'storage',
    level: 'info',
    data: { projectId, prefix: `projects/${projectId}/` }
  });

  admin
    .app()
    .storage()
    .bucket()
    .deleteFiles({prefix: `projects/${projectId}/`}, (e) => {
      if (e) {
        console.error(e);
        Sentry.addBreadcrumb({
          message: 'Error deleting project files from Firebase Storage',
          category: 'storage',
          level: 'error',
          data: { projectId, error: e }
        });
      } else {
        console.log('Deleted related project files in Firebase Storage.');
        Sentry.addBreadcrumb({
          message: 'Successfully deleted project files from Firebase Storage',
          category: 'storage',
          level: 'info',
          data: { projectId }
        });
      }
    });
});
