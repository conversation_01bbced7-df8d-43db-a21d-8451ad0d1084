import {doc, type FirestoreDataConverter, getDoc, QueryDocumentSnapshot, type SnapshotOptions, type WithFieldValue} from 'firebase/firestore';
import {firestore} from '../../firebase';
import {University, UniversityConverter} from './university';
import {formatHours} from '../../utils/format';

export class UniversityMetrics {
  constructor(
    // The number of 'message requests' received by this university
    readonly incomingConnectionRequests: number,
    // The number of 'message requests' sent by this university
    readonly outgoingConnectionRequests: number,
    // The number of opportunity applications received by this university
    readonly incomingOppApplications: number,
    // The number of opportunity applications sent by this university
    readonly outgoingOppApplications: number,
    // The number of opportunities created by this university
    readonly opportunitiesCreated: number,
    // The number of times a user from this university has successfully filled an opportunity
    readonly numSuccessfulOppApplicants: number,
    // The total number of hours that opportunities from this university have been open for
    readonly hoursOppsSpentOpen: number,
    // Where applicants are from who are filling this university's opportunities
    readonly unisFillingOurOpps: {[universityId: string]: number},
    // Where opportunities are from that this university's users are filling
    readonly unisWhoseOppsWeFill: {[universityId: string]: number},
    // Unused alumni-related metrics
    readonly alumni: {
      readonly totalAlumniApplications: number;
      readonly numAlumniApproved: number;
      readonly numAlumniRejected: number;
      readonly numApprovedAlumniExpired: number;
      readonly hoursAlumniApplicationsSpentUndecided: number;
    },
  ) {}

  public static doc(universityId: string) {
    return doc(firestore(), University.collectionName, universityId, 'metrics', 'metrics').withConverter(new UniversityMetricsConverter());
  }

  public static defaults() {
    return new UniversityMetrics(
      0,
      0,
      0,
      0,
      0,
      0,
      0,
      {},
      {},
      {
        totalAlumniApplications: 0,
        numAlumniApproved: 0,
        numAlumniRejected: 0,
        numApprovedAlumniExpired: 0,
        hoursAlumniApplicationsSpentUndecided: 0,
      },
    );
  }

  public static async fetchForUniversity(universityId: string) {
    const snapshot = await getDoc(doc(firestore(), University.collectionName, universityId, 'metrics', 'metrics').withConverter(new UniversityMetricsConverter()));

    return snapshot.data();
  }

  /**
   * Adds the metrics from another instance and returns a new UniversityMetrics object.
   */
  public add(other: UniversityMetrics): UniversityMetrics {
    const unisFillingOurOpps = {...this.unisFillingOurOpps};
    for (const [universityId, num] of Object.entries(other.unisFillingOurOpps)) {
      unisFillingOurOpps[universityId] = (unisFillingOurOpps[universityId] || 0) + num;
    }

    const unisWhoseOppsWeFill = {...this.unisWhoseOppsWeFill};
    for (const [universityId, num] of Object.entries(other.unisWhoseOppsWeFill)) {
      unisWhoseOppsWeFill[universityId] = (unisWhoseOppsWeFill[universityId] || 0) + num;
    }

    return new UniversityMetrics(
      this.incomingConnectionRequests + other.incomingConnectionRequests,
      this.outgoingConnectionRequests + other.outgoingConnectionRequests,
      this.incomingOppApplications + other.incomingOppApplications,
      this.outgoingOppApplications + other.outgoingOppApplications,
      this.opportunitiesCreated + other.opportunitiesCreated,
      this.numSuccessfulOppApplicants + other.numSuccessfulOppApplicants,
      this.hoursOppsSpentOpen + other.hoursOppsSpentOpen,
      unisFillingOurOpps,
      unisWhoseOppsWeFill,
      {
        totalAlumniApplications: this.alumni.totalAlumniApplications + other.alumni.totalAlumniApplications,
        numAlumniApproved: this.alumni.numAlumniApproved + other.alumni.numAlumniApproved,
        numAlumniRejected: this.alumni.numAlumniRejected + other.alumni.numAlumniRejected,
        numApprovedAlumniExpired: this.alumni.numApprovedAlumniExpired + other.alumni.numApprovedAlumniExpired,
        hoursAlumniApplicationsSpentUndecided: this.alumni.hoursAlumniApplicationsSpentUndecided + other.alumni.hoursAlumniApplicationsSpentUndecided,
      },
    );
  }

  /**
   * Returns the average time it takes for an opportunity from this university to go from posted to filled.
   */
  public getAvgTimeToMatch() {
    const oppApplicationsAccepted = this.getOppApplicationsAccepted();
    return formatHours(oppApplicationsAccepted === 0 ? 0 : this.hoursOppsSpentOpen / oppApplicationsAccepted);
  }

  /**
   * Returns the number of accepted opp applications sent by this university.
   */
  public getOppApplicationsAccepted() {
    return Object.values(this.unisWhoseOppsWeFill).reduce((a, b) => a + b, 0);
  }
}

export interface UniversityMetricsDbModel {
  incomingConnectionRequests: number;
  outgoingConnectionRequests: number;
  incomingOppApplications: number;
  outgoingOppApplications: number;
  opportunitiesCreated: number;
  numSuccessfulOppApplicants: number;
  hoursOppsSpentOpen: number;
  unisFillingOurOpps: {[universityId: string]: number};
  unisWhoseOppsWeFill: {[universityId: string]: number};
  alumni: {
    totalAlumniApplications: number;
    numAlumniApproved: number;
    numAlumniRejected: number;
    numApprovedAlumniExpired: number;
    hoursAlumniApplicationsSpentUndecided: number;
  };
}

export class UniversityMetricsConverter implements FirestoreDataConverter<UniversityMetrics, UniversityMetricsDbModel> {
  toFirestore(metrics: WithFieldValue<UniversityMetrics>): WithFieldValue<UniversityMetricsDbModel> {
    return {
      incomingConnectionRequests: metrics.incomingConnectionRequests,
      outgoingConnectionRequests: metrics.outgoingConnectionRequests,
      incomingOppApplications: metrics.incomingOppApplications,
      outgoingOppApplications: metrics.outgoingOppApplications,
      opportunitiesCreated: metrics.opportunitiesCreated,
      numSuccessfulOppApplicants: metrics.numSuccessfulOppApplicants,
      hoursOppsSpentOpen: metrics.hoursOppsSpentOpen,
      unisFillingOurOpps: metrics.unisFillingOurOpps,
      unisWhoseOppsWeFill: metrics.unisWhoseOppsWeFill,
      alumni: metrics.alumni,
    };
  }

  fromFirestore(snapshot: QueryDocumentSnapshot, options: SnapshotOptions): UniversityMetrics {
    const data = snapshot.data(options) as UniversityMetricsDbModel;
    return new UniversityMetrics(data.incomingConnectionRequests, data.outgoingConnectionRequests, data.incomingOppApplications, data.outgoingOppApplications, data.opportunitiesCreated, data.numSuccessfulOppApplicants, data.hoursOppsSpentOpen, data.unisFillingOurOpps, data.unisWhoseOppsWeFill, data.alumni);
  }
}
