import {FormEvent, useState} from 'react';
import Button from '@mui/joy/Button';
import FormControl from '@mui/joy/FormControl';

import {auth, logger} from '@creator-campus/common';
import {LoadingIndicator, ModalHeader, ModalShell, useAuth, useResetEmailPasswordString, useSnackbar} from '@creator-campus/common-components';
import {FormHelperText, Input} from '@mui/joy';
import {sendPasswordResetEmail} from 'firebase/auth';
import {EMAIL_INPUT_SLOT_PROPS} from '../../constants/styles.ts';
import ButtonGroup from '@mui/joy/ButtonGroup';

interface FormElements extends HTMLFormControlsCollection {
  email: HTMLInputElement;
}

interface ResetPasswordFormElement extends HTMLFormElement {
  readonly elements: FormElements;
}

interface Props {
  open: boolean;
  onClose: () => void;
}

export default function ResetPasswordModal({open, onClose}: Props) {
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const {showSnackbar} = useSnackbar();
  const {currentUser} = useAuth();

  async function handleSubmit(email: string) {
    setLoading(true);

    await sendPasswordResetEmail(auth(), email)
      .then(async () => {
        showSnackbar('Check your email!');
        onClose();
      })
      .catch((e) => {
        logger.error(e);

        if (e.code === 'auth/missing-email') {
          setError('Please enter your email.');
        } else if (e.code === 'auth/user-not-found') {
          setError('No account exists with that email.');
        } else {
          setError('Sorry, something went wrong.');
        }
      });

    setLoading(false);
  }

  return (
    <>
      {open && (
        <ModalShell
          onClose={onClose}
          withCloseButton={true}
          closeOnBackgroundClick={true}
        >
          <ModalHeader
            title={useResetEmailPasswordString().heading}
            subtitle={useResetEmailPasswordString().subheading}
          />

          <form
            onSubmit={async (event: FormEvent<ResetPasswordFormElement>) => {
              event.preventDefault();
              const formData = event.currentTarget.elements;
              await handleSubmit(formData.email.value);
            }}
          >
            <FormControl
              sx={{mt: 2}}
              error={error !== null}
            >
              <Input
                required
                name='email'
                placeholder='Email'
                variant='outlined'
                defaultValue={currentUser?.email || ''}
                slotProps={EMAIL_INPUT_SLOT_PROPS}
                onKeyDown={async (e) => {
                  if (e.key === 'Enter') {
                    const email = (e.target as HTMLInputElement).value;
                    await handleSubmit(email);
                  }
                }}
              />
              {error && <FormHelperText>{error}</FormHelperText>}
            </FormControl>

            <ButtonGroup
              spacing={1}
              sx={{mt: 2}}
            >
              <Button
                variant='solid'
                color='primary'
                disabled={loading}
                type='submit'
              >
                {loading ? <LoadingIndicator size='sm' /> : useResetEmailPasswordString().reset}
              </Button>
              <Button
                variant='outlined'
                onClick={() => onClose()}
              >
                {useResetEmailPasswordString().cancel}
              </Button>
            </ButtonGroup>
          </form>
        </ModalShell>
      )}
    </>
  );
}
