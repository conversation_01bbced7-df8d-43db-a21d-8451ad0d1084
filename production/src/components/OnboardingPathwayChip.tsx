import {OnboardingPathway} from '@creator-campus/common';
import {TooltipChip} from './TooltipChip.tsx';

interface Props {
  role: OnboardingPathway;
}

export function OnboardingPathwayChip({role}: Props) {
  return (
    <TooltipChip
      tooltip={role.label}
      icon={<role.icon sx={{fontSize: '13px'}} />}
      size={'sm'}
      color={'neutral'}
      enterDelay={0}
      enterNextDelay={0}
    />
  );
}
