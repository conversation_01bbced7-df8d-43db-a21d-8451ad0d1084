import Box from '@mui/joy/Box';
import {EditRounded} from '@mui/icons-material';
import {useEffect, useRef, useState} from 'react';
import {University} from '@creator-campus/common';
import {LoadingIndicator} from '@creator-campus/common-components';
import {Stack, Typography} from '@mui/joy';

interface Props {
  university: University;
  onImageSelected: (image: File) => void;
  selectedImageUrl?: string;
}

export function UniversityLogoWithUpload({university, onImageSelected, selectedImageUrl}: Props) {
  const [loading, setLoading] = useState(!!university.branding?.logoUpdatedAt);
  const [logoUrl, setLogoUrl] = useState<string | null>(null);
  const inputRef = useRef<HTMLInputElement | null>(null);

  const imageSource = selectedImageUrl || logoUrl;

  useEffect(() => {
    if (university.branding?.logoUpdatedAt) {
      university.getLogoUrl().then((url) => {
        setLogoUrl(url);
        setLoading(false);
      });
    } else {
      setLoading(false);
    }
  }, [university]);

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.value = '';
    }
  }, [selectedImageUrl]);

  return (
    <label style={{cursor: 'pointer'}}>
      <Box
        position='relative'
        sx={{
          width: imageSource ? undefined : 76,
          height: imageSource ? undefined : 76,
          borderRadius: 'md',
          justifyItems: 'center',
          '&:hover .overlay': {
            opacity: 1,
          },
        }}
      >
        {loading ? (
          <LoadingIndicator size='sm' />
        ) : imageSource ? (
          <img
            src={imageSource}
            alt={`${university.name} logo`}
            style={{
              maxWidth: 300,
              maxHeight: 76,
              objectFit: 'contain',
              display: 'block',
              borderRadius: 'inherit',
            }}
          />
        ) : (
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              borderRadius: 'inherit',
            }}
          />
        )}

        {/* Hidden file input */}
        <input
          ref={inputRef}
          type='file'
          accept='image/*'
          style={{display: 'none'}}
          onChange={(e) => {
            const file = e.target.files?.[0];
            if (file) {
              onImageSelected(file);
            }
          }}
        />

        {/* Hover overlay with edit icon */}
        <Box
          className='overlay'
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            bgcolor: 'rgba(0, 0, 0, 0.5)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            opacity: imageSource ? 0 : 0.75,
            transition: 'opacity 0.3s ease',
            pointerEvents: 'none',
            borderRadius: 'inherit',
          }}
        >
          <Stack
            alignItems={'center'}
            spacing={0.1}
          >
            <Typography
              level='title-sm'
              sx={{color: '#ffffff'}}
            >
              Set logo
            </Typography>
            <EditRounded sx={{color: 'background.body', fontSize: 20}} />
          </Stack>
        </Box>
      </Box>
    </label>
  );
}
