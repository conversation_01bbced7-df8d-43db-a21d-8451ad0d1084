import {Typography} from '@mui/joy';
import Box from '@mui/joy/Box';

interface Props {
  variant?: 'minimal' | 'default';
}

export function Footer({variant = 'default'}: Props) {
  if (variant === 'minimal') {
    return (
      <Box
        component='footer'
        sx={{py: 3}}
      >
        <Typography
          level='body-xs'
          textAlign='center'
        >
          © Creator Campus {new Date().getFullYear()}
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{display: 'block', alignItems: 'center', alignContent: 'center'}}>
      <Typography
        level='body-sm'
        textAlign='center'
      >
        © Creator Campus {new Date().getFullYear()}. All rights reserved.
      </Typography>
    </Box>
  );
}
