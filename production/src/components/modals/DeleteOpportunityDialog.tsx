import {logger, Opportunity} from '@creator-campus/common';
import {LoadingIndicator, useCreateOpportunityString, useMyProjectsString, useSnackbar} from '@creator-campus/common-components';
import Button from '@mui/joy/Button';
import DialogTitle from '@mui/joy/DialogTitle';
import WarningRoundedIcon from '@mui/icons-material/WarningRounded';
import Divider from '@mui/joy/Divider';
import DialogContent from '@mui/joy/DialogContent';
import DialogActions from '@mui/joy/DialogActions';
import ModalDialog from '@mui/joy/ModalDialog';
import {Link, Modal} from '@mui/joy';
import {useState} from 'react';
import Typography from '@mui/joy/Typography';

interface Props {
  opportunity: Opportunity;
  onOppRemoved?: () => void;
  onClose: () => void;
}

export function DeleteOpportunityDialog({opportunity, onOppRemoved, onClose}: Props) {
  const [loading, setLoading] = useState(false);

  const {showErrorSnackbar} = useSnackbar();

  return (
    <Modal
      open={true}
      onClose={onClose}
    >
      <ModalDialog
        variant='outlined'
        role='alertdialog'
      >
        <DialogTitle>
          <WarningRoundedIcon />
          Delete opportunity
        </DialogTitle>
        <Divider />
        <DialogContent>
          {/*@ts-ignore*/}
          <Typography color={'grey'}>
            Was this opportunity filled? Tell us{' '}
            <Link
              href='https://forms.gle/HAYRPBbZg9zq6J8o8'
              target='_blank'
            >
              here
            </Link>{' '}
            (opens in a new tab).
          </Typography>
          {useCreateOpportunityString().delete_subheading}
        </DialogContent>
        <DialogActions>
          <Button
            disabled={loading}
            variant='solid'
            color='danger'
            onClick={async () => {
              setLoading(true);

              await opportunity
                .delete()
                .then(() => {
                  if (onOppRemoved) {
                    onOppRemoved();
                  }

                  onClose();
                })
                .catch((e) => {
                  logger.error(e);
                  showErrorSnackbar(e, 'Error deleting opportunity.');
                });

              setLoading(false);
            }}
          >
            {loading ? <LoadingIndicator size='sm' /> : useMyProjectsString().delete}
          </Button>
          <Button
            variant='plain'
            color='neutral'
            onClick={onClose}
          >
            {useMyProjectsString().cancel}
          </Button>
        </DialogActions>
      </ModalDialog>
    </Modal>
  );
}
