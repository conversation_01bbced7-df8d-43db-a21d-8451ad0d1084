import ButtonGroup from '@mui/joy/ButtonGroup';
import Button from '@mui/joy/Button';
import {User} from '@creator-campus/common';
import {ModalHeader, ModalShell, useSnackbar} from '@creator-campus/common-components';
import {useState} from 'react';

interface Props {
  onClose: (cvRemoved: boolean) => void;
  user: User;
}

export default function ConfirmRemoveCvModal({onClose, user}: Props) {
  const [loading, setLoading] = useState<boolean>(false);

  const {showErrorSnackbar} = useSnackbar();

  return (
    <>
      <ModalShell
        onClose={() => onClose(false)}
        withCloseButton={true}
        closeOnBackgroundClick={true}
      >
        <ModalHeader
          title='Remove CV'
          subtitle='Are you sure you want to proceed? This will also remove your CV from any opportunities you have applied to.'
        />
        <ButtonGroup
          spacing={1}
          sx={{mt: 2}}
        >
          <Button
            variant='solid'
            color='primary'
            disabled={loading}
            onClick={async () => {
              setLoading(true);
              await user
                .removeCv()
                .then(() => onClose(true))
                .catch((e) => showErrorSnackbar(e, 'Error removing CV.'));
              setLoading(false);
            }}
          >
            Confirm
          </Button>
          <Button
            onClick={() => onClose(false)}
            variant='outlined'
          >
            Cancel
          </Button>
        </ButtonGroup>
      </ModalShell>
    </>
  );
}
