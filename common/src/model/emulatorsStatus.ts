import {type FirestoreDataConverter, doc, QueryDocumentSnapshot, type SnapshotOptions, type WithFieldValue} from 'firebase/firestore';
import {firestore} from '../firebase';

export class EmulatorsStatus {
  constructor(readonly ready: boolean) {}

  public static doc() {
    return doc(firestore(), 'config', 'emulators').withConverter(new EmulatorsStatusConverter());
  }
}

interface EmulatorsStatusDbModel {
  ready: boolean;
}

export class EmulatorsStatusConverter implements FirestoreDataConverter<EmulatorsStatus, EmulatorsStatusDbModel> {
  toFirestore(emulatorsStatus: WithFieldValue<EmulatorsStatus>): WithFieldValue<EmulatorsStatusDbModel> {
    return {
      ready: emulatorsStatus.ready,
    };
  }

  fromFirestore(snapshot: QueryDocumentSnapshot, options: SnapshotOptions): EmulatorsStatus {
    const data = snapshot.data(options) as EmulatorsStatusDbModel;
    return new EmulatorsStatus(data.ready);
  }
}
