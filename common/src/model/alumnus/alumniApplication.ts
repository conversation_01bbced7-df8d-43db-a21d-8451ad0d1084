import {collection, FieldValue, type FirestoreDataConverter, getCountFromServer, QueryDocumentSnapshot, type SnapshotOptions, Timestamp, type WithFieldValue} from 'firebase/firestore';
import {firestore} from '../../firebase.js';
import {University} from '../university/university';

export class AlumniApplication {
  constructor(
    readonly id: string,
    readonly name: string,
    readonly email: string,
    readonly universityId: string,
    readonly studentNumber: string,
    readonly graduationYear: string,
    readonly ts: Date,
  ) {}

  static async getCount(universityId: string) {
    const snapshot = await getCountFromServer(collection(firestore(), University.collectionName, universityId, 'alumniApplications'));
    return snapshot.data().count;
  }
}

interface AlumniApplicationDbModel {
  name: string;
  email: string;
  universityId: string;
  studentNumber: string;
  graduationYear: string;
  ts: Timestamp;
}

export class AlumniApplicationConverter implements FirestoreDataConverter<AlumniApplication, AlumniApplicationDbModel> {
  toFirestore(alumniApplication: WithFieldValue<AlumniApplication>): WithFieldValue<AlumniApplicationDbModel> {
    return {
      name: alumniApplication.name,
      email: alumniApplication.email,
      universityId: alumniApplication.universityId,
      studentNumber: alumniApplication.studentNumber,
      graduationYear: alumniApplication.graduationYear,
      ts: alumniApplication.ts instanceof Date ? Timestamp.fromDate(alumniApplication.ts) : (alumniApplication.ts as FieldValue),
    };
  }

  fromFirestore(snapshot: QueryDocumentSnapshot, options: SnapshotOptions): AlumniApplication {
    const data = snapshot.data(options) as AlumniApplicationDbModel;
    return new AlumniApplication(snapshot.id, data.name, data.email, data.universityId, data.studentNumber, data.graduationYear, data.ts.toDate());
  }
}
