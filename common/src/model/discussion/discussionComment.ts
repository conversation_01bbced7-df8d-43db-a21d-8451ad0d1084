import {collection, doc, FieldValue, type FirestoreDataConverter, getDoc, QueryDocumentSnapshot, type SnapshotOptions, Timestamp, type WithFieldValue} from 'firebase/firestore';
import {firestore} from '../../firebase.js';
import {DiscussionPostBase, DiscussionPostBaseDbModel, DiscussionPostBaseDbModelSafe} from './discussionPostBase.js';
import {User} from '../user/user.js';
import {generateRandomId} from '../../utils/misc.js';
import {DiscussionAttachment} from './discussionAttachment.js';
import {University} from '../university/university';

export class DiscussionComment extends DiscussionPostBase {
  constructor(
    override readonly id: string,
    override readonly attachments: DiscussionAttachment[],
    override readonly authorId: string,
    override readonly datePosted: Date,
    readonly postId: string,
    override readonly text: string,
    override readonly universityId: string | null,
    override readonly upVotes: string[],
  ) {
    super(id, attachments, authorId, datePosted, text, universityId, upVotes);
  }

  public static create(user: User, postId: string, text: string, attachments: DiscussionAttachment[], universityId?: string) {
    return new DiscussionComment(generateRandomId(), attachments, user.id, new Date(), postId, text, universityId || null, []);
  }

  public doc() {
    if (this.universityId) {
      return doc(firestore(), University.collectionName, this.universityId, 'discussion', this.postId, 'comments', this.id).withConverter<DiscussionComment, DiscussionCommentDbModel>(new DiscussionCommentConverter());
    }

    return doc(firestore(), 'global', 'discussion', 'discussion', this.postId, 'comments', this.id).withConverter<DiscussionComment, DiscussionCommentDbModel>(new DiscussionCommentConverter());
  }

  public collection() {
    if (this.universityId) {
      return collection(firestore(), University.collectionName, this.universityId, 'discussion', this.postId, 'comments').withConverter<DiscussionComment, DiscussionCommentDbModel>(new DiscussionCommentConverter());
    }

    return collection(firestore(), 'global', 'discussion', 'discussion', this.postId, 'comments').withConverter<DiscussionComment, DiscussionCommentDbModel>(new DiscussionCommentConverter());
  }

  public async refresh() {
    const comment = await getDoc(this.doc());
    return comment.data()!;
  }

  public static getUploadsFolder(postId: string, commentId: string, universityId?: string) {
    if (universityId) {
      return `universities/${universityId}/discussion/posts/${postId}/comments/${commentId}`;
    }

    return `global/discussion/posts/${postId}/comments/${commentId}`;
  }

  public getUploadsFolder() {
    return DiscussionComment.getUploadsFolder(this.postId, this.id, this.universityId || undefined);
  }
}

export interface DiscussionCommentDbModel extends DiscussionPostBaseDbModel {}

export interface DiscussionCommentDbModelSafe extends DiscussionPostBaseDbModelSafe {}

export class DiscussionCommentConverter implements FirestoreDataConverter<DiscussionComment, DiscussionCommentDbModel> {
  toFirestore(comment: WithFieldValue<DiscussionComment>): WithFieldValue<DiscussionCommentDbModel> {
    return {
      attachments: comment.attachments,
      authorId: comment.authorId,
      datePosted: comment.datePosted instanceof FieldValue ? comment.datePosted : Timestamp.fromDate(comment.datePosted as Date),
      numUpVotes: (comment.upVotes as string[]).length,
      text: comment.text,
      upVotes: comment.upVotes,
    };
  }

  fromFirestore(snapshot: QueryDocumentSnapshot, options: SnapshotOptions): DiscussionComment {
    const data = snapshot.data(options) as DiscussionCommentDbModel;
    const parentCollection = snapshot.ref.parent.parent!.parent.parent!.id;

    return new DiscussionComment(snapshot.id, data.attachments, data.authorId, data.datePosted.toDate(), snapshot.ref.parent.parent!.id, data.text, parentCollection === 'discussion' ? null : parentCollection, data.upVotes);
  }
}
