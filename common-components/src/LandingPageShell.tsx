import {ReactNode} from 'react';
import Box from '@mui/joy/Box';
import {formLabelClasses} from '@mui/joy/FormLabel';
import {Navigate} from 'react-router-dom';
import {LoadingIndicator} from './LoadingIndicator';
import {useAuth} from './providers/AuthProvider';
import {ColorSchemeToggle} from './ColorSchemeToggle';
import {Footer} from './Footer';

interface Props {
  lightImageUrl: string;
  darkImageUrl: string;
  creatorCampusLogo: ReactNode;
  children: ReactNode;
}

export function LandingPageShell({lightImageUrl, darkImageUrl, creatorCampusLogo, children}: Props) {
  const {currentUser, loadingAuth} = useAuth();

  if (loadingAuth) {
    return (
      <Box sx={{height: '100vh', width: '50%', alignContent: 'center'}}>
        <LoadingIndicator />
      </Box>
    );
  }

  if (currentUser) {
    return <Navigate to='/' />;
  }

  return (
    <>
      <Box
        sx={{
          width: 'clamp(100vw - var(--Cover-width), (var(--Collapsed-breakpoint) - 100vw) * 999, 100vw)',
          transition: 'width var(--Transition-duration)',
          transitionDelay: 'calc(var(--Transition-duration) + 0.1s)',
          position: 'relative',
          zIndex: 1,
          display: 'flex',
          justifyContent: 'flex-end',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            minHeight: '100dvh',
            width: 'clamp(var(--Form-maxWidth), (var(--Collapsed-breakpoint) - 100vw) * 999, 100%)',
            maxWidth: '100%',
            px: 2,
          }}
        >
          <Box
            component='header'
            sx={{
              py: 3,
              display: 'flex',
              alignItems: 'left',
              justifyContent: 'space-between',
            }}
          >
            <Box>
              <Box sx={{display: 'flex', gap: 1, alignItems: 'center', mt: 1, ml: 'auto', mr: 'auto'}}>{creatorCampusLogo}</Box>
            </Box>
            <Box>
              <ColorSchemeToggle />
            </Box>
          </Box>
          <Box
            component='main'
            sx={{
              my: 'auto',
              pb: 5,
              display: 'flex',
              flexDirection: 'column',
              gap: 2,
              width: 435,
              maxWidth: '100%',
              mx: 'auto',
              borderRadius: 'sm',
              '& form': {
                display: 'flex',
                flexDirection: 'column',
                gap: 2,
              },
              [`& .${formLabelClasses.asterisk}`]: {
                visibility: 'hidden',
              },
            }}
          >
            {children}
          </Box>
          <Footer variant='minimal' />
        </Box>
      </Box>
      <Box
        sx={(theme) => ({
          height: '100%',
          position: 'fixed',
          right: 0,
          top: 0,
          bottom: 0,
          left: 'clamp(0px, (100vw - var(--Collapsed-breakpoint)) * 999, 100vw - var(--Cover-width))',
          transition: 'background-image var(--Transition-duration), left var(--Transition-duration) !important',
          transitionDelay: 'calc(var(--Transition-duration) + 0.1s)',
          backgroundColor: {xs: 'white', sm: theme.vars.palette.background.level1},
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
          backgroundImage: {xs: 'none', sm: lightImageUrl},
          [theme.getColorSchemeSelector('dark')]: {
            backgroundImage: {xs: 'none', sm: darkImageUrl},
          },
        })}
      />
    </>
  );
}
