{"name": "functions", "version": "0.0.0", "private": true, "scripts": {"build": "webpack", "deploy": "firebase deploy --only functions --project creator-campus-app && firebase deploy --only functions --project creator-campus-demo"}, "engines": {"node": "20"}, "main": "lib/index.js", "dependencies": {"@creator-campus/common": "*", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@influxdata/influxdb-client": "^1.35.0", "@mui/base": "^5.0.0-beta.69", "@mui/icons-material": "5.16.14", "@mui/material": "5.16.14", "@sentry/node": "^9.30.0", "@types/react": "^18.3.18", "algoliasearch": "^5.20.0", "dotenv": "^16.4.7", "firebase-admin": "^13.4.0", "firebase-functions": "^6.4.0", "jest": "^29.7.0", "react": "^18.3.1", "react-dom": "^18.3.1", "stripe": "^18.2.1", "url-metadata": "^4.1.1", "uuid": "^11.0.4"}, "devDependencies": {"@types/react-dom": "^18.3.5", "firebase-functions-test": "^3.4.0", "firebase-tools": "^14.11.0", "ts-loader": "^9.5.2", "tsx": "^4.19.2", "typescript": "^5.7.3", "webpack": "^5.97.1", "webpack-cli": "^6.0.1", "webpack-node-externals": "^3.0.0"}}