import {FormEvent, useState} from 'react';
import Button from '@mui/joy/Button';
import FormControl from '@mui/joy/FormControl';
import Form<PERSON>abel from '@mui/joy/FormLabel';
import Link from '@mui/joy/Link';
import Input from '@mui/joy/Input';
import Typography from '@mui/joy/Typography';
import Stack from '@mui/joy/Stack';
import {createUserWithEmailAndPassword, deleteUser, sendEmailVerification} from 'firebase/auth';
import {useNavigate} from 'react-router-dom';
import {Alert, FormHelperText} from '@mui/joy';
import {InfoOutlined} from '@mui/icons-material';
import {auth, logger, Role, User} from '@creator-campus/common';
import {LoadingIndicator, useSignupString, useSnackbar} from '@creator-campus/common-components';
import CreatePasswordInput from './CreatePasswordInput.tsx';
import {PASSWORD_INPUT_SLOT_PROPS} from '../constants/styles.ts';
import {TermsAndConditionsCheckbox} from './TermsAndConditionsCheckbox.tsx';
import {setDoc} from 'firebase/firestore';

interface FormElements extends HTMLFormControlsCollection {
  password: HTMLInputElement;
  confirmPassword: HTMLInputElement;
}

interface AccountFormElement extends HTMLFormElement {
  readonly elements: FormElements;
}

interface Props {
  email: string;
  universityId: string;
  role: Role;
}

export default function CreateAccountFromLinkForm({email, universityId, role}: Props) {
  const [passwordsMatch, setPasswordsMatch] = useState(true);
  const [accountAlreadyExists, setAccountAlreadyExists] = useState(false);
  const [longPassword, setLongPassword] = useState(true);

  const [loading, setLoading] = useState(false);

  const navigate = useNavigate();
  const {showErrorSnackbar} = useSnackbar();

  async function signUpNewUser(password: string) {
    await createUserWithEmailAndPassword(auth(), email, password)
      .then(async (creds) => {
        const authUser = creds.user;

        // Mentors are automatically accepted via a Firebase Function
        const user = User.defaults(authUser.uid, email, universityId).copyWith({role});

        const userDocCreated = await setDoc(User.doc(user.id), user)
          .then(() => {
            return true;
          })
          .catch(async (e) => {
            // If the auth account is created but the user doc is not, we have to try and
            // backtrack by deleting the auth account so the user can try again.
            logger.error(e);
            showErrorSnackbar(e, 'Error initialising user. Please try again.');
            await deleteUser(authUser).catch(logger.error);
            return false;
          });

        if (!userDocCreated) {
          return;
        }

        // If this email doesn't send, it's not a critical error so just keep going
        await sendEmailVerification(authUser);

        navigate('/onboarding');
      })
      .catch((e) => {
        if (e.code === 'auth/email-already-in-use') {
          setAccountAlreadyExists(true);
        } else {
          logger.error(e);
          showErrorSnackbar(e, 'Error signing up.');
        }
      });
  }

  function checkFormValid(password: string, confirmPassword: string) {
    const doPasswordsMatch = password === confirmPassword;
    setPasswordsMatch(doPasswordsMatch);

    const isLongPassword = password.length >= 8;
    setLongPassword(isLongPassword);

    return doPasswordsMatch && isLongPassword;
  }

  return (
    <Stack
      gap={4}
      sx={{mt: 2}}
    >
      {accountAlreadyExists && (
        <Alert color='danger'>
          <Typography
            level='title-sm'
            //@ts-ignore
            color='danger-500'
          >
            An account has already been created with that email. Please <Link href={'/login'}>sign in</Link> instead.
          </Typography>
        </Alert>
      )}
      <form
        onSubmit={async (event: FormEvent<AccountFormElement>) => {
          event.preventDefault();

          const formElements = event.currentTarget.elements;
          const data = {
            password: formElements.password.value,
            confirmPassword: formElements.confirmPassword.value,
          };

          if (checkFormValid(data.password, data.confirmPassword)) {
            setLoading(true);
            await signUpNewUser(data.password).catch((e) => showErrorSnackbar(e, 'Error signing up.'));
            setLoading(false);
          }
        }}
      >
        <FormControl required>
          <FormLabel>Email</FormLabel>
          <Input
            disabled
            value={email}
          />
        </FormControl>
        <CreatePasswordInput
          longPassword={longPassword}
          setLongPassword={setLongPassword}
        />
        <FormControl
          required
          error={!passwordsMatch}
        >
          <FormLabel>{useSignupString().confirm_password}</FormLabel>
          <Input
            type='password'
            name='confirmPassword'
            autoComplete='off'
            onChange={() => setPasswordsMatch(true)}
            slotProps={PASSWORD_INPUT_SLOT_PROPS}
          />
          {!passwordsMatch ? (
            <FormHelperText>
              {' '}
              <InfoOutlined />
              {useSignupString().password_match}
            </FormHelperText>
          ) : (
            <></>
          )}
        </FormControl>
        <Stack
          gap={3}
          sx={{mt: 1}}
        >
          <TermsAndConditionsCheckbox />
          <Button
            disabled={loading}
            type='submit'
            fullWidth
          >
            {loading ? <LoadingIndicator size='sm' /> : useSignupString().sign_up}
          </Button>
        </Stack>
        <Typography
          level='body-xs'
          textAlign='center'
        >
          {useSignupString().email_verification}
        </Typography>
      </form>
    </Stack>
  );
}
