import {useEffect, useState} from 'react';
import {getBlob, getDownloadURL, getStorage, ref, uploadBytesResumable} from 'firebase/storage';
import {DiscussionAttachment, DiscussionAttachmentType, DiscussionPostBase, logger, storage} from '@creator-campus/common';
import DiscussionImageAttachmentCard from './DiscussionImageAttachmentCard.tsx';
import DiscussionFileAttachmentCard from './DiscussionFileAttachmentCard.tsx';

export interface UploadableSettings {
  file: File;
  removeFile: () => void;
  onUploadSuccess: (fileId: string) => void;
}

export interface DownloadableSettings {
  metadata: DiscussionAttachment;
  post: DiscussionPostBase;
}

interface Props {
  attachmentId: string;
  fileType: DiscussionAttachmentType;
  universityId?: string;
  uploadable?: UploadableSettings;
  downloadable?: DownloadableSettings;
}

export default function DiscussionAttachmentCard({attachmentId, fileType, universityId, uploadable, downloadable}: Props) {
  if (!!uploadable === !!downloadable) {
    console.error('One of `uploadable` and `downloadable` must be specified.');
    return null;
  }

  // Uploadable
  const [uploadProgress, setUploadProgress] = useState<number | null>(0);
  const [uploadComplete, setUploadComplete] = useState<boolean>(false);

  // Downloadable
  const [downloadUrl, setDownloadUrl] = useState<string | null>(null);
  const [downloadInProgress, setDownloadInProgress] = useState<boolean>(false);

  useEffect(() => {
    if (uploadable) {
      // Preemptively start uploading file to Firebase Storage.
      // For now, upload to a temporary folder. Upon posting, a
      // Firebase Function will move it to a permanent folder.
      // Each week, a Firebase Function will clean up files that
      // have been in the temporary folder for more than a day.

      const pendingFile = ref(getStorage(), `${DiscussionPostBase.getPendingUploadsFolder(universityId)}/${attachmentId}`);
      const uploadTask = uploadBytesResumable(pendingFile, uploadable.file);
      const unsubscribe = uploadTask.on(
        'state_changed',
        (e) => {
          setUploadProgress(e.bytesTransferred / e.totalBytes);
        },
        (e) => {
          logger.error(e);
          setUploadProgress(null);
        },
        () => {
          setUploadComplete(true);
          uploadable.onUploadSuccess(attachmentId);
        },
      );

      return () => unsubscribe;
    } else if (fileType === 'image') {
      let retryCount = 0;

      const interval = setInterval(async () => {
        getDownloadURL(getStorageRef())
          .then((url) => {
            setDownloadUrl(url);
            return () => clearInterval(interval);
          })
          .catch(() => {
            retryCount++;
            if (retryCount > 10) {
              return () => clearInterval(interval);
            }
          });
      }, 500);

      return () => clearInterval(interval);
    }
  }, []);

  function getStorageRef() {
    return ref(storage(), `${downloadable!.post.getUploadsFolder()}/${attachmentId}`);
  }

  async function downloadAttachment() {
    setDownloadInProgress(true);

    const blob = await getBlob(getStorageRef());

    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', downloadable!.metadata.name);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    setDownloadInProgress(false);
  }

  if (fileType === 'image') {
    return (
      <DiscussionImageAttachmentCard
        downloadable={downloadable}
        uploadable={uploadable}
        downloadUrl={downloadUrl}
        downloadInProgress={downloadInProgress}
        uploadProgress={uploadProgress}
        uploadComplete={uploadComplete}
        downloadAttachment={downloadAttachment}
      />
    );
  }

  return (
    <DiscussionFileAttachmentCard
      downloadable={downloadable}
      uploadable={uploadable}
      downloadInProgress={downloadInProgress}
      uploadComplete={uploadComplete}
      uploadProgress={uploadProgress}
      downloadAttachment={downloadAttachment}
    />
  );
}
