import {<PERSON><PERSON>ens, CurrencyExchange, Lightbulb, People, ShowChart, Storefront} from '@mui/icons-material';
import {Navigate, useParams} from 'react-router-dom';
import {TabData} from '@creator-campus/common';
import {AdminInsightsTab, PageHeader, PageShell, Tabs, useActivateCommunityString, useUser} from '@creator-campus/common-components';
import {AdminStaffTab} from '../components/AdminStaffTab.tsx';
import {AdminBrandingTab} from '../components/AdminBrandingTab.tsx';
import {AdminShowcaseTab} from '../components/AdminShowcaseTab.tsx';
import {AdminMentorsTab} from '../components/AdminMentorsTab.tsx';
import {BlurryOverlay} from '../components/BlurryOverlay.tsx';
import {Box} from '@mui/joy';
import {AdminManageSubscriptionTab} from '../components/AdminManageSubscriptionTab.tsx';

export default function AdminPage() {
  const params = useParams();
  const {user, university} = useUser();
  const strings = useActivateCommunityString();

  if (!user || !university) {
    return <></>;
  }

  const tabs: TabData[] = [
    ...(user?.staffRole?.canEditBranding()
      ? [
          {
            displayName: 'Branding',
            leadingIcon: <ColorLens />,
            path: 'branding',
            content: <AdminBrandingTab />,
          },
        ]
      : []),
    {
      displayName: 'Insights',
      leadingIcon: <ShowChart />,
      path: 'insights',
      content: <AdminInsightsTab uniId={university.id} />,
    },
    ...(user?.staffRole?.canManageMentors()
      ? [
          {
            displayName: 'Mentors',
            leadingIcon: <Lightbulb />,
            path: 'mentors',
            content: <AdminMentorsTab university={university} />,
          },
        ]
      : []),
    ...(user?.staffRole?.canEditShowcase()
      ? [
          {
            displayName: 'Startup Showcase',
            leadingIcon: <Storefront />,
            path: 'startup-showcase',
            content: <AdminShowcaseTab university={university} />,
          },
        ]
      : []),
    // ...(user?.staffRole?.canReviewAlumniApplications()
    //   ? [
    //       {
    //         displayName: 'Alumni Applications',
    //         leadingIcon: <School />,
    //         trailingIcon: <AlumniCountChip universityId={university.id} />,
    //         path: 'alumni-applications',
    //         content: <AdminAlumniApplicationsTab />,
    //       },
    //     ]
    //   : []),
    ...(user?.staffRole?.canEditRoles()
      ? [
          {
            displayName: 'Community Admins',
            leadingIcon: <People />,
            path: 'staff',
            content: <AdminStaffTab university={university} />,
          },
        ]
      : []),
    ...(user?.staffRole?.canManageSubscription()
      ? [
          {
            displayName: 'Manage Subscription',
            leadingIcon: <CurrencyExchange />,
            path: 'manage-subscription',
            content: <AdminManageSubscriptionTab university={university} />,
          },
        ]
      : []),
  ];

  if (!user.staffRole) {
    return <Navigate to='/startups/explore' />;
  }

  return (
    <PageShell banners={{activateCommunity: {hideChildrenIfShown: false}}}>
      <PageHeader
        title={'Admin Dashboard'}
        sx={{mt: 1, px: {xs: 2, md: 6}}}
      />
      <Tabs
        value={`/admin/${params.tab}`}
        tabs={tabs.map((t) => ({
          ...t,
          path: `/admin/${t.path}`,
          content: (
            <Box px={t.path === 'insights' ? 0 : {xs: 0, sm: 1, md: 4}}>
              {university.partner || t.path === 'branding' || t.path === 'manage-subscription' ? (
                t.content
              ) : (
                <Box sx={{position: 'relative'}}>
                  <BlurryOverlay
                    subtitle={strings.overlay.admin_dashboard_subtitle}
                    blur={t.path === 'insights' ? '7px' : undefined}
                    button={'activate'}
                  >
                    {t.content}
                  </BlurryOverlay>
                </Box>
              )}
            </Box>
          ),
        }))}
      />
    </PageShell>
  );
}
