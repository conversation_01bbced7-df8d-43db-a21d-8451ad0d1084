import {ReactNode, useEffect, useState} from 'react';
import {doc, onSnapshot} from 'firebase/firestore';
import {firestore, MaintenanceMode, CREATOR_CAMPUS_ADMINS, MaintenanceModeConverter} from '@creator-campus/common';
import {useAuth} from '@creator-campus/common-components';
import {MaintenanceModePage} from '../pages/MaintenanceModePage.tsx';

interface Props {
  children: ReactNode;
}

export default function MaintenanceModeWrapper({children}: Props) {
  const [maintenanceMode, setMaintenanceMode] = useState<MaintenanceMode | null>(null);
  const {currentUser} = useAuth();

  useEffect(() => {
    const unsubscribe = onSnapshot(doc(firestore(), 'config', 'maintenanceMode').withConverter(new MaintenanceModeConverter()), (snapshot) => {
      const data = snapshot.data();
      if (data) {
        setMaintenanceMode(data);
      }
    });

    return () => unsubscribe();
  }, []);

  if (maintenanceMode?.enabled && !CREATOR_CAMPUS_ADMINS.includes(currentUser?.email || '')) {
    return <MaintenanceModePage />;
  }

  return <>{children}</>;
}
