import {onCall} from 'firebase-functions/v2/https';
import {<PERSON>Val<PERSON>, Filter, Timestamp} from 'firebase-admin/firestore';
import {v4 as uuidv4} from 'uuid';
import {onSchedule} from 'firebase-functions/v2/scheduler';
import * as admin from 'firebase-admin';
import {AlumniApplication, AlumniApplicationAcceptedEmailTemplate, AlumniApplicationRejectedEmailTemplate, ApprovedAlumnus, NewAlumniApplicationsNotificationEmailTemplate, StaffRole, University, User} from '@creator-campus/common';
import {deleteExpiredDocs, getMetricsCollection, sendEmail} from './utils';

export const usingEmulators = !!process.env.FIRESTORE_EMULATOR_HOST;

export const submitAlumniApplication = onCall(async (request) => {
  // The max number of submissions per IP address per day
  const MAX_SUBMISSIONS_PER_DAY = 10;

  // IP is undefined when using emulators
  const userIp = request.rawRequest.ip || (request.data.usingEmulators && 'emulated');
  const data: AlumniApplication = request.data;

  const uniDoc = admin.firestore().collection(University.collectionName).doc(data.universityId);
  const applicationsCollection = uniDoc.collection('alumniApplications');

  // Check if an application with this email has already been submitted
  const existingApplicationSnapshot = await applicationsCollection.where('email', '==', data.email).get();
  if (existingApplicationSnapshot.size > 0) {
    return {
      success: false,
      message: 'An application with this email has already been submitted.',
    };
  }

  // Check if an application with this email has already been approved
  const approvedAlumniSnapshot = await admin.firestore().collection('approvedAlumni').where('email', '==', data.email).get();
  if (approvedAlumniSnapshot.size > 0) {
    return {
      success: false,
      message: 'Your application has already been accepted. Check your email for a sign-up link!',
    };
  }

  // Only allow x applications from each IP per day
  if (!userIp) {
    console.error('Request IP is undefined.');
    return {
      success: false,
      message: 'Something went wrong on our end. Please try again.',
    };
  }

  const now = Timestamp.now();
  const startOfDay = new Date();
  startOfDay.setUTCHours(0, 0, 0, 0);

  // Fetch applications from the same IP submitted today
  const snapshot = await applicationsCollection.where('ip', '==', userIp).where('ts', '>=', Timestamp.fromDate(startOfDay)).count().get();

  if (snapshot.data().count >= MAX_SUBMISSIONS_PER_DAY) {
    return {
      success: false,
      message: 'You have reached the daily submission limit. Try again tomorrow.',
    };
  }

  await applicationsCollection.add({
    name: data.name,
    email: data.email,
    studentNumber: data.studentNumber,
    graduationYear: data.graduationYear,
    ip: userIp,
    ts: now,
  });

  await getMetricsCollection(data.universityId).update({
    'alumni.totalAlumniApplications': FieldValue.increment(1),
  });

  return {
    success: true,
    message: 'Application submitted successfully.',
  };
});

export const decideAlumniApplication = onCall(async (request) => {
  const userId = request.auth?.uid;
  if (!userId) {
    return {
      success: false,
      message: 'Unauthenticated request.',
    };
  }

  const callerDoc = await admin.firestore().collection(User.collectionName).doc(userId).get();
  const callerHasPermissions = [StaffRole.ADMIN.id, StaffRole.OWNER.id].includes(callerDoc.get('staffRole'));
  if (!callerHasPermissions) {
    return {
      success: false,
      message: 'Insufficient permissions.',
    };
  }

  const applicationId = request.data.applicationId;

  const uniDoc = admin.firestore().collection(University.collectionName).doc(request.data.universityId);
  const applicationsCollection = uniDoc.collection('alumniApplications');
  const applicationSnapshot = await applicationsCollection.doc(applicationId).get();

  const application = applicationSnapshot.data() as AlumniApplication | undefined;
  if (!application) {
    return {
      success: false,
      message: 'Application no longer exists.',
    };
  }

  const acceptApplication = request.data.accept === true;

  if (acceptApplication) {
    await getMetricsCollection(uniDoc.id).update({
      'alumni.numAlumniApproved': FieldValue.increment(1),
    });

    const token = uuidv4();
    const oneDay = 24 * 60 * 60 * 1000;
    const daysValid = 14;
    const expiresAt = new Date(Date.now() + daysValid * oneDay);

    const approvedAlumnus: ApprovedAlumnus = {
      email: application.email,
      token,
      expiresAt: expiresAt,
      universityId: request.data.universityId,
    };

    const approvedAlumnusDoc = await admin.firestore().collection('approvedAlumni').add(approvedAlumnus);

    const link = `https://app.creatorcampus.io/create-alumnus-account?token=${token}&alumnusId=${approvedAlumnusDoc.id}`;
    if (usingEmulators) {
      console.log(`Generated alumnus account creation link: ${link}`);
    }

    await sendEmail(
      application.email,
      new AlumniApplicationAcceptedEmailTemplate({
        name: application.name || 'User',
        link,
        daysValid,
      }),
    ).catch(console.error);
  } else {
    await getMetricsCollection(uniDoc.id).update({
      'alumni.numAlumniRejected': FieldValue.increment(1),
    });

    await sendEmail(
      application.email,
      new AlumniApplicationRejectedEmailTemplate({
        name: application.name || 'User',
      }),
    ).catch(console.error);
  }

  await applicationsCollection.doc(applicationId).delete();

  const msSpentUndecided = new Date().getTime() - application.ts.getTime();
  const hoursSpentUndecided = msSpentUndecided / 1000 / 60 / 60;
  await getMetricsCollection(uniDoc.id).update({
    'alumni.hoursAlumniApplicationsSpentUndecided': FieldValue.increment(hoursSpentUndecided),
  });

  return {
    success: true,
    message: `Application ${acceptApplication ? 'accepted' : 'rejected'}.`,
  };
});

export const cleanUpExpiredAlumniApprovals = onSchedule('every day 18:00', async () => {
  const numExpiredByUniversity = await deleteExpiredDocs('approvedAlumni');

  const promises = [];
  for (const [universityId, numExpired] of Object.entries(numExpiredByUniversity)) {
    promises.push(
      getMetricsCollection(universityId).update({
        'alumni.numApprovedAlumniExpired': FieldValue.increment(numExpired),
      }),
    );
  }

  await Promise.all(promises);
});

export const validateAlumnusToken = onCall(async (request) => {
  if (!request.data.alumnusId || !request.data.token) {
    return {valid: false};
  }

  const approvedAlumnusDoc = admin.firestore().collection('approvedAlumni').doc(request.data.alumnusId);

  const snapshot = await approvedAlumnusDoc.get();
  if (!snapshot.exists) {
    return {valid: false};
  }

  const data = snapshot.data() as ApprovedAlumnus;

  if (data.token !== request.data.token) {
    return {valid: false};
  }

  await approvedAlumnusDoc.delete();

  return {
    valid: true,
    ...data,
  };
});

export const sendNewAlumniApplicationsNotifications = onSchedule('every day 09:00', async () => {
  // Get a list of every university
  const unisSnapshot = await admin.firestore().collection(University.collectionName).get();
  const uniIds = unisSnapshot.docs.map((d) => d.id);

  const notificationPromises = [];

  for (const uniId of uniIds) {
    notificationPromises.push(
      (async () => {
        // Fetch the number of pending alumni applications for each university
        console.log(`Fetching alumni applications for university ${uniId}...`);
        const alumniApplicationsSnapshot = await admin.firestore().collection(University.collectionName).doc(uniId).collection('alumniApplications').count().get();

        const numPendingApplications = alumniApplicationsSnapshot.data().count;
        console.log(`Found ${numPendingApplications} pending alumni applications.`);

        if (numPendingApplications > 0) {
          // Fetch a list of staff for this university
          console.log(`Fetching staff for university ${uniId}...`);
          const staffSnapshot = await admin
            .firestore()
            .collection(User.collectionName)
            .where('universityId', '==', uniId)
            .where(Filter.or(Filter.where('staffRole', '==', 'admin'), Filter.where('staffRole', '==', 'owner')))
            .get();

          // Email each staff member
          for (const staffDoc of staffSnapshot.docs) {
            const staffData = staffDoc.data();
            console.log(`Sending email to staff member ${staffDoc.id}...`);

            await sendEmail(
              staffData.email,
              new NewAlumniApplicationsNotificationEmailTemplate({
                name: staffData.name || 'User',
                numPendingApplications,
              }),
            ).catch(console.error);
          }
        }
      })(),
    );
  }

  // Await all emails to be sent
  try {
    await Promise.all(notificationPromises);
    console.log('All emails sent successfully.');
  } catch (error) {
    console.error('Error sending emails:', error);
  }
});
