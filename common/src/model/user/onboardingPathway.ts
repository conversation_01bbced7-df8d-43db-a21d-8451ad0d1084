import {ElementType} from 'react';
import {Favorite, Person, RocketLaunch} from '@mui/icons-material';
import AutoAwesome from '@mui/icons-material/AutoAwesome';

export type OnboardingPathwayId = 'founder' | 'talent' | 'supporter';

export class OnboardingPathway {
  static readonly TALENT = new OnboardingPathway('talent', 'Talent', 'Talent', AutoAwesome);
  static readonly FOUNDER = new OnboardingPathway('founder', 'Founder', 'Founders', RocketLaunch);
  static readonly SUPPORTER = new OnboardingPathway('supporter', 'Supporter', 'Supporters', Favorite);

  private constructor(
    readonly id: OnboardingPathwayId,
    readonly label: string,
    readonly labelPlural: string,
    readonly icon: ElementType,
  ) {}

  static fromId(id: string | null) {
    return this.values().find((c) => c.id === id) || null;
  }

  static values(): OnboardingPathway[] {
    return Object.values(OnboardingPathway);
  }
}
