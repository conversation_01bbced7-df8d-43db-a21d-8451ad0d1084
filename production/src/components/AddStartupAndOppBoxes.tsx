import Box from '@mui/joy/Box';
import Divider from '@mui/joy/Divider';
import {DashedClickableBox} from './DashedClickableBox.tsx';
import Stack from '@mui/joy/Stack';
import {AddCircle, DashboardCustomize} from '@mui/icons-material';
import Typography from '@mui/joy/Typography';
import {useEffect, useState} from 'react';
import CreateEditProjectModal from './modals/CreateEditProjectModal.tsx';
import {logger, MembershipApplication, Opportunity, Project, User} from '@creator-campus/common';
import {LoadingIndicator, useScreenWidth} from '@creator-campus/common-components';
import {updateDoc} from 'firebase/firestore';
import ProjectCard from './ProjectCard.tsx';

interface Props {
  user: User;
  membershipApplication: MembershipApplication;
}

export function AddStartupAndOppBoxes({user, membershipApplication}: Props) {
  const [loading, setLoading] = useState<boolean>(false);
  const [addStartupModalOpen, setAddStartupModalOpen] = useState<boolean>(false);
  const [project, setProject] = useState<Project | null>(null);
  const [opportunity, setOpportunity] = useState<Opportunity | null>(null);

  const screenWidth = useScreenWidth();
  const isLargeScreen = screenWidth > 650;

  const lineColor = '#f5773b';
  const lineThickness = 2;
  const leftMargin = -30;
  const tickLength = 20;

  useEffect(() => {
    async function fetchApplicationProjectAndOpp() {
      setLoading(true);

      const promises: Promise<any>[] = [];

      if (membershipApplication?.projectId) {
        promises.push(Project.fetch(membershipApplication.projectId).then(setProject));
      }

      if (membershipApplication?.opportunityId) {
        promises.push(Opportunity.fetch(membershipApplication.opportunityId).then(setOpportunity));
      }

      await Promise.all(promises);
      setLoading(false);
    }

    fetchApplicationProjectAndOpp().catch(logger.error);
  }, []);

  if (loading) {
    return <LoadingIndicator size={'md'} />;
  }

  return (
    <Stack spacing={1.5}>
      {addStartupModalOpen && (
        <CreateEditProjectModal
          onClose={() => setAddStartupModalOpen(false)}
          onSave={async (_event, createdProject) => {
            // When saving, either createdProject or project is not null
            const p = (createdProject || (await Project.fetch(project!.id)))!;
            await updateDoc(MembershipApplication.doc(user.id), {projectId: p.id});
            setProject(p);
          }}
          hiddenProject={true}
        />
      )}
      {isLargeScreen && (
        <>
          <Box sx={{position: 'absolute', top: 145, left: leftMargin, width: lineThickness, height: 236, backgroundColor: lineColor}} />
          <Box sx={{position: 'absolute', top: 132, left: leftMargin, width: tickLength, height: lineThickness, backgroundColor: lineColor, borderRadius: '9999px'}} />
          {!membershipApplication.projectId && <Box sx={{position: 'absolute', top: 300, left: leftMargin, width: tickLength, height: lineThickness, backgroundColor: lineColor, borderRadius: '9999px'}} />}
          <Box sx={{position: 'absolute', top: 368, left: leftMargin, width: tickLength, height: lineThickness, backgroundColor: lineColor, borderRadius: '9999px'}} />
        </>
      )}
      <Divider />
      {project ? (
        <ProjectCard
          projectId={project.id}
          editMode={true}
          privacyMode={true}
          maxOpportunities={1}
          cardSx={{borderColor: '#f6651f', borderWidth: 1.5}}
          onOppChanged={async (event, createdOpp) => {
            const opp = event === 'remove' ? null : event === 'add' ? createdOpp : await Opportunity.fetch(opportunity!.id);
            await updateDoc(MembershipApplication.doc(user.id), {opportunityId: opp?.id});
            setOpportunity(opp);
          }}
          onDeleted={async () => {
            await updateDoc(MembershipApplication.doc(user.id), {projectId: null});
            setProject(null);
          }}
        />
      ) : (
        <>
          <DashedClickableBox onClick={() => setAddStartupModalOpen(true)}>
            <Stack
              direction={'row'}
              spacing={1}
              alignItems={'center'}
            >
              <DashboardCustomize />
              <Typography level={'title-sm'}>Add your startup</Typography>
            </Stack>
          </DashedClickableBox>
          <DashedClickableBox disabled={!project}>
            <Stack
              direction={'row'}
              spacing={1}
              alignItems={'center'}
            >
              <AddCircle />
              <Typography level={'title-sm'}>Add an opportunity</Typography>
            </Stack>
          </DashedClickableBox>
        </>
      )}
      <Divider />
    </Stack>
  );
}
