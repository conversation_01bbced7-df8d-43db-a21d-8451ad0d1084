export class SubscriptionTier {
  static readonly INACTIVE = new SubscriptionTier('Inactive', 'You are yet to activate your community.', 'neutral', 0, [], []);

  static readonly PARTNER = new SubscriptionTier(
    'Partner',
    'Full access to partnership features.',
    'warning',
    1,
    ['Customise your university branding', 'Reach members directly by signposting your services in platform emails', 'Analytics and insights for easier reporting', 'Private group discussion for your university', 'Access to the Enterprise Educators Vault', 'Startup showcase to share with stakeholders'],
    [],
  );

  private constructor(
    readonly label: string,
    readonly description: string,
    readonly color: 'success' | 'warning' | 'neutral' | 'danger' | 'primary',
    readonly value: number,
    readonly benefits: string[],
    readonly missingBenefits: string[],
  ) {}

  static values(): SubscriptionTier[] {
    return Object.values(SubscriptionTier);
  }
}
