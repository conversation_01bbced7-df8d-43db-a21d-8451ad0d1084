import {CREATOR_CAMPUS_ADMINS, DiscussionNewPostsSummaryDoubleEmailTemplate, DiscussionNewPostsSummaryEmailTemplate, DiscussionPostBase, DiscussionPostBasic, University, User, UserDbModelSafe} from '@creator-campus/common';
import {onSchedule} from 'firebase-functions/v2/scheduler';
import {CollectionReference} from 'firebase-admin/firestore';
import * as admin from 'firebase-admin';
import {sendEmail} from './utils';
import {onRequest} from 'firebase-functions/v2/https';
// import {OpenAI} from 'openai';

const OPENAI_ORG = 'Creator Campus';
const OPENAI_PROJECT = 'proj_o3Jnv5tbnpJ5598d0SJVLO9i';

/**
 * Clears pending discussion attachment uploads for a given university.
 * @param universityId Leave empty to clear pending uploads for global discussion.
 */
async function clearPendingUploads(universityId?: string) {
  const pendingUploadsFolder = DiscussionPostBase.getPendingUploadsFolder(universityId);
  const [pendingUploads] = await admin.app().storage().bucket().getFiles({prefix: pendingUploadsFolder});

  const now = new Date();

  for (const pendingUpload of pendingUploads) {
    if (!pendingUpload.metadata.timeCreated) {
      await pendingUpload.delete();
      continue;
    }

    const timeCreated = new Date(pendingUpload.metadata.timeCreated);
    const moreThanADayOld = now.getTime() - timeCreated.getTime() > 24 * 60 * 60 * 1000;
    if (moreThanADayOld) {
      await pendingUpload.delete();
    }
  }
}

export const clearPendingDiscussionAttachmentUploads = onSchedule('0 9 * * 0', async () => {
  const universitiesSnapshot = await admin.firestore().collection(University.collectionName).get();
  const deletePromises: Promise<any>[] = [clearPendingUploads()];

  for (const university of universitiesSnapshot.docs) {
    deletePromises.push(clearPendingUploads(university.id));
  }

  await Promise.all(deletePromises);
});

async function emailSendingEnabled() {
  // Check if email-sending is enabled
  const configSnapshot = await admin.firestore().collection('config').doc('discussionSummaryEmails').get();
  return configSnapshot.data()?.sendingEnabled;
}

export const sendDailyPostSummaryNotifications = onSchedule(
  {
    secrets: ['OPENAI_API_KEY'],
    schedule: 'every day 18:00',
  },
  async () => {
    const isEmailSendingEnabled = await emailSendingEnabled();
    if (!isEmailSendingEnabled) {
      console.warn('Email sending is disabled. Skipping...');
      return;
    }

    // const openaiClient = new OpenAI({
    //   apiKey: process.env.OPENAI_API_KEY,
    //   organization: OPENAI_ORG,
    //   project: OPENAI_PROJECT,
    // });

    // Fetch users who want daily notifications
    console.log('Fetching users who want daily notifications...');
    const usersSnapshot = await admin.firestore().collection(User.collectionName).where('notifications.discussionNewPosts', '==', 'daily').get();
    console.log(`Found ${usersSnapshot.size} users.`);

    const users = usersSnapshot.docs.map((d) => d.data() as UserDbModelSafe);
    await sendPostSummaryEmails(users, 1);
  },
);

export const sendWeeklyPostSummaryNotifications = onSchedule(
  {
    secrets: ['OPENAI_API_KEY'],
    schedule: '0 18 * * 0', // Every Sunday at 18:00
  },
  async () => {
    const isEmailSendingEnabled = await emailSendingEnabled();
    if (!isEmailSendingEnabled) {
      console.warn('Email sending is disabled. Skipping...');
      return;
    }

    // const openaiClient = new OpenAI({
    //   apiKey: process.env.OPENAI_API_KEY,
    //   organization: OPENAI_ORG,
    //   project: OPENAI_PROJECT,
    // });

    // Fetch users who want weekly notifications
    console.log('Fetching users who want weekly notifications...');
    const usersSnapshot = await admin.firestore().collection(User.collectionName).where('notifications.discussionNewPosts', '==', 'weekly').get();
    console.log(`Found ${usersSnapshot.size} users.`);

    const users = usersSnapshot.docs.map((d) => d.data() as UserDbModelSafe);
    await sendPostSummaryEmails(users, 7);
  },
);

export const testSendPostSummaryNotifications = onRequest(async (req, res) => {
  // const openaiClient = new OpenAI({
  //   apiKey: process.env.OPENAI_API_KEY,
  //   organization: OPENAI_ORG,
  //   project: OPENAI_PROJECT,
  // });

  console.log('Fetching Creator Campus admins...');
  const ccAdminsSnapshot = await admin.firestore().collection(User.collectionName).where('email', 'in', CREATOR_CAMPUS_ADMINS).get();
  console.log(`Found ${ccAdminsSnapshot.size} Creator Campus admins.`);

  const ccAdmins = ccAdminsSnapshot.docs.map((d) => d.data() as UserDbModelSafe);
  await sendPostSummaryEmails(ccAdmins, 1);

  res.send('OK');
});

async function fetchTopPostAndSummary(universityId: string | 'global', maxDaysAgo: number, topPosts: Record<string, DiscussionPostBasic[]>, summaries: Record<string, string>) {
  const numPosts = 10;
  console.log(`(${universityId}) Fetching top ${numPosts} posts in the last ${maxDaysAgo} days...`);

  const discussionCollection = universityId === 'global' ? admin.firestore().collection('global').doc('discussion').collection('discussion') : admin.firestore().collection(University.collectionName).doc(universityId).collection('discussion');
  const uniTopPosts = await getTopPosts(discussionCollection, {maxDaysAgo, numPosts});
  console.log(`(${universityId}) Found ${uniTopPosts.length} post(s).`);

  if (uniTopPosts.length > 0) {
    topPosts[universityId] = uniTopPosts;
    console.log(`First post text: ${uniTopPosts[0].text}`);

    console.log(`(${universityId}) Generating summary of posts...`);
    summaries[universityId] = await getGenAiPostsSummary('temp', uniTopPosts);
    console.log(`(${universityId}) Generated summary: ${summaries[universityId]}`);
  }
}

async function sendPostSummaryEmails(users: UserDbModelSafe[], maxDaysAgo: number) {
  // For each university, fetch the top discussion posts and generate a summary of them
  const topPosts: Record<string, DiscussionPostBasic[]> = {};
  const summaries: Record<string, string> = {};
  const universityIds = new Set(users.map((u) => u.universityId));

  for (const universityId of universityIds) {
    await fetchTopPostAndSummary(universityId, maxDaysAgo, topPosts, summaries);
  }

  // Fetch summary of recent global discussion
  await fetchTopPostAndSummary('global', maxDaysAgo, topPosts, summaries);
  const recentGlobalDiscussion = !!summaries['global'];

  // Send email to each user
  const promises = [];
  for (const user of users) {
    const recentUniDiscussion = !!summaries[user.universityId];

    if (recentGlobalDiscussion && recentUniDiscussion) {
      promises.push(
        sendEmail(
          user.email,
          new DiscussionNewPostsSummaryDoubleEmailTemplate({
            username: user.name,
            university: {
              name: user.universityId,
              summary: summaries[user.universityId],
              topPost: topPosts[user.universityId]?.[0],
            },
            global: {
              summary: summaries['global'],
              topPost: topPosts['global']?.[0],
            },
          }),
        ).catch(console.error),
      );
    } else if (recentGlobalDiscussion || recentUniDiscussion) {
      promises.push(
        sendEmail(
          user.email,
          new DiscussionNewPostsSummaryEmailTemplate({
            username: user.name,
            variant: recentUniDiscussion ? 'university' : 'global',
            universityName: user.universityId,
            summary: recentUniDiscussion ? summaries[user.universityId] : summaries['global'],
            topPost: recentUniDiscussion ? topPosts[user.universityId]?.[0] : topPosts['global']?.[0],
          }),
        ).catch(console.error),
      );
    }
  }

  console.log('Emailing users post summary for their university...');
  await Promise.all(promises);
  console.log('Done!');
}

async function getGenAiPostsSummary(openaiClient: any, posts: DiscussionPostBasic[]) {
  // const postMessages = posts.map((p) => p.text);
  // const prompt = `Write a short, concise summary of the following discussion posts:\n\n${postMessages.join('\n\n')}`;
  //
  // const response = await openaiClient.responses.create({
  //   model: 'gpt-5-nano-2025-08-07',
  //   input: prompt,
  // });
  //
  // return response.output_text;

  return "We're still working on this feature!";
}

async function getTopPosts(discussionCollection: CollectionReference, settings: {maxDaysAgo: number; numPosts: number}): Promise<DiscussionPostBasic[]> {
  const now = new Date();
  const maxMsAgo = settings.maxDaysAgo * 24 * 60 * 60 * 1000;

  const snapshot = await discussionCollection
    .where('datePosted', '>', new Date(now.getTime() - maxMsAgo))
    .orderBy('numUpVotes', 'desc')
    .limit(settings.numPosts)
    .get();

  return snapshot.docs.map((post) => {
    const data = post.data();
    const datePosted: Date = data.datePosted.toDate();

    return {
      text: data.text,
      datePosted: datePosted.toDateString(),
      numUpVotes: data.numUpVotes,
    };
  });
}
