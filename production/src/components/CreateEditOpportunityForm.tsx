import {<PERSON><PERSON><PERSON>perText, Stack} from '@mui/joy';
import Typography from '@mui/joy/Typography';
import {Compensation, Location, logger, Opportunity, Project} from '@creator-campus/common';
import {LoadingIndicator, useCreateOpportunityString, useNewProjectModalString, useSnackbar} from '@creator-campus/common-components';
import Button from '@mui/joy/Button';
import * as React from 'react';
import {useState} from 'react';
import FormControl from '@mui/joy/FormControl';
import FormLabel from '@mui/joy/FormLabel';
import Textarea from '@mui/joy/Textarea';
import Tooltip from '@mui/joy/Tooltip';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import Link from '@mui/joy/Link';
import Chip from '@mui/joy/Chip';
import {Check} from '@mui/icons-material';

interface Props {
  project: Project;
  initialOpp?: Opportunity;
  onClose: (success: boolean, createdOpp: Opportunity | null) => Promise<void> | void;
  onBack?: () => void;
}

export function CreateEditOpportunityForm({project, initialOpp, onClose, onBack}: Props) {
  const [title, setTitle] = useState<string>(initialOpp?.title ?? '');
  const [description, setDescription] = useState<string>(initialOpp?.description ?? '');
  const [compensation, setCompensation] = useState<Compensation[]>(initialOpp?.compensation || []);
  const [location, setLocation] = useState<Location | null>(initialOpp?.location || null);

  const [titleOkay, setTitleOkay] = useState<boolean>(true);
  const [descriptionOkay, setDescriptionOkay] = useState<boolean>(true);
  const [compensationOkay, setCompensationOkay] = useState<boolean>(true);
  const [locationOkay, setLocationOkay] = useState<boolean>(true);

  const [loading, setLoading] = useState<boolean>(false);

  const {showErrorSnackbar} = useSnackbar();

  function handleTitleChange(event: React.ChangeEvent<HTMLTextAreaElement>) {
    setTitle(event.target.value);
    setTitleOkay(true);
  }

  function handleDescriptionChange(event: React.ChangeEvent<HTMLTextAreaElement>) {
    setDescription(event.target.value);
    setDescriptionOkay(true);
  }

  function handleCompensationChange(value: Compensation) {
    setCompensationOkay(true);
    setCompensation((prev) => {
      if (!prev) {
        // Add initial value
        return [value];
      }

      if (prev.includes(value)) {
        // Remove value
        return prev.filter((c) => c !== value);
      }

      // Add value
      return [...prev.filter((c) => !value.isExclusiveWith(c) && !c.isExclusiveWith(value)), value];
    });
  }

  function handleLocationChange(value: Location) {
    setLocationOkay(true);
    setLocation(value);
  }

  async function handleSubmit() {
    const isTitleOkay = title.length <= Opportunity.MAX_TITLE_LEN;
    const isDescOkay = description.length <= Opportunity.MAX_DESC_LEN;
    const isCompOkay = compensation.length > 0;
    const isLocOkay = !!location;

    setTitleOkay(isTitleOkay);
    setDescriptionOkay(isDescOkay);
    setCompensationOkay(isCompOkay);
    setLocationOkay(isLocOkay);

    if (isTitleOkay && isDescOkay && isCompOkay && isLocOkay) {
      if (initialOpp) {
        const success = await initialOpp
          .update(compensation, description, location, title)
          .then(() => {
            return true;
          })
          .catch((e) => {
            logger.error(e);
            showErrorSnackbar(e, 'Error updating opportunity.');
            return false;
          });

        await onClose(success, null);
      } else {
        const createdOpp = await Opportunity.create(project, compensation, description, location, title).catch((e) => {
          logger.error(e);
          showErrorSnackbar(e, 'Error adding opportunity.');
          return null;
        });

        await onClose(!!createdOpp, createdOpp);
      }
    }
  }

  return (
    <>
      <FormControl
        sx={{mt: 3}}
        error={!titleOkay}
      >
        <FormLabel>{useCreateOpportunityString().title}</FormLabel>
        <Textarea
          name='title'
          placeholder='Who are you looking for? (e.g. Technical Co-Founder)'
          variant='outlined'
          required
          defaultValue={title}
          onChange={(event) => handleTitleChange(event)}
          endDecorator={
            <Typography
              level='body-xs'
              sx={{ml: 'auto'}}
            >
              {title.length} / {Opportunity.MAX_TITLE_LEN}
            </Typography>
          }
        />
        {titleOkay ? <></> : <FormHelperText sx={{mt: 0.75, fontSize: 'xs'}}>Title must be shorter than {Opportunity.MAX_TITLE_LEN} characters.</FormHelperText>}
      </FormControl>
      <FormControl
        sx={{mt: 1}}
        error={!descriptionOkay}
      >
        <FormLabel>
          {useCreateOpportunityString().description}
          <Tooltip
            enterTouchDelay={0}
            title={useCreateOpportunityString().description_guidance}
            variant='solid'
          >
            <InfoOutlinedIcon fontSize='large' />
          </Tooltip>
        </FormLabel>
        <Textarea
          name='description'
          variant='outlined'
          required
          minRows={3}
          defaultValue={description}
          onChange={(event) => handleDescriptionChange(event)}
          endDecorator={
            <Typography
              level='body-xs'
              sx={{ml: 'auto'}}
            >
              {description.length} / {Opportunity.MAX_DESC_LEN}
            </Typography>
          }
        />
        {descriptionOkay ? <></> : <FormHelperText sx={{mt: 0.75, fontSize: 'xs'}}>Description must be shorter than {Opportunity.MAX_DESC_LEN} characters.</FormHelperText>}
      </FormControl>
      <FormControl
        sx={{mt: 1}}
        error={!compensationOkay}
      >
        <FormLabel>
          {useCreateOpportunityString().compensation}
          <Link
            href='https://app.creatorcampus.io/education/Co-Founder%20Guide/How%20do%20I%20successfully%20bring%20a%20co-founder%20onboard'
            target='_blank'
            underline='hover'
            level='body-xs'
          >
            - Guidance
          </Link>
        </FormLabel>
        <Stack
          spacing={1}
          direction={'row'}
        >
          {Compensation.values().map((comp) => {
            const isSelected = compensation?.includes(comp);

            return (
              <Chip
                key={comp.id}
                startDecorator={<comp.icon />}
                endDecorator={isSelected ? <Check /> : null}
                color={isSelected ? 'primary' : 'neutral'}
                variant={isSelected ? 'solid' : 'outlined'}
                onClick={() => handleCompensationChange(comp)}
              >
                {comp.label}
              </Chip>
            );
          })}
        </Stack>
        {!compensationOkay ? <FormHelperText>You must select a compensation method.</FormHelperText> : <></>}
      </FormControl>
      <FormControl
        sx={{mt: 1}}
        error={!locationOkay}
      >
        <FormLabel>{useCreateOpportunityString().location}</FormLabel>
        <Stack
          spacing={1}
          direction={'row'}
        >
          {Location.values().map((loc) => (
            <Chip
              key={loc.id}
              startDecorator={<loc.icon />}
              endDecorator={location === loc ? <Check /> : null}
              color={location === loc ? 'primary' : 'neutral'}
              variant={location === loc ? 'solid' : 'outlined'}
              onClick={() => handleLocationChange(loc)}
            >
              {loc.label}
            </Chip>
          ))}
        </Stack>
        {!locationOkay ? <FormHelperText>You must select a location type.</FormHelperText> : <></>}
      </FormControl>

      <Stack
        direction={'row'}
        spacing={1}
        sx={{mt: 2}}
      >
        <Button
          disabled={loading}
          color='primary'
          variant='solid'
          onClick={async () => {
            setLoading(true);
            await handleSubmit();
            setLoading(false);
          }}
        >
          {loading ? <LoadingIndicator size={'sm'} /> : initialOpp ? 'Save' : useNewProjectModalString().create}
        </Button>
        <Button
          variant='outlined'
          color='neutral'
          onClick={async () => await onClose(false, null)}
        >
          Cancel
        </Button>
        {onBack && (
          <Button
            variant='outlined'
            color='neutral'
            onClick={onBack}
          >
            Back
          </Button>
        )}
      </Stack>
    </>
  );
}
