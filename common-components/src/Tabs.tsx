import TabList from '@mui/joy/TabList';
import Tab, {tabClasses} from '@mui/joy/Tab';
import {Link} from 'react-router-dom';
import {ListItemDecorator, TabPanel} from '@mui/joy';
import JoyTabs from '@mui/joy/Tabs';
import { TabData } from '@creator-campus/common';

interface Props {
  tabs: TabData[];
  value?: string;
  leftPadding?: number;
}

export function Tabs({tabs, value, leftPadding = 4}: Props) {
  return (
    <JoyTabs
      value={value}
      sx={{
        bgcolor: 'transparent',
      }}
    >
      <TabList
        tabFlex={1}
        size='sm'
        sx={{
          display: 'flex',
          flexWrap: 'wrap',
          rowGap: 0.5,
          pl: {xs: 0, md: leftPadding},
          justifyContent: 'left',
          [`&& .${tabClasses.root}`]: {
            fontWeight: '600',
            flex: 'initial',
            color: 'text.tertiary',
            [`&.${tabClasses.selected}`]: {
              bgcolor: 'transparent',
              color: 'text.primary',
              '&::after': {
                height: '2px',
                bgcolor: 'primary.500',
              },
            },
          },
        }}
      >
        {tabs.map((tab, i) => (
          <Tab
            key={i}
            sx={{borderRadius: '6px 6px 0 0'}}
            indicatorInset
            value={tab.path}
            component={Link}
            to={tab.path}
          >
            {tab.leadingIcon && <ListItemDecorator>{tab.leadingIcon}</ListItemDecorator>}
            {tab.displayName}
            {tab.trailingIcon}
          </Tab>
        ))}
      </TabList>
      {tabs.map((tab, i) => (
        <TabPanel
          key={i}
          value={tab.path}
          sx={{px: {xs: 1, sm: 2}, py: 3}}
        >
          {tab.content}
        </TabPanel>
      ))}
    </JoyTabs>
  );
}
