import {useEffect, useState} from 'react';
import Card from '@mui/joy/Card';
import Divider from '@mui/joy/Divider';
import Typography from '@mui/joy/Typography';
import Avatar from '@mui/joy/Avatar';
import Box from '@mui/joy/Box';
import IconButton from '@mui/joy/IconButton';

import {AddCircle, Edit, Language, Share, Star} from '@mui/icons-material';
import AccordionIndicator from './AccordionIndicator';
import {CardActions, Chip, Stack} from '@mui/joy';
import OpportunityCard from './OpportunityCard.tsx';
import {onSnapshot} from 'firebase/firestore';
import ProjectOwnerChip from './ProjectOwnerChip.tsx';
import {useNavigate} from 'react-router-dom';
import {CREATOR_CAMPUS_ADMINS, logger, Opportunity, Project, User} from '@creator-campus/common';
import {LoadingIndicator, useAuth, useSnackbar} from '@creator-campus/common-components';
import CreateEditProjectModal from './modals/CreateEditProjectModal.tsx';
import Button from '@mui/joy/Button';
import {CreateEditOpportunityModal} from './modals/CreateEditOpportunityModal.tsx';
import {DeleteProjectDialog} from './modals/DeleteProjectDialog.tsx';
import {DashedClickableBox} from './DashedClickableBox.tsx';
import {FeatureContentButton} from './FeatureContentButton.tsx';
import {UniversityChip} from './UniversityChip.tsx';

interface Props {
  // The ID of the project displayed in this ProjectCard
  projectId: string;
  // Provide for a static component, leave undefined to get real-time updates based on the given `projectId`
  hit?: Project;
  // Whether to display the 'editing' variant of the ProjectCard
  editMode?: boolean;
  // Whether personal information (e.g. the owner's full name) should be hidden
  privacyMode?: boolean;
  withFeatureButton?: boolean;
  withOpportunities?: boolean;
  withEditButton?: boolean;
  maxOpportunities?: number;
  cardSx?: object;
  onOppChanged?: (event: 'add' | 'edit' | 'remove', createdOpp: Opportunity | null) => void;
  onDeleted?: () => void;
}

export default function ProjectCard({projectId, hit, editMode = false, privacyMode = false, withFeatureButton = false, withOpportunities = true, withEditButton = true, maxOpportunities = 20, cardSx = {}, onOppChanged, onDeleted}: Props) {
  const [project, setProject] = useState<Project | null>(hit || null);
  const [opportunities, setOpportunities] = useState<Opportunity[] | null>(null);
  const [logoUrl, setLogoUrl] = useState<string>('');
  const [owner, setOwner] = useState<User | null>(null);

  // Modals
  const [editModalOpen, setEditModalOpen] = useState<boolean>(false);
  const [addOppModalOpen, setAddOppModalOpen] = useState<boolean>(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState<boolean>(false);

  // Hooks
  const {currentUser} = useAuth();
  const navigate = useNavigate();
  const {showErrorSnackbar, showSnackbar} = useSnackbar();

  /**
   * Fetches and sets the URL of the project's logo for use by image components.
   */
  useEffect(() => {
    if (project) {
      project
        .getLogoUrl()
        .then((url) => {
          setLogoUrl(url);
        })
        .catch((e) => {
          logger.error(e);
        });
    }
  }, [project]);

  /**
   * Subscribes to receive real-time snapshots of the project if only the project ID was given.
   */
  useEffect(() => {
    if (!hit) {
      const unsub = onSnapshot(Project.doc(projectId), (snapshot) => {
        setProject(snapshot.data() || null);
      });

      return () => unsub();
    }
  }, []);

  /**
   * Fetches all Opportunities related to this project.
   */
  useEffect(() => {
    if (hit) {
      Opportunity.fetchForProject(projectId).then((opps) => {
        setOpportunities(opps);
      });
    } else {
      const unsub = onSnapshot(Opportunity.queryForProject(projectId), (snapshot) => {
        const opps = snapshot.docs.map((d) => d.data());
        setOpportunities(opps);
      });

      return () => unsub();
    }
  }, []);

  /**
   * Subscribes to receive real-time snapshots of the project owner's Firestore document.
   */
  useEffect(() => {
    if (project) {
      const unsub = onSnapshot(User.doc(project.ownerId), (snapshot) => {
        setOwner(snapshot.data() || null);
      });

      return () => unsub();
    }
  }, [project?.ownerId]);

  async function handleCopy(p: Project) {
    await navigator.clipboard
      .writeText(p.getLink(true))
      .then(() => {
        showSnackbar('Project link copied to clipboard.');
      })
      .catch((e) => {
        logger.error(e);
        showErrorSnackbar(e, 'Error copying project link.');
      });
  }

  if (!project) {
    // Project document is still being fetched
    return <></>;
  }

  function NameText({name}: {name: string}) {
    return (
      <Typography
        level='title-lg'
        sx={{ml: '10px'}}
      >
        {name}
      </Typography>
    );
  }

  function DesktopProjectCard({project}: {project: Project}) {
    return (
      <>
        {/* Top section containing project logo, name, summary, and tags */}
        <Box sx={{display: 'flex', justifyContent: 'flex-start', alignItems: 'flex-start'}}>
          <Box sx={{display: 'flex', justifyContent: 'flex-start', alignItems: 'center'}}>
            {/* Project logo/avatar */}
            <Avatar
              src={logoUrl}
              size='lg'
              sx={{'--Avatar-size': {xs: '60px', sm: '80px'}}}
            />

            {/* Project details: name, summary, and tags */}
            <Stack sx={{ml: '10px'}}>
              <Typography level='title-lg'>{project.name}</Typography>
              <Typography level='body-sm'>{project.summary}</Typography>
              <Stack
                direction='row'
                spacing={1}
                useFlexGap
                flexWrap='wrap'
                mt={0.4}
              >
                {/* Featured chip */}
                {project.featuredUntil && (
                  <Chip
                    startDecorator={<Star />}
                    color='primary'
                    variant='outlined'
                  >
                    Featured
                  </Chip>
                )}

                {/* University ID chip */}
                <UniversityChip universityId={project.universityId} />

                {/* Project tag chips */}
                {project.tags.map((tag) => (
                  <Chip
                    key={tag.label}
                    startDecorator={<tag.icon text={tag.label} />}
                    variant='outlined'
                  >
                    {tag.label}
                  </Chip>
                ))}

                {/* Project owner chip (if not in privacy mode) */}
                {!privacyMode && owner && <ProjectOwnerChip owner={owner} />}
              </Stack>
            </Stack>
          </Box>

          {/* Action buttons: Edit, Website, Share */}
          <Box sx={{marginLeft: 'auto', display: 'flex'}}>
            {/* IconButton to switch to edit mode */}
            {currentUser?.uid === project.ownerId && !editMode && withEditButton && (
              <IconButton
                variant='plain'
                onClick={() => navigate('/startups/my-startups')}
                color='primary'
              >
                <Edit />
              </IconButton>
            )}

            {/* IconButton to open the project website in a new tab */}
            {project.website && (
              <IconButton
                variant='plain'
                onClick={() => window.open(project.website!, '_blank')}
              >
                <Language />
              </IconButton>
            )}

            {/* IconButton to copy link to share project */}
            {!privacyMode && (
              <IconButton
                variant='plain'
                onClick={() => handleCopy(project)}
              >
                <Share />
              </IconButton>
            )}
          </Box>
        </Box>

        {/* 'Read more' button to expand project description */}
        <AccordionIndicator
          summary='Read more'
          text={project.description}
        />
      </>
    );
  }

  function MobileProjectCard({project}: {project: Project}) {
    return (
      <>
        {/* Top section with logo and name */}
        <Box sx={{display: 'flex', justifyContent: 'flex-start', alignItems: 'flex-start'}}>
          <Box sx={{display: 'flex', justifyContent: 'flex-start', alignItems: 'center'}}>
            {/* Project logo/avatar */}
            <Avatar
              src={logoUrl}
              size='lg'
            />

            {/* Project name */}
            <Box>
              <NameText name={project.name} />
              <Box sx={{ml: '10px'}}>
                {/* Project tag chips */}
                {project.tags.map((tag) => (
                  <Chip
                    key={tag.label}
                    startDecorator={<tag.icon text={tag.label} />}
                  >
                    {tag.label}
                  </Chip>
                ))}
              </Box>
            </Box>
          </Box>

          {/* Share button */}
          <Stack
            direction={'row'}
            spacing={1}
            sx={{marginLeft: 'auto'}}
          >
            {currentUser?.uid === project.ownerId && !editMode && withEditButton ? (
              <IconButton
                variant='plain'
                onClick={() => navigate('/startups/my-startups')}
                color='primary'
              >
                <Edit />
              </IconButton>
            ) : (
              <>
                {project.website && (
                  <IconButton
                    variant='plain'
                    onClick={() => window.open(project.website!, '_blank')}
                  >
                    <Language />
                  </IconButton>
                )}
                {!privacyMode && (
                  <IconButton
                    variant='plain'
                    onClick={() => handleCopy(project)}
                  >
                    <Share />
                  </IconButton>
                )}
              </>
            )}
          </Stack>
        </Box>

        {/* Project summary */}
        <Typography
          level='body-sm'
          sx={{mt: 1}}
        >
          {project.summary}
        </Typography>

        {/* Tags section */}
        <Stack
          direction='row'
          spacing={1}
          useFlexGap
          flexWrap='wrap'
          sx={{mt: 1, mb: 1}}
        >
          {/* University ID chip */}
          <UniversityChip universityId={project.universityId} />

          {/* Project owner chip (if not in privacy mode) */}
          {!privacyMode && owner && <ProjectOwnerChip owner={owner} />}
        </Stack>

        {/* 'Read more' button to expand project description */}
        <AccordionIndicator
          summary='Read more'
          text={project.description}
        />
      </>
    );
  }

  return (
    <Card
      color={project.featuredUntil ? 'primary' : 'neutral'}
      variant={project.featuredUntil ? 'soft' : 'outlined'}
      sx={{
        maxWidth: '100%',
        overflowWrap: 'break-word',
        wordBreak: 'break-word',
        ...cardSx,
        minWidth: 300,
      }}
    >
      {withFeatureButton && CREATOR_CAMPUS_ADMINS.includes(currentUser?.email || '') && (
        <Box sx={{position: 'absolute', top: -16, right: 8}}>
          <FeatureContentButton content={project} />
        </Box>
      )}

      {/* Mobile view */}
      <Box sx={{display: {xs: 'block', sm: 'none'}}}>
        <MobileProjectCard project={project} />
      </Box>

      {/* Desktop view */}
      <Box sx={{display: {xs: 'none', sm: 'block'}}}>
        <DesktopProjectCard project={project} />
      </Box>

      {/* List of opportunity cards related to this project */}
      {withOpportunities && (
        <>
          <Divider />
          <Stack spacing={2}>
            {opportunities === null ? (
              <LoadingIndicator size={'sm'} />
            ) : (
              opportunities.map((opp) => {
                // Hide filled opportunities for everyone but the owner
                if (opp.fillDate && !editMode) {
                  return null;
                }

                return (
                  <OpportunityCard
                    key={`${project.id}-${opp.id}`}
                    opp={opp}
                    project={project}
                    editMode={editMode}
                    onOppChanged={(event) => {
                      if (event === 'remove') {
                        showSnackbar('Opportunity removed.', 'success');
                      }

                      if (onOppChanged) {
                        onOppChanged(event, opp);
                      }
                    }}
                  />
                );
              })
            )}

            {/* Button to add a new opportunity if edit mode */}
            {editMode && opportunities && opportunities.length < maxOpportunities ? (
              <>
                <DashedClickableBox onClick={() => setAddOppModalOpen(true)}>
                  <Stack
                    direction={'row'}
                    spacing={1}
                    alignItems={'center'}
                  >
                    <AddCircle />
                    <Typography level={'title-sm'}>Add an opportunity</Typography>
                  </Stack>
                </DashedClickableBox>

                {/* Opportunity creation modal */}
                {addOppModalOpen && (
                  <CreateEditOpportunityModal
                    project={project}
                    onClose={(success, createdOpp) => {
                      setAddOppModalOpen(false);
                      if (success) {
                        showSnackbar('Opportunity added.', 'success');
                      }

                      if (onOppChanged) {
                        onOppChanged('add', createdOpp);
                      }
                    }}
                  />
                )}
              </>
            ) : (
              (hit?.numOpenOpportunities === 0 || opportunities?.length === 0) && <Typography level='body-sm'>Currently no opportunities.</Typography>
            )}
          </Stack>
        </>
      )}

      {editMode && <Divider />}

      {/* Buttons to edit/delete project */}
      {editMode && owner && (
        <CardActions sx={{display: 'flex', justifyContent: 'center'}}>
          {/* Edit project button */}
          <Button
            variant='soft'
            color='neutral'
            onClick={() => setEditModalOpen(true)}
          >
            Edit startup
          </Button>

          {/* Edit project modal */}
          {editModalOpen && (
            <CreateEditProjectModal
              initialProject={project}
              onClose={() => setEditModalOpen(false)}
              onSave={() => {
                showSnackbar('Project saved.', 'success');
                setEditModalOpen(false);
              }}
            />
          )}

          {/* Delete project button */}
          <Button
            variant='soft'
            color='danger'
            onClick={() => setDeleteModalOpen(true)}
          >
            Delete startup
          </Button>

          {/* Edit project modal */}
          {deleteModalOpen && (
            <DeleteProjectDialog
              project={project}
              onClose={(deleted) => {
                if (deleted && onDeleted) {
                  onDeleted();
                }

                setDeleteModalOpen(false);
              }}
            />
          )}
        </CardActions>
      )}
    </Card>
  );
}
