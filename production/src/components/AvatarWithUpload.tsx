import Avatar from '@mui/joy/Avatar';
import Box from '@mui/joy/Box';
import {EditRounded} from '@mui/icons-material';
import {useEffect, useRef} from 'react';

interface Props {
  profilePicUrl?: string;
  selectedImageUrl?: string;
  onImageSelected: (image: File) => void;
}

export default function AvatarWithUpload({profilePicUrl, selectedImageUrl, onImageSelected}: Props) {
  const inputRef = useRef<HTMLInputElement | null>(null);
  const imageSource = selectedImageUrl || profilePicUrl;

  useEffect(() => {
    // Reset input value (e.g., when reverting changes)
    // to allow selecting the same image again
    if (inputRef.current) {
      inputRef.current.value = '';
    }
  }, [selectedImageUrl]);

  return (
    <label style={{cursor: 'pointer', width: 120, height: 120, borderRadius: '100%', position: 'relative'}}>
      <Box sx={{width: '100%', height: '100%'}}>
        {imageSource ? (
          <img
            src={imageSource}
            alt='Profile Preview'
            style={{width: '100%', height: '100%', objectFit: 'cover', borderRadius: '100%'}}
          />
        ) : (
          <Avatar
            size='lg'
            sx={{
              width: '100%',
              height: '100%',
              padding: 3,
            }}
          />
        )}
        <input
          ref={inputRef}
          type='file'
          accept='image/*'
          style={{display: 'none'}}
          onChange={(event) => {
            const selectedImage = event.target.files?.[0];
            if (selectedImage) {
              onImageSelected(selectedImage);
            }
          }}
        />
        <Box>
          <EditRounded
            sx={{
              bgcolor: 'background.body',
              position: 'absolute',
              bottom: 4,
              left: 90,
              borderRadius: '50%',
              boxShadow: 'sm',
              pointerEvents: 'none',
              padding: 0.5,
              width: 32,
              height: 32,
            }}
          />
        </Box>
      </Box>
    </label>
  );
}
