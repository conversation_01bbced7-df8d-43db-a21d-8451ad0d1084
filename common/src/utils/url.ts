import {trimChar} from './misc';

export function isUrlValid(url: string): boolean {
  const result = url.match(/(http(s)?:\/\/.)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/g);
  return result !== null;
}

export function getGithubUsernameFromUrl(url: string | null | undefined) {
  if (!url) {
    return null;
  }

  url = trimChar(url, '/');

  if (url === 'https://github.com' || url === 'http://github.com') {
    return null;
  }

  const urlParts = url.split('/');
  if (urlParts.length === 1) {
    // Url IS username
    return url;
  }

  return urlParts[urlParts.length - 1];
}

export function getLinkedinUsernameFromUrl(url: string | null | undefined) {
  if (!url) {
    return null;
  }

  url = trimChar(url, '/');

  if (url === 'https://linkedin.com/in' || url === 'http://linkedin.com/in') {
    return null;
  }

  const urlParts = url.split('/');
  if (urlParts.length === 1) {
    // Url IS username
    return url;
  }

  return urlParts[urlParts.length - 1];
}
