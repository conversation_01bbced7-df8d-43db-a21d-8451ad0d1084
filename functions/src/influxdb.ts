import {InfluxDB, Point} from '@influxdata/influxdb-client';
import {influxUrl, influxOrg, influxBucket, influxMeasurement} from '@creator-campus/common';

const writeOptions = {
  flushInterval: 5000, // flush every 5s if there are points to send
  maxRetries: 3,
  defaultTags: {}, // default tags added to each point
};

export const newPoint = () => new Point(influxMeasurement);

export const getInfluxWriter = (token: string) => {
  const client = new InfluxDB({
    url: influxUrl,
    token: token,
  });

  return client.getWriteApi(influxOrg, influxBucket, undefined, writeOptions);
};
