{"private": true, "scripts": {"build": "yarn workspaces foreach --all --topological run build", "buildCommon": "yarn workspace @creator-campus/common build && yarn workspace @creator-campus/common-components build", "buildFunctions": "yarn buildCommon && yarn workspace functions build", "dev": "cd production && npm run dev", "admin": "cd admin && npm run dev", "production": "cd production && npm run dev", "cypress": "cd production && firebase emulators:exec --only auth,firestore,functions,hosting,storage,pubsub \"npm run dev\"", "emulators": "cd common && npm run emulators", "emulatorsSaveData": "cd common && npm run emulatorsSaveData", "migrateEmulators": "curl -X GET \"http://localhost:5001/creator-campus-app/europe-west2/migration\"", "migrateProduction": "gcloud functions call migration --region europe-west2 --project creator-campus-app && gcloud functions call migration --region europe-west2 --project creator-campus-demo", "migrate": "yarn migrateEmulators", "killPorts": "lsof -t -i tcp:9099 | xargs kill && lsof -t -i tcp:8080 | xargs kill && lsof -t -i tcp:5000 | xargs kill && lsof -t -i tcp:5001 | xargs kill && lsof -t -i tcp:5002 | xargs kill && lsof -t -i tcp:4000 | xargs kill && lsof -t -i tcp:9199 | xargs kill && lsof -t -i tcp:8085 | xargs kill", "format": "biome format", "format:apply": "biome format --write", "lint": "biome lint", "lint:apply": "biome lint --write"}, "workspaces": ["admin", "common", "common-components", "functions", "production"], "packageManager": "yarn@4.9.4", "devDependencies": {"@biomejs/biome": "1.9.4", "lefthook": "^1.11.0"}}