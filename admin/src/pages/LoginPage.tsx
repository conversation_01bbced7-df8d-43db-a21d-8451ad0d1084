import * as React from 'react';
import {useState} from 'react';
import Button from '@mui/joy/Button';
import FormControl from '@mui/joy/FormControl';
import FormLabel from '@mui/joy/FormLabel';
import Input from '@mui/joy/Input';
import Typography from '@mui/joy/Typography';
import Stack from '@mui/joy/Stack';
import {signInWithEmailAndPassword} from 'firebase/auth';
import {auth} from '@creator-campus/common';
import {LandingPageShell, LoadingIndicator, useLoginString} from '@creator-campus/common-components';
import {Alert} from '@mui/joy';
import {useNavigate} from 'react-router-dom';
import {CreatorCampusLogo} from '../components/CreatorCampusLogo.tsx';

interface FormElements extends HTMLFormControlsCollection {
  email: HTMLInputElement;
  password: HTMLInputElement;
  persistent: HTMLInputElement;
}

interface SignInFormElement extends HTMLFormElement {
  readonly elements: FormElements;
}

export default function LoginPage() {
  const [loading, setLoading] = useState(false);
  const [unauthorized, setUnauthorized] = useState(false);

  const navigate = useNavigate();

  async function signInUser(email: string, password: string) {
    setUnauthorized(false);
    setLoading(true);

    if (!email.endsWith('creatorcampus.io')) {
      setUnauthorized(true);
    } else {
      await signInWithEmailAndPassword(auth(), email, password)
        .then((creds) => {
          if (!creds.user.emailVerified) {
            setUnauthorized(true);
            return;
          }

          navigate(`/dashboard`);
        })
        .catch(() => {
          setUnauthorized(true);
        });
    }

    setLoading(false);
  }

  return (
    <LandingPageShell
      lightImageUrl={'url(https://firebasestorage.googleapis.com/v0/b/creator-campus-app.appspot.com/o/site-images%2Flogin-light.png?alt=media&token=d94bd7be-da3a-4bbe-a14c-88b64ed06ee2)'}
      darkImageUrl={'url(https://firebasestorage.googleapis.com/v0/b/creator-campus-app.appspot.com/o/site-images%2Flogin-dark.png?alt=media&token=2ca307c8-e592-4551-8893-a3ec7d408197)'}
      creatorCampusLogo={<CreatorCampusLogo size='40px' />}
    >
      <Typography level='h3'>{useLoginString().sign_in}</Typography>
      <Stack
        gap={4}
        sx={{mt: 2}}
      >
        {unauthorized ? <Alert color='danger'>Unauthorized.</Alert> : <></>}
        <form
          onSubmit={async (event: React.FormEvent<SignInFormElement>) => {
            event.preventDefault();
            const formElements = event.currentTarget.elements;
            const data = {
              email: formElements.email.value,
              password: formElements.password.value,
              // persistent: formElements.persistent.checked,
            };

            await signInUser(data.email, data.password);
          }}
        >
          <FormControl required>
            <FormLabel>{useLoginString().email}</FormLabel>
            <Input
              type='email'
              name='email'
            />
          </FormControl>
          <FormControl required>
            <FormLabel>{useLoginString().password}</FormLabel>
            <Input
              type='password'
              name='password'
            />
          </FormControl>
          <Button
            disabled={loading}
            type='submit'
            fullWidth
          >
            {loading ? <LoadingIndicator size='sm' /> : useLoginString().sign_in}
          </Button>
        </form>
      </Stack>
    </LandingPageShell>
  );
}
