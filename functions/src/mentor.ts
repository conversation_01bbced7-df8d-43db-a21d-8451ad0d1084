import {onCall} from 'firebase-functions/v2/https';
import * as admin from 'firebase-admin';
import {ApprovedMentor} from '@creator-campus/common';
import {onSchedule} from 'firebase-functions/v2/scheduler';
import {deleteExpiredDocs, initSentryForFunction} from './utils';
import * as Sentry from '@sentry/node';

export const validateMentorToken = onCall(async (request) => {
  initSentryForFunction('validateMentorToken');

  Sentry.addBreadcrumb({
    message: 'Validating mentor token',
    category: 'function',
    level: 'info',
    data: {
      mentorId: request.data.mentorId,
      hasToken: !!request.data.token,
      authUid: request.auth?.uid
    }
  });

  if (!request.data.mentorId || !request.data.token) {
    console.error(`At least one required argument is undefined. mentorId=${request.data.mentorId}, token=${request.data.token}`);

    Sentry.addBreadcrumb({
      message: 'Missing required arguments for mentor token validation',
      category: 'validation',
      level: 'error',
      data: {
        hasMentorId: !!request.data.mentorId,
        hasToken: !!request.data.token
      }
    });

    return {valid: false};
  }

  const approvedMentorDoc = admin.firestore().collection('approvedMentors').doc(request.data.mentorId);

  Sentry.addBreadcrumb({
    message: 'Fetching approved mentor document',
    category: 'firestore',
    level: 'info',
    data: { mentorId: request.data.mentorId }
  });

  const snapshot = await approvedMentorDoc.get();
  if (!snapshot.exists) {
    Sentry.addBreadcrumb({
      message: 'Approved mentor document not found',
      category: 'validation',
      level: 'warning',
      data: { mentorId: request.data.mentorId }
    });

    return {valid: false};
  }

  const data = snapshot.data() as ApprovedMentor;

  if (data.token !== request.data.token) {
    console.error(`Tokens do not match. mentorId=${request.data.mentorId}, token=${request.data.token}`);

    Sentry.addBreadcrumb({
      message: 'Mentor token mismatch',
      category: 'validation',
      level: 'error',
      data: {
        mentorId: request.data.mentorId,
        providedToken: request.data.token,
        expectedToken: data.token
      }
    });

    return {valid: false};
  }

  Sentry.addBreadcrumb({
    message: 'Mentor token validated successfully, deleting approval document',
    category: 'mentor',
    level: 'info',
    data: {
      mentorId: request.data.mentorId,
      mentorEmail: data.email,
      universityId: data.universityId
    }
  });

  await approvedMentorDoc.delete();

  Sentry.addBreadcrumb({
    message: 'Mentor token validation completed successfully',
    category: 'mentor',
    level: 'info',
    data: {
      mentorId: request.data.mentorId,
      mentorEmail: data.email,
      universityId: data.universityId
    }
  });

  return {
    valid: true,
    ...data,
  };
});

export const cleanUpExpiredMentorApprovals = onSchedule('every day 18:00', async () => {
  initSentryForFunction('cleanUpExpiredMentorApprovals');

  Sentry.addBreadcrumb({
    message: 'Starting cleanup of expired mentor approvals',
    category: 'scheduler',
    level: 'info'
  });

  await deleteExpiredDocs('approvedMentors');

  Sentry.addBreadcrumb({
    message: 'Completed cleanup of expired mentor approvals',
    category: 'scheduler',
    level: 'info'
  });
});
