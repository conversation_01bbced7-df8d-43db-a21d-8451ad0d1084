import Card from '@mui/joy/Card';
import Typography from '@mui/joy/Typography';
import {Stack} from '@mui/joy';
import {UserHit} from '@creator-campus/common';
import {useState} from 'react';
import UserDetailsModal from './UserDetailsModal.tsx';
import Chip from '@mui/joy/Chip';

interface Props {
  userHit: UserHit;
  sx?: object;
}

export default function UserCard({userHit, sx}: Props) {
  const [showUserDetailsModal, setShowUserDetailsModal] = useState<boolean>(false);

  return (
    <>
      {showUserDetailsModal && (
        <UserDetailsModal
          userHit={userHit}
          onClose={() => setShowUserDetailsModal(false)}
        />
      )}
      <Card
        onClick={() => setShowUserDetailsModal(true)}
        sx={{
          cursor: 'pointer',
          transition: 'background-color 0.2s',
          '&:hover': {
            backgroundColor: 'rgba(0, 0, 0, 0.05)',
          },
          ...sx,
        }}
      >
        {/* User details */}
        <Stack sx={{mx: 1}}>
          <Stack
            direction={'row'}
            spacing={1}
            alignItems={'center'}
          >
            <Typography level='title-lg'>{userHit.name}</Typography>
            <Chip
              variant={'soft'}
              size={'sm'}
              color={userHit.profileCompleted ? 'success' : 'warning'}
            >
              {userHit.profileCompleted ? 'Profile complete' : 'Profile incomplete'}
            </Chip>
          </Stack>
          <Typography level='body-sm'>{userHit.id}</Typography>
          <Typography level='body-sm'>{userHit.universityId}</Typography>
        </Stack>
      </Card>
    </>
  );
}
