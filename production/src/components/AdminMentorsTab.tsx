import {Input, Stack} from '@mui/joy';
import {ApprovedMentor, firestore, logger, Role, University, User} from '@creator-campus/common';
import {LoadingIndicator, useSnackbar, useUser} from '@creator-campus/common-components';
import {useEffect, useState} from 'react';
import Button from '@mui/joy/Button';
import IconButton from '@mui/joy/IconButton';
import {Delete} from '@mui/icons-material';
import Typography from '@mui/joy/Typography';
import Divider from '@mui/joy/Divider';
import InviteMentorsModal, {DAYS_MENTOR_INVITE_VALID} from './modals/InviteMentorsModal.tsx';
import {deleteDoc, doc, getDoc, setDoc, updateDoc} from 'firebase/firestore';
import {ResendMentorInviteButton} from './ResendMentorInviteButton.tsx';
import {MentorInviteStatusText} from './MentorInviteStatusText.tsx';

interface Mentor {
  email: string;
  status: 'pending' | 'active';
}

interface Props {
  university: University;
}

export function AdminMentorsTab({university}: Props) {
  const [savedMentors, setSavedMentors] = useState<Mentor[] | null>(null);
  const [editedMentors, setEditedMentors] = useState<Mentor[] | null>(null);
  const [showInviteMentorsModal, setShowInviteMentorsModal] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);

  const {user} = useUser();
  const {showSnackbar} = useSnackbar();

  const allChangesSaved = JSON.stringify(editedMentors?.filter((m) => m.email.length > 0)) === JSON.stringify(savedMentors?.filter((m) => m.email.length > 0));
  const anyDuplicateEmails = editedMentors && new Map(editedMentors.map((m) => [m!.email, m])).size !== editedMentors.length;

  useEffect(() => {
    university.getMentors().then((activeMentors) => {
      university.getPendingMentors().then((pendingMentors) => {
        const mentors: Mentor[] = [...activeMentors.map((m): Mentor => ({email: m.email, status: 'active'})), ...pendingMentors.map((m): Mentor => ({email: m.email, status: 'pending'}))];

        setSavedMentors(mentors);
        setEditedMentors([...mentors, defaultMentor()]);
      });
    });
  }, [university]);

  async function fetchMentorDoc(mentor: Mentor) {
    if (mentor.status === 'pending') {
      const snapshot = await getDoc(doc(firestore(), 'approvedMentors', mentor.email));

      return snapshot.exists() ? snapshot.ref : null;
    }

    const user = await User.fromEmail(mentor.email);
    return user?._doc;
  }

  /**
   * This is used to put a blank input box at the end of the list.
   */
  function defaultMentor(): Mentor {
    return {
      email: '',
      status: 'pending',
    };
  }

  function getAddedMentors() {
    // Find difference between original and edited
    const originalMentorIds = savedMentors!.map((m) => m.email);
    return editedMentors!.filter((m) => !originalMentorIds.includes(m.email) && m.email.length > 0);
  }

  function getRemovedMentors() {
    // Find difference between original and edited
    const finalMentorIds = editedMentors!.map((m) => m.email);
    return savedMentors!.filter((m) => !finalMentorIds.includes(m.email));
  }

  async function updateMentorsInFirestore(emailTokenPairs: Record<string, string>) {
    const promises = [];

    // Add newly added mentors to Firestore
    logger.debug('Sending mentor invites:', Object.keys(emailTokenPairs));
    for (const [email, token] of Object.entries(emailTokenPairs)) {
      if (email === user!.email) {
        // In this case, token is actually the Calendly URL
        promises.push(updateDoc(user!._doc, {role: Role.MENTOR.id, website: token}));
        continue;
      }

      promises.push(
        (async () => {
          const oneDay = 24 * 60 * 60 * 1000;
          const expiresAt = new Date(Date.now() + DAYS_MENTOR_INVITE_VALID * oneDay);

          const approvedMentor: ApprovedMentor = {
            email,
            token,
            expiresAt,
            universityId: university.id,
          };

          await setDoc(doc(firestore(), 'approvedMentors', email), approvedMentor);
        })(),
      );
    }

    // Delete removed mentors from Firestore
    logger.debug(
      'Removing mentors:',
      getRemovedMentors().map((m) => m.email),
    );
    for (const mentor of getRemovedMentors()) {
      if (mentor.email === user!.email) {
        logger.debug('Removing ourselves as a mentor, updating role to staff...');
        promises.push(updateDoc(user!._doc, {role: Role.STAFF.id, website: null}));
        continue;
      }

      promises.push(
        (async () => {
          const removedMentorDoc = await fetchMentorDoc(mentor);
          if (removedMentorDoc) {
            await deleteDoc(removedMentorDoc);
          }
        })(),
      );
    }

    // Wait for all operations to finish
    await Promise.all(promises);
  }

  function isDuplicateMentor(mentorIndex: number) {
    if (!editedMentors) {
      return false;
    }

    const targetMentor = editedMentors[mentorIndex];

    for (let i = 0; i < mentorIndex; i++) {
      if (editedMentors[i].email === targetMentor.email) {
        return true;
      }
    }

    return false;
  }

  function handleEmailChange(index: number, newEmail: string) {
    setEditedMentors((prev) => {
      const updatedMentors = prev!.map((m, i) => (i === index ? {email: newEmail, status: m.status} : m));

      // Remove trailing empty inputs
      while (!updatedMentors[updatedMentors.length - 1].email && updatedMentors.length > 1) {
        updatedMentors.pop();
      }

      // If the last input is being edited and isn't empty, append a new empty input
      const lastInput = updatedMentors[updatedMentors.length - 1];
      if (lastInput.email) {
        updatedMentors.push(defaultMentor());
      }

      return updatedMentors;
    });
  }

  function handleDelete(mentor: Mentor) {
    setEditedMentors((prev) => prev!.filter((prevMentors) => prevMentors.email !== mentor.email));
  }

  async function handleSave() {
    if (getAddedMentors().length > 0) {
      setShowInviteMentorsModal(true);
    } else {
      setLoading(true);
      await updateMentorsInFirestore({});
      postSave();
      setLoading(false);
    }
  }

  function postSave() {
    setSavedMentors(editedMentors);
    setShowInviteMentorsModal(false);
    showSnackbar('Changes saved.', 'success');
  }

  if (editedMentors === null || !user) {
    return <LoadingIndicator size={'md'} />;
  }

  return (
    <>
      {showInviteMentorsModal && (
        <InviteMentorsModal
          onClose={() => setShowInviteMentorsModal(false)}
          afterInvitesSent={async (emailTokenPairs: Record<string, string>) => {
            await updateMentorsInFirestore(emailTokenPairs);
            postSave();
          }}
          newMentorEmails={getAddedMentors().map((m) => m.email)}
          myEmail={user.email}
          university={university}
        />
      )}
      <Stack
        spacing={1.5}
        sx={{maxWidth: 750}}
      >
        <Stack spacing={0.2}>
          <Typography level={'title-sm'}>Invite and manage mentors to support your community</Typography>
          <Typography
            level={'body-sm'}
            sx={{mb: {xs: 1.5, sm: 0}}}
          >
            Mentors you add will receive an invite link by email to sign up for their mentor account. After they've completed their profile, they'll be visible to your community via the Mentors page in the sidebar. They'll need a Calendly account so your community members can book mentorship sessions with them.
          </Typography>
        </Stack>

        <Divider />

        <Stack
          spacing={1.5}
          pt={2}
        >
          {editedMentors.map((m, i) => (
            <Stack
              key={i}
              direction='row'
              spacing={2}
              alignItems='start'
            >
              <Stack spacing={0.2}>
                <Input
                  value={m.email}
                  disabled={m.status === 'active'}
                  placeholder='Email'
                  size='sm'
                  onChange={(e) => handleEmailChange(i, e.target.value)}
                  sx={{width: {xs: 270, sm: 350}}}
                />
                {isDuplicateMentor(i) ? (
                  <Typography
                    level={'body-xs'}
                    color={'danger'}
                  >
                    Email already added
                  </Typography>
                ) : (
                  savedMentors!.map((savedMentor) => savedMentor.email).includes(m.email) &&
                  m.email.length > 0 && (
                    <MentorInviteStatusText
                      status={m.status}
                      email={m.email}
                    />
                  )
                )}
              </Stack>
              {m.email.length > 0 && (
                <IconButton
                  size='sm'
                  variant='soft'
                  color='neutral'
                  onClick={() => handleDelete(m)}
                >
                  <Delete />
                </IconButton>
              )}
              {m.email.length > 0 && m.status === 'pending' && savedMentors!.map((savedMentor) => savedMentor.email).includes(m.email) && (
                <ResendMentorInviteButton
                  mentorEmail={m.email}
                  university={university}
                />
              )}
            </Stack>
          ))}
        </Stack>

        <Button
          disabled={loading || anyDuplicateEmails || allChangesSaved}
          onClick={handleSave}
          sx={{width: 60}}
        >
          {loading ? <LoadingIndicator size={'sm'} /> : 'Save'}
        </Button>
      </Stack>
    </>
  );
}
