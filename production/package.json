{"name": "production", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "test": "npx cypress run"}, "dependencies": {"@creator-campus/common": "*", "@creator-campus/common-components": "*", "@dhaiwat10/react-link-preview": "^1.15.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fontsource/inter": "^5.1.1", "@google-analytics/data": "^4.12.0", "@influxdata/influxdb-client": "^1.35.0", "@mui/base": "^5.0.0-beta.40-0", "@mui/icons-material": "5.16.14", "@mui/joy": "5.0.0-beta.51", "@mui/material": "5.16.14", "@mui/system": "^6.4.3", "@sentry/react": "^9.1.0", "@sentry/vite-plugin": "^3.2.2", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.1", "algoliasearch": "^5.20.0", "axios": "^1.7.9", "date-fns": "^3.6.0", "deepmerge": "^4.3.1", "firebase": "^11.2.0", "firebase-admin": "^12.7.0", "firebase-functions": "^6.2.0", "lodash": "^4.17.21", "marked": "^13.0.3", "match-sorter": "^8.0.0", "node-fetch": "^3.3.2", "normalize-url": "^8.0.1", "npm-check": "^6.0.1", "react": "^18.3.1", "react-calendly": "^4.3.1", "react-color": "^2.19.3", "react-confetti-explosion": "^2.1.2", "react-dom": "^18.3.1", "react-icons": "^5.4.0", "react-infinite-scroll-component": "^6.1.0", "react-instantsearch": "^7.15.1", "react-markdown": "^9.0.3", "react-router-dom": "^6.28.2", "react-window": "^1.8.11", "recharts": "^2.15.0", "rehype-raw": "^7.0.0", "tinycolor2": "^1.6.0", "ts-node": "^10.9.2"}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/plugin-transform-modules-commonjs": "^7.26.3", "@babel/preset-env": "^7.26.0", "@babel/preset-react": "^7.26.3", "@babel/preset-typescript": "^7.26.0", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.6.1", "@types/lodash": "^4.17.14", "@types/node": "^22.10.9", "@types/react": "^18.3.18", "@types/react-color": "^2", "@types/react-dom": "^18.3.5", "@types/react-window": "^1", "@types/tinycolor2": "^1", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react": "^4.3.4", "cypress": "^14.0.1", "dotenv": "^16.4.7", "eslint": "^8.57.1", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.18", "esm": "^3.2.25", "firebase-tools": "^13.29.2", "identity-obj-proxy": "^3.0.0", "prettier": "^3.4.2", "react-error-overlay": "^6.0.11", "react-test-renderer": "^18.3.1", "text-encoding-polyfill": "^0.6.7", "tsx": "^4.19.2", "typescript": "^5.7.3", "vite": "^5.4.14"}}