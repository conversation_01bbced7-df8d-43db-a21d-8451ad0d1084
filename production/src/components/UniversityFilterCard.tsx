import Card from '@mui/joy/Card';
import {Stack} from '@mui/joy';
import {Public, School} from '@mui/icons-material';
import Typography from '@mui/joy/Typography';
import {useTheme} from '@mui/material/styles';
import {influxWriter, newAppMetric} from '@creator-campus/common';
import {useUser} from '@creator-campus/common-components';
import {useLocation} from 'react-router-dom';

interface Props {
  variant: 'myUniversity' | 'global';
  selected: boolean;
  onClick: () => void;
}

export function UniversityFilterCard({variant, selected, onClick}: Props) {
  const ICON_SIZE = '28px';
  const theme = useTheme();
  const {pathname} = useLocation();
  const {user} = useUser();

  return (
    <Card
      sx={{
        p: 1,
        width: '100%',
        cursor: 'pointer',
        borderWidth: selected ? 2.5 : 1.5,
        // @ts-ignore
        borderColor: selected ? theme.palette.primary['500'] : 'neutral',
        '&:hover': {
          backgroundColor: 'rgba(0, 0, 0, 0.04)',
        },
      }}
      onClick={() => {
        onClick();

        if (user) {
          const metric = newAppMetric(user).intField('filterToggleCount', 1).tag('value', variant).tag('page', pathname).tag('attribute', 'universityId').tag('type', 'card');

          influxWriter.writePoint(metric);
        }
      }}
    >
      <Stack
        spacing={0.1}
        alignItems={'center'}
        sx={{pointerEvents: 'none'}}
      >
        {variant === 'myUniversity' ? <School sx={{fontSize: ICON_SIZE}} /> : <Public sx={{fontSize: ICON_SIZE}} />}
        <Typography sx={{typography: 'body-sm', fontWeight: '500'}}>{variant === 'myUniversity' ? 'My University' : 'Global'}</Typography>
      </Stack>
    </Card>
  );
}
