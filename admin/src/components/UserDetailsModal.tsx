import Button from '@mui/joy/Button';
import {User, UserHit} from '@creator-campus/common';
import {LoadingIndicator, ModalHeader, ModalShell, useSnackbar} from '@creator-campus/common-components';
import ProfileCard from 'production/src/components/ProfileCard.tsx';
import Typography from '@mui/joy/Typography';
import {useEffect, useState} from 'react';
import Stack from '@mui/joy/Stack';
import Chip from '@mui/joy/Chip';
import {ContentCopy} from '@mui/icons-material';
import {Box} from '@mui/joy';

interface Props {
  userHit: UserHit;
  onClose: () => void;
}

export default function UserDetailsModal({userHit, onClose}: Props) {
  const [user, setUser] = useState<User | null>(null);
  const {showSnackbar} = useSnackbar();

  useEffect(() => {
    User.fetch(userHit.id).then(setUser);
  }, []);

  return (
    <ModalShell
      onClose={onClose}
      closeOnBackgroundClick={true}
      withCloseButton={true}
    >
      <ModalHeader title={'User details'} />

      {!user ? (
        <LoadingIndicator size={'md'} />
      ) : (
        <Stack spacing={1}>
          {user.profileCompleted && (
            <Box
              pt={2}
              pb={1}
            >
              <ProfileCard
                me={user}
                user={user}
                withActionButton={false}
              />
            </Box>
          )}
          <Stack
            direction={'row'}
            spacing={1}
            alignItems={'center'}
          >
            <Typography level={'title-sm'}>User ID:</Typography>
            <Typography level={'body-sm'}>{user.id}</Typography>
            <Button
              size={'sm'}
              variant={'soft'}
              color={'neutral'}
              startDecorator={<ContentCopy />}
              onClick={async () => {
                showSnackbar('User ID copied to clipboard.');
                await navigator.clipboard.writeText(user.id);
              }}
            >
              Copy
            </Button>
          </Stack>
          <Stack
            direction={'row'}
            spacing={1}
          >
            <Typography level={'title-sm'}>Application status:</Typography>
            <Chip
              variant={'soft'}
              color={user.applicationStatus === 'accepted' ? 'success' : user.applicationStatus === 'rejected' ? 'danger' : 'warning'}
            >
              {user.applicationStatus}
            </Chip>
          </Stack>
          <Stack
            direction={'row'}
            spacing={1}
          >
            <Typography level={'title-sm'}>Profile:</Typography>
            <Chip
              variant={'soft'}
              color={user.profileCompleted ? 'success' : 'warning'}
            >
              {user.profileCompleted ? 'complete' : 'incomplete'}
            </Chip>
          </Stack>
        </Stack>
      )}
    </ModalShell>
  );
}
