import {FormHelperText} from '@mui/joy';
import {firestore, logger, StaffRole, User} from '@creator-campus/common';
import {LoadingIndicator, ModalHeader, ModalShell, useAuth, useProfileString, useSnackbar, useUser} from '@creator-campus/common-components';
import Input from '@mui/joy/Input';
import Button from '@mui/joy/Button';
import {deleteUser, EmailAuthProvider, reauthenticateWithCredential} from 'firebase/auth';
import {useState} from 'react';
import FormControl from '@mui/joy/FormControl';
import ButtonGroup from '@mui/joy/ButtonGroup';
import {PASSWORD_INPUT_SLOT_PROPS} from '../../constants/styles.ts';
import {collection, getCountFromServer, query, where} from 'firebase/firestore';

interface Props {
  onClose: () => void;
}

export default function DeleteProfileModal({onClose}: Props) {
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [password, setPassword] = useState<string>('');

  const {currentUser} = useAuth();
  const {user} = useUser();
  const {showErrorSnackbar, showSnackbar} = useSnackbar();

  async function deleteProfile() {
    // Reauthenticate user
    const creds = EmailAuthProvider.credential(user!.email, password);

    await reauthenticateWithCredential(currentUser!, creds)
      .then(async () => {
        await deleteUser(currentUser!).catch((e) => {
          logger.error(e);
          showErrorSnackbar(e, 'Error deleting account, please contact support.');
        });
      })
      .catch((e) => {
        logger.error(e);

        if (e.code === 'auth/missing-password') {
          setError('Please enter your password.');
        } else if (e.code === 'auth/wrong-password' || e.code === 'auth/invalid-credential') {
          setError('Password is incorrect.');
        } else {
          setError('Sorry, something went wrong.');
        }
      });
  }

  async function canDelete() {
    // Check if this is a staff member who is the only owner of this university
    if (user!.staffRole === StaffRole.OWNER) {
      const numOwnersSnapshot = await getCountFromServer(query(collection(firestore(), User.collectionName), where('universityId', '==', user!.universityId), where('staffRole', '==', StaffRole.OWNER.id)));

      const numOwners = numOwnersSnapshot.data().count;
      if (numOwners === 1) {
        showSnackbar('You are the only owner of this university. Please add another owner in the Dashboard before deleting your account.', 'danger', 5000);
        return false;
      }
    }

    return true;
  }

  return (
    <ModalShell
      onClose={onClose}
      closeOnBackgroundClick={true}
    >
      <ModalHeader
        title={useProfileString().confirmation}
        subtitle={useProfileString().delete_profile_guidance}
      />
      <FormControl
        sx={{mt: 2}}
        error={error !== null}
      >
        <Input
          placeholder='Password'
          type='password'
          required
          onChange={(e) => setPassword(e.target.value)}
          slotProps={PASSWORD_INPUT_SLOT_PROPS}
          sx={{mt: 1}}
        />
        {error && <FormHelperText>{error}</FormHelperText>}
      </FormControl>
      <ButtonGroup
        spacing={1}
        sx={{mt: 2}}
      >
        <Button
          disabled={loading}
          variant='solid'
          color='danger'
          onClick={async () => {
            setLoading(true);
            if (await canDelete()) {
              await deleteProfile();
            }
            setLoading(false);
          }}
        >
          {loading ? <LoadingIndicator size='sm' /> : useProfileString().delete}
        </Button>
        <Button
          variant='outlined'
          color='neutral'
          onClick={() => onClose()}
        >
          {useProfileString().cancel}
        </Button>
      </ButtonGroup>
    </ModalShell>
  );
}
