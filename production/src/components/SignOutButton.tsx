import LogoutRoundedIcon from '@mui/icons-material/LogoutRounded';
import {Button} from '@mui/joy';
import {useAuth, useScreenWidth} from '@creator-campus/common-components';

interface Props {
  text?: string;
  sx?: object;
}

export function SignOutButton({text, sx}: Props) {
  const {logout} = useAuth();

  const screenWidth = useScreenWidth();
  const isSmallScreen = screenWidth < 450;

  return (
    <Button
      data-cy={'sign-out-button'}
      startDecorator={!isSmallScreen && <LogoutRoundedIcon />}
      variant='plain'
      color='neutral'
      onClick={logout}
      sx={sx}
    >
      {isSmallScreen ? <LogoutRoundedIcon /> : text || 'Sign out'}
    </Button>
  );
}
