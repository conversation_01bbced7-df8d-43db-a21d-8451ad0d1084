import {Box, FormControl, FormLabel, Tooltip} from '@mui/joy';
import {Compensation, Location, ProjectTag, University, User, StaffRole} from '@creator-campus/common';
import {AlgoliaDropdown, AlgoliaFilters, AlgoliaSwitch, UpdateAlgoliaFilters, useExploreString} from '@creator-campus/common-components';
import {InfoOutlined} from '@mui/icons-material';
import {UniversityFilterCardsRow} from './UniversityFilterCardsRow.tsx';

interface Props {
  user: User;
  university: University;
  filters: AlgoliaFilters;
  onFilterChange: UpdateAlgoliaFilters;
}

export default function ProjectFilterOptions({user, university, filters, onFilterChange}: Props) {
  return (
    <>
      <FormControl sx={{typography: 'body-sm', display: 'flex', alignItems: 'left'}}>
        {university.id !== 'University of Oxford' && (university.partner || user.staffRole === StaffRole.OWNER) && (
          <UniversityFilterCardsRow
            universityId={university.id}
            filters={filters}
            onFilterChange={onFilterChange}
            sx={{mb: 1.5}}
          />
        )}
        <FormLabel sx={{typography: 'body-sm'}}>
          {useExploreString().active_opps}
          <Tooltip
            enterTouchDelay={0}
            title={useExploreString().active_opps_tooltip}
            variant='solid'
            size='sm'
          >
            <InfoOutlined fontSize='small' />
          </Tooltip>
        </FormLabel>
        <Box sx={{display: 'flex', alignItems: 'center'}}>
          <AlgoliaSwitch
            attribute={'numOpenOpportunities'}
            activeValue={'>0'}
            filters={filters}
            onFilterChange={onFilterChange}
          />
        </Box>
      </FormControl>
      <FormControl>
        <FormLabel sx={{typography: 'body-sm'}}>{useExploreString().category}</FormLabel>
        <AlgoliaDropdown
          attribute='tags'
          options={ProjectTag.values().map((tag) => tag.label)}
          filters={filters}
          onFilterChange={onFilterChange}
        />
      </FormControl>
      <FormControl>
        <FormLabel sx={{typography: 'body-sm'}}>{useExploreString().location}</FormLabel>
        <AlgoliaDropdown
          attribute='locations'
          options={Location.values().map((loc) => loc.label)}
          filters={filters}
          onFilterChange={onFilterChange}
        />
      </FormControl>
      <FormControl>
        <FormLabel sx={{typography: 'body-sm'}}>{useExploreString().compensation}</FormLabel>
        <AlgoliaDropdown
          attribute='compensations'
          options={Compensation.values().map((comp) => comp.label)}
          filters={filters}
          onFilterChange={onFilterChange}
        />
      </FormControl>
    </>
  );
}
