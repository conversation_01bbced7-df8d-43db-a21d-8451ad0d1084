import Typography from '@mui/joy/Typography';
import {useSnackbar} from '@creator-campus/common-components';
import {enrichText} from '../utils.tsx';

interface Props {
  text: string;
  maxBodyLines?: number;
}

export default function DiscussionPostText({text, maxBodyLines}: Props) {
  const {showSnackbar} = useSnackbar();

  return (
    <Typography
      level='body-md'
      sx={{
        mx: 1,
        ...(maxBodyLines
          ? {
              overflow: 'hidden',
              display: '-webkit-box',
              WebkitBoxOrient: 'vertical',
              WebkitLineClamp: maxBodyLines,
              textOverflow: 'ellipsis',
            }
          : {}),
      }}
    >
      {text.split('\n').map((line, index) => (
        <span key={index}>
          {enrichText(line, showSnackbar)}
          <br />
        </span>
      ))}
    </Typography>
  );
}
