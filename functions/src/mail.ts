import {onDocumentCreated} from 'firebase-functions/v2/firestore';
import {getMetricsCollection} from './utils';
import {FieldValue} from 'firebase-admin/firestore';
import {getInfluxWriter, newPoint} from './influxdb';

export const onMailCreated = onDocumentCreated(
  {
    document: 'mail/{mailId}',
    secrets: ['REACT_APP_INFLUX_DB_TOKEN'],
  },
  async (event) => {
    if (!event.data) {
      console.error('Event data is undefined!');
      return;
    }

    const data = event.data.data();

    if (data.template.name === 'connection') {
      const payload = data.template.data;

      await getMetricsCollection(payload.senderUniversityId).update({
        outgoingConnectionRequests: FieldValue.increment(1),
      });

      await getMetricsCollection(payload.receiverUniversityId).update({
        incomingConnectionRequests: FieldValue.increment(1),
      });

      const influxToken = String(process.env.REACT_APP_INFLUX_DB_TOKEN);
      const influxWriter = getInfluxWriter(influxToken);

      let point = newPoint().intField('connectionRequestsSent', 1).tag('university', data.senderUniversityId);
      influxWriter.writePoint(point);

      point = newPoint().intField('connectionRequestsReceived', 1).tag('university', data.receiverUniversityId);
      influxWriter.writePoint(point);
    }
  },
);
