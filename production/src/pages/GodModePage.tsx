import {Button} from '@mui/joy';
import {Compensation, firestore, Location, Opportunity, Project, ProjectTag, User} from '@creator-campus/common';
import {PageHeader, PageShell, useSnackbar, useUser} from '@creator-campus/common-components';
import {collection, deleteDoc, getDocs, updateDoc} from 'firebase/firestore';
import Stack from '@mui/joy/Stack';

export default function GodModePage() {
  const anotherUserId = 'B17geKBwjGo0q7zx90nzSu9k3y4U';

  const {user} = useUser();
  const {showSnackbar} = useSnackbar();

  async function addTestProject(toMe: boolean) {
    if (toMe) {
      await Project.create(user!, `Project ${new Date().getTime()}`, `This project is on my list.`, `This is a really nice description. My favourite number is this.`, [ProjectTag.getRandom()], '');
    } else {
      const anotherUser = await User.fetch(anotherUserId);
      await Project.create(anotherUser!, `Other project ${new Date().getTime()}`, `This project is not on my list.`, `This is a terrible description. My least favourite number is this.`, [ProjectTag.getRandom()], '');
    }

    showSnackbar('Startup added!', 'success');
  }

  async function addRandomProjectsAndOpps(user: User) {
    for (let i = 0; i < 5; i++) {
      const p = await Project.create(user, `Project ${i}`, `This project is number ${i} on my list.`, `This is a really nice description. My favourite number is ${i}.`, [ProjectTag.getRandom()], '');

      for (let j = 0; j < 2; j++) {
        await Opportunity.create(p, [Compensation.getRandom()], 'This opportunity is quite nice.', Location.getRandom(), `Opportunity ${i}-${j}`);
      }
    }
  }

  async function resetProjects() {
    showSnackbar('Resetting startups...');

    const projectDocs = await getDocs(collection(firestore(), Project.collectionName));
    for (const docSnap of projectDocs.docs) {
      await deleteDoc(docSnap.ref);
    }

    const peopleDocs = await getDocs(collection(firestore(), User.collectionName));
    for (const docSnap of peopleDocs.docs) {
      await updateDoc(docSnap.ref, {projects: []});
    }

    const anotherUser = await User.fetch(anotherUserId);

    await addRandomProjectsAndOpps(user!);
    await addRandomProjectsAndOpps(anotherUser!);

    showSnackbar('Startups reset!', 'success');
  }

  return (
    <PageShell footer={false}>
      <PageHeader
        title={'God Mode'}
        subtitle={'The world is your oyster!'}
      />
      <Stack
        spacing={2}
        width={250}
      >
        <Button onClick={() => addTestProject(true)}>Add project (me)</Button>
        <Button onClick={() => addTestProject(false)}>Add project (other user)</Button>
        <Button
          variant='outlined'
          onClick={resetProjects}
        >
          Reset projects
        </Button>
      </Stack>
    </PageShell>
  );
}
