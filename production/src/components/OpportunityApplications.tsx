import {Add, PeopleAlt} from '@mui/icons-material';
import {Box, Stack, Typography} from '@mui/joy';
import AccordionGroup from '@mui/joy/AccordionGroup';
import Accordion from '@mui/joy/Accordion';
import AccordionDetails from '@mui/joy/AccordionDetails';
import AccordionSummary, {accordionSummaryClasses} from '@mui/joy/AccordionSummary';
import {Opportunity, OpportunityApplication, User} from '@creator-campus/common';
import {LoadingIndicator, useSnackbar} from '@creator-campus/common-components';
import {OpportunityReviewApplicationDialog} from './modals/OpportunityReviewApplicationDialog.tsx';
import {useEffect, useState} from 'react';
import {ApplicantCard} from './ApplicantCard.tsx';

interface Props {
  opp: Opportunity;
  user: User;
}

export default function OpportunityApplications({opp, user}: Props) {
  const [applications, setApplications] = useState<OpportunityApplication[] | null>(null);
  const [selectedApplication, setSelectedApplication] = useState<OpportunityApplication | null>(null);

  const {showSnackbar} = useSnackbar();

  useEffect(() => {
    const unsub = opp.listenToApplications(setApplications);
    return () => unsub();
  }, []);

  if (!applications) {
    return <LoadingIndicator size={'md'} />;
  }

  if (applications.length === 0) {
    return <></>;
  }

  return (
    <>
      {selectedApplication && (
        <OpportunityReviewApplicationDialog
          application={selectedApplication}
          onClose={(accepted) => {
            setSelectedApplication(null);
            if (accepted) {
              showSnackbar('Application accepted.', 'success');
            }
          }}
        />
      )}
      <Box sx={{mt: 1}}>
        <AccordionGroup
          sx={{
            [`& .${accordionSummaryClasses.indicator}`]: {
              transition: '0.2s',
            },
            [`& [aria-expanded="true"] .${accordionSummaryClasses.indicator}`]: {
              transform: 'rotate(45deg)',
            },
          }}
        >
          <Accordion onChange={() => user.setOppApplicationsRead()}>
            <AccordionSummary
              indicator={<Add />}
              variant='plain' // use plain to avoid internal background conflict
              sx={
                user.hasUnreadOppApplications
                  ? {
                      position: 'relative',
                      overflow: 'hidden',
                      '&::before': {
                        content: '""',
                        position: 'absolute',
                        inset: 0,
                        zIndex: 0,
                        animation: 'fadeColor 2s infinite alternate',
                        backgroundColor: 'var(--joy-palette-neutral-softBg)',
                      },
                      '@keyframes fadeColor': {
                        from: {
                          backgroundColor: 'var(--joy-palette-neutral-softBg)',
                        },
                        to: {
                          backgroundColor: 'var(--joy-palette-primary-softBg)',
                        },
                      },
                      '& > *': {
                        position: 'relative',
                        zIndex: 1,
                      },
                      borderRadius: 'md',
                    }
                  : {
                      variant: 'plain',
                    }
              }
            >
              <Stack
                direction={'row'}
                spacing={1}
              >
                <PeopleAlt />
                <Typography level='title-sm'>
                  View {applications.length} {applications.length === 1 ? 'application' : 'applications'}
                </Typography>
              </Stack>
            </AccordionSummary>
            <AccordionDetails>
              {applications.map((application, i) => (
                <ApplicantCard
                  key={i}
                  opp={opp}
                  application={application}
                  onAccept={() => setSelectedApplication(application)}
                />
              ))}
            </AccordionDetails>
          </Accordion>
        </AccordionGroup>
      </Box>
    </>
  );
}
