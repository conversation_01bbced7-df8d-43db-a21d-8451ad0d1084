{"name": "@creator-campus/common-components", "version": "3.0.0", "files": ["dist"], "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/types/index.d.ts", "exports": {"types": "./dist/types/index.d.ts", "require": "./dist/index.cjs", "import": "./dist/index.js"}, "scripts": {"build": "rm -rf dist && tsc && rollup -c rollup.config.js --exports auto"}, "keywords": [], "author": "Creator Campus", "type": "module", "license": "ISC", "description": "", "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@influxdata/influxdb-client": "^1.35.0", "@mui/base": "^5.0.0-beta.69", "@mui/icons-material": "5.16.14", "@mui/material": "5.16.14", "algoliasearch": "^5.20.0", "firebase": "^11.2.0", "react": "^18.3.1", "react-confetti-explosion": "^2.1.2", "react-dom": "^18.3.1"}, "devDependencies": {"@rollup/plugin-json": "^6.1.0", "@types/node": "^22.10.7", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "prettier": "^3.4.2", "rollup": "^4.34.0", "typescript": "^5.7.3"}}