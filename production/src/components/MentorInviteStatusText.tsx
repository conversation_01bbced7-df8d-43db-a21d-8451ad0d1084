import Typography from '@mui/joy/Typography';
import {useEffect, useState} from 'react';
import {User} from '@creator-campus/common';

interface Props {
  email: string;
  status: 'pending' | 'active';
}

export function MentorInviteStatusText({email, status}: Props) {
  const [mentor, setMentor] = useState<User | null>(null);

  useEffect(() => {
    if (status === 'active') {
      User.fromEmail(email).then(setMentor);
    }
  }, [email, status]);

  if (status === 'pending') {
    return (
      <Typography
        level={'body-xs'}
        color={'warning'}
      >
        Invite pending
      </Typography>
    );
  }

  if (!mentor) {
    return null;
  }

  if (!mentor.profileCompleted) {
    return (
      <Typography
        level={'body-xs'}
        color={'warning'}
      >
        Profile incomplete
      </Typography>
    );
  }

  return (
    <Typography
      level={'body-xs'}
      color={'success'}
    >
      Active
    </Typography>
  );
}
