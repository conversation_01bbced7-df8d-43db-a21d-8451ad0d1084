name: Deploy main app

on:
  push:
    branches:
      - main
    paths:
      - 'common/**'
      - 'common-components/**'
      - 'production/**'
      - 'extensions/**'
      - '!extensions/*.local'
      - 'package.json'
      - 'package.lock'
      - 'firebase.json'
      - '.firebaserc'
      - 'tsconfig.json'
      - 'vite.config.ts'
      - '.github/workflows/deploy_main_app.yml'
      - '.github/workflows/build_app.yml'

jobs:
  build:
    uses: ./.github/workflows/build_app.yml
    secrets:
      FIREBASE_SERVICE_ACCOUNT_CREATOR_CAMPUS_APP: '${{ secrets.FIREBASE_SERVICE_ACCOUNT_CREATOR_CAMPUS_APP }}'

  deploy:
    needs: build  # Run after build job completes
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Download build artifact
        uses: actions/download-artifact@v4
        with:
          name: production-dist
          path: production/dist

      # Deploy the production app (app.creatorcampus.io)
      - name: Deploy app (production)
        uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: '${{ secrets.GITHUB_TOKEN }}'
          firebaseServiceAccount: '${{ secrets.FIREBASE_SERVICE_ACCOUNT_CREATOR_CAMPUS_APP }}' # Defined in GitHub repo
          channelId: live
          projectId: creator-campus-app
          target: creator-campus-app

      # Deploy the demo app (demo.creatorcampus.io)
      - name: Deploy app (demo)
        uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: '${{ secrets.GITHUB_TOKEN }}'
          firebaseServiceAccount: '${{ secrets.FIREBASE_SERVICE_ACCOUNT_CREATOR_CAMPUS_DEMO }}' # Defined in GitHub repo
          channelId: live
          projectId: creator-campus-demo
          target: creator-campus-demo
