import {onDocumentCreated, onDocumentUpdated} from 'firebase-functions/v2/firestore';
import {FieldValue} from 'firebase-admin/firestore';
import {getMetricsCollection} from './utils';
import * as admin from 'firebase-admin';
import {getInfluxWriter, newPoint} from './influxdb';
import {Opportunity, User, OpportunityApplicationDbModelSafe} from '@creator-campus/common';

export const onOppApplicationCreated = onDocumentCreated(
  {
    document: 'opportunityApplications/{applicationId}',
    secrets: ['REACT_APP_INFLUX_DB_TOKEN'],
  },
  async (event) => {
    if (!event.data) {
      console.error('Event data is undefined!');
      return;
    }

    const data = event.data.data() as OpportunityApplicationDbModelSafe;
    const applicationId = event.params.applicationId;
    console.log(`Detected opportunity application ${applicationId} created.`);

    // Update the hasUnreadOppApplications field in the owner's document
    console.log(`Updating document of project owner ${data.projectOwnerId}...`);
    await admin.firestore().collection(User.collectionName).doc(data.projectOwnerId).update({
      hasUnreadOppApplications: true,
    });

    // Update the opportunity document
    console.log(`Updating document of opportunity ${data.opportunityId}...`);
    await admin
      .firestore()
      .collection(Opportunity.collectionName)
      .doc(data.opportunityId)
      .update({
        applicantIds: FieldValue.arrayUnion(data.applicantId),
      });

    // Update metrics
    console.log('Updating metrics...');
    await getMetricsCollection(data.applicantUniversityId).update({
      outgoingOppApplications: FieldValue.increment(1),
    });

    await getMetricsCollection(data.opportunityUniversityId).update({
      incomingOppApplications: FieldValue.increment(1),
    });

    const influxToken = String(process.env.REACT_APP_INFLUX_DB_TOKEN);
    const influxWriter = getInfluxWriter(influxToken);

    let point = newPoint().intField('oppApplicationsSent', 1).tag('university', data.applicantUniversityId);
    influxWriter.writePoint(point);

    point = newPoint().intField('oppApplicationsReceived', 1).tag('university', data.opportunityUniversityId);
    influxWriter.writePoint(point);
  },
);

export const onOppApplicationUpdated = onDocumentUpdated(
  {
    document: 'opportunityApplications/{applicationId}',
    secrets: ['REACT_APP_INFLUX_DB_TOKEN'],
  },
  async (event) => {
    if (!event.data) {
      console.error('Event data is undefined!');
      return;
    }

    const beforeData = event.data.before.data() as OpportunityApplicationDbModelSafe;
    const afterData = event.data.after.data() as OpportunityApplicationDbModelSafe;
    const applicationId = event.params.applicationId;
    console.log(`Detected opportunity application ${applicationId} updated.`);

    // Check if the application just got accepted
    if (!beforeData.dateAccepted && afterData.dateAccepted) {
      console.log(`Opportunity application ${applicationId} just got accepted.`);

      console.log(`Updating related opportunity ${afterData.opportunityId}...`);
      await admin.firestore().collection(Opportunity.collectionName).doc(afterData.opportunityId).update({
        fillDate: new Date(),
        acceptedApplicantId: afterData.applicantId,
      });

      console.log('Updating metrics...');
      const influxToken = String(process.env.REACT_APP_INFLUX_DB_TOKEN);
      const influxWriter = getInfluxWriter(influxToken);

      const point = newPoint().intField('numSuccessfulOppApplicants', 1).tag('university', afterData.applicantUniversityId);
      influxWriter.writePoint(point);
    }
  },
);
