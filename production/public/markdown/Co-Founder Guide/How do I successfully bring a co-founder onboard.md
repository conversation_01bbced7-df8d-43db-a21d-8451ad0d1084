# How do I successfully bring a co-founder onboard?

<span style="color: green;">*If you're serious about making your startup a success, you should realise that this is going to be a 4-8-year commitment on average. That's about as long as most marriages, the average duration being about eight years. So this is not something to take lightly.*</span>

<span style="color: green;">**- How to Build a Billion Dollar App, <PERSON>**</span>

<p style='text-align: justify;'>Asking someone to be your co-founder after meeting them for the first time would be about as wise as proposing marriage on a first date. Not only do you not have enough data points to know whether they would be a good fit, it’s also a little intense... Instead, it would be better to go on a few dates and trial out the relationship. This is where the trial project comes in.</p>

### The Trial Project

<p style='text-align: justify;'>An incompatible co-founder or bad hire has the potential to slowly kill your venture. In order to mitigate against this, trial projects are a good idea. This provides you with the opportunity to determine whether you’ll work well together, before committing to anything more serious (like giving away equity). There’s no cut a dried length of time for a trial project. I would instinctively say a couple months at least. A trial project would usually look like an achievable but ambitious piece of work with a clear objective and end date.</p>
 
<p style='text-align: justify;'>You can use a <a href="https://seedlegals.com/start/team-agreements/consultancy-agreement/?utm_source=ebook&utm_medium=pdf&utm_campaign=creator_campus" target="_blank">consultancy agreement</a> to formally (and legally) outline the scope of the project, the compensation, and the timeline. Crucially, a consultancy agreement should allocate ownership of the completed work either to yourself as an individual, or (if you’re registered) your company. This will protect you from somebody running off with precious lines of code or other valuable assets in the event you decide to part ways. I have included a simple template consultancy agreement in Appendix C of this guide.</p>

<p style='text-align: justify;'>In order to generate interest and ensure nobody is exploited for their work, you’ll need to think about compensation.  There are two primary ways to achieve this:</p>

### Payment (Short-Term)

<p style='text-align: justify;'>A little bit of money goes a long way. I found it challenging to generate interest in my co-founder opportunity, and people often didn’t take it seriously. As soon as I had some money to put behind it - it changed the whole dynamic (and when I say a little bit, I mean like ~£500). This helps for two main reasons:</p>

- It separates you from the herd. Most student founders are a little bit too optimistic in how exciting they believe their business to be to others, and so they think the promise of potential future equity and the opportunity to simply be involved must be enough to incentivise somebody good to join. There will be exceptions, but usually this isn’t the case. The fact most student startups pursue this approach means if you’re actually able to offer any money you’ll stand out, and demonstrate your credibility.

- Getting involved in someone else's project is risky, so have some empathy for whoever you bring onto the team. They don’t know much about you or your startup. As a founder and leader, you’ve probably painted an exciting vision of world domination in your own head, but it’s a mistake to assume someone new will be able to see what you see after a couple conversations. That’s why a promise of some money will make the decision much less riskier and ease them into things.

### Equity (Long-Term)

After the trial project, you’re faced with two options:
1. They unfortunately aren’t the right partner. Compensate them for the work as agreed, shake their hand, and wish them well. Start the search again.
2. You work amazingly well together and you want to make things official.

<p style='text-align: justify;'>Typically, “making things official” involves offering equity ownership in the company. First-time founders often agonise over the right amount of equity to give away. As a general rule, it’s better to be on the generous side. You want to maximise levels of motivation, and ownership means the harder they work the more valuable their shares will be (and the juicier the payout upon exit via acquisition or IPO). Plus, even though you came up with the idea, a lot of the value is still to be created in the future. That said, you absolutely don’t want to give away large chunks of ownership in your company without having an insurance policy that will protect you against a co-founder breakup.</p>

<p style='text-align: justify;'>This insurance policy takes the form of ”founder vesting”. Founder vesting means the equity is allocated gradually over time, rather than all at once. Founder vesting protects the company and ensures if god-forbid things ever go sour, your co-founder won’t leave with half the company (which could destroy the company, or at the very least cause some very serious headaches).</p>

<p style='text-align: justify;'>In order to better understand equity, let’s look at a (simplified) example:</p>

<p style='text-align: justify;'>- Annie wants to give Jim 40% of the company. Annie implements a 4 year vesting schedule, with a 1-year cliff. This is how Jim would be allocated his equity over time.</p>

<img src="/graph.png" width="100%" height="auto" />

<p style='text-align: justify;'>- A 1-year cliff simply means that for the first year Jim does not receive any equity. However, after the completion of the year, Jim automatically vests all 25% of his shares and would continue to vest gradually over the remaining period (3 years). The purpose of the cliff is to incentivise individuals to stay with the company for at least a year before they start earning equity. A cliff is optional, and perhaps best suited in situations where Jim would also be earning a salary as compensation for his work.</p>

<p style='text-align: justify;'>It’s worth noting you won’t be able to formally <a href="https://seedlegals.com/resources/how-to-give-shares-in-a-startup/" target="_blank">grant shares</a> to a co-founder until you incorporate your company. In the meantime, you can still have a conversation about what the equity split might look like and make an informal agreement.</p>

<p style='text-align: justify;'>Quick word of advice - I would hold off from incorporating your company until it becomes absolutely necessary (i.e. you’re going after grant funding, or you’re looking to distribute your product or service). I know it sounds cool to put “Limited” next to your company name, but dealing with HMRC sucks. That said, one of the fundamental benefits of incorporating a company is a legal concept termed “limited liability”. This protects your personal assets in the event anything awful happens that could leave you exposed to lawsuits (e.g. your app is used improperly by someone leading to real world consequences). So if you’re launching your product to the world, incorporate first.</p>