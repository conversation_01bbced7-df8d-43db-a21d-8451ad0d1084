import {FieldValue, type FirestoreDataConverter, QueryDocumentSnapshot, type SnapshotOptions, Timestamp, type WithFieldValue} from 'firebase/firestore';

export class MaintenanceMode {
  constructor(
    readonly enabled: boolean,
    readonly lastToggled: Date,
  ) {}
}

interface MaintenanceModeDbModelNoDates {
  enabled: boolean;
}

interface MaintenanceModeDbModel extends MaintenanceModeDbModelNoDates {
  lastToggledTs: Timestamp;
}

export interface MaintenanceModeDbModelSafe extends MaintenanceModeDbModelNoDates {
  lastToggledTs: any;
}

export class MaintenanceModeConverter implements FirestoreDataConverter<MaintenanceMode, MaintenanceModeDbModel> {
  toFirestore(maintenanceMode: WithFieldValue<MaintenanceMode>): WithFieldValue<MaintenanceModeDbModel> {
    return {
      enabled: maintenanceMode.enabled,
      lastToggledTs: maintenanceMode.lastToggled instanceof FieldValue ? maintenanceMode.lastToggled : Timestamp.fromDate(maintenanceMode.lastToggled as Date),
    };
  }

  fromFirestore(snapshot: QueryDocumentSnapshot, options: SnapshotOptions): MaintenanceMode {
    const data = snapshot.data(options) as MaintenanceModeDbModel;
    return new MaintenanceMode(data.enabled, data.lastToggledTs.toDate());
  }
}
