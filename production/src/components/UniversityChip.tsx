import {logger, University} from '@creator-campus/common';
import {useEffect, useState} from 'react';
import {AccountBalance} from '@mui/icons-material';
import Chip from '@mui/joy/Chip';

interface Props {
  universityId?: string;
  universityName?: string;
  university?: University;
  variant?: 'plain' | 'soft' | 'outlined' | 'solid';
}

export function UniversityChip({universityId, universityName, university, variant = 'soft'}: Props) {
  const [fetchedUniName, setFetchedUniName] = useState<string | null>(universityName || null);

  if (!universityId && !universityName && !university) {
    logger.error('Exactly one of universityId, universityName, or university must be provided.');
    return null;
  }

  useEffect(() => {
    if (universityId && !universityName && !university) {
      University.fromId(universityId).then((uni) => {
        setFetchedUniName(uni?.name || null);
      });
    }
  }, [universityId, universityName, university]);

  const text = universityName || university?.name || fetchedUniName;

  return (
    <Chip
      startDecorator={<AccountBalance />}
      variant={variant}
    >
      {text || '...'}
    </Chip>
  );
}
