import {doc, type FirestoreDataConverter, getDoc, QueryDocumentSnapshot, setDoc, type SnapshotOptions, type WithFieldValue} from 'firebase/firestore';
import {firestore} from '../../firebase';
import {User} from './user';

export class MembershipApplication {
  static readonly docName = 'application';

  constructor(
    readonly userId: string,
    readonly whyJoin: string,
    readonly graduationYear: string | null,
    readonly projectId: string | null,
    readonly opportunityId: string | null,
  ) {}

  public static async create(userId: string) {
    const application = new MembershipApplication(userId, '', null, null, null);
    await setDoc(MembershipApplication.doc(userId), application);
  }

  public static doc(userId: string) {
    return doc(firestore(), User.collectionName, userId, 'private', MembershipApplication.docName).withConverter(new MembershipApplicationConverter());
  }

  public doc() {
    return MembershipApplication.doc(this.userId);
  }

  public static async fetchFromFirestore(userId: string) {
    const uniDoc = await getDoc(MembershipApplication.doc(userId));
    return uniDoc.data();
  }
}

export interface MembershipApplicationDbModel {
  whyJoin: string;
  graduationYear: string | null;
  projectId: string | null;
  opportunityId: string | null;
}

export class MembershipApplicationConverter implements FirestoreDataConverter<MembershipApplication, MembershipApplicationDbModel> {
  toFirestore(application: WithFieldValue<MembershipApplication>): WithFieldValue<MembershipApplicationDbModel> {
    return {
      whyJoin: application.whyJoin,
      graduationYear: application.graduationYear,
      projectId: application.projectId,
      opportunityId: application.opportunityId,
    };
  }

  fromFirestore(snapshot: QueryDocumentSnapshot, options: SnapshotOptions): MembershipApplication {
    const data = snapshot.data(options) as MembershipApplicationDbModel;
    return new MembershipApplication(snapshot.ref.parent.parent!.id, data.whyJoin, data.graduationYear, data.projectId, data.opportunityId);
  }
}
