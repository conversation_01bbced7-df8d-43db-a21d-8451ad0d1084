import {CircularProgress} from '@mui/joy';
import {CartesianGrid, Line, LineChart, Tooltip, XAxis, YAxis} from 'recharts';
import Box from '@mui/joy/Box';
import FormLabel from '@mui/joy/FormLabel';
import Select from '@mui/joy/Select';
import Option from '@mui/joy/Option';
import Typography from '@mui/joy/Typography';
import {useEffect, useState} from 'react';
import {InfluxData, logger, Timespan, fetchInfluxData} from '@creator-campus/common';
import { useSnackbar } from '../providers/SnackbarProvider';

const size = {
  width: 250,
  height: 150,
};

const margin = {
  top: 0,
  right: 5,
  left: 0,
  bottom: 0,
};

interface Props {
  universityId?: string;
  influxField: string;
  label: string;
}

export function AdminLineChart({universityId, influxField, label}: Props) {
  const [timespan, setTimespan] = useState<Timespan>(Timespan.LAST_WEEK);
  const [data, setData] = useState<InfluxData[] | null>(null);

  const {showErrorSnackbar} = useSnackbar();

  useEffect(() => {
    setData(null);
    fetchInfluxData([influxField], timespan, universityId)
      .then((data) => {
        setData(data.get(influxField)!);
      })
      .catch((e) => {
        logger.error(e);
        showErrorSnackbar(e, 'Error fetching line chart data.');
      });
  }, [timespan]);

  return (
    <>
      <Box sx={{mb: 3}}>
        <FormLabel>{label}</FormLabel>
        <Box sx={{display: 'flex', alignItems: 'center', mt: 1, mb: 2, ml: 1, gap: 1}}>
          <Typography
            level='h4'
            fontWeight='bold'
          >
            {data ? data.reduce((n, {value}) => n + value, 0) : '...'}
          </Typography>
          <Typography
            level='body-md'
            sx={{ml: 0.3, mr: 0.3}}
          >
            in
          </Typography>
          <Select
            sx={{width: '100%', zIndex: 100}}
            value={timespan}
            onChange={(_, value) => {
              if (value) {
                setTimespan(value);
              }
            }}
            size='sm'
          >
            {Timespan.values().map((timespan) => (
              <Option
                key={timespan.label}
                value={timespan}
              >
                {timespan.label}
              </Option>
            ))}
          </Select>
        </Box>
        {data ? (
          <LineChart
            width={size.width}
            height={size.height}
            margin={margin}
            data={data}
          >
            <CartesianGrid strokeDasharray='3 3' />
            <XAxis
              dataKey='ts'
              tickFormatter={timespan.formatter.format}
            />
            <YAxis
              width={25}
              allowDecimals={false}
            />
            <Tooltip
              labelFormatter={timespan.formatter.format}
              formatter={(value, _, __) => [`${value}`, label]}
            />
            <Line
              type='monotone'
              dataKey='value'
              stroke='#fa641d'
            />
          </LineChart>
        ) : (
          <Box
            sx={{
              width: size.width,
              height: size.height,
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <CircularProgress size='md' />
          </Box>
        )}
      </Box>
    </>
  );
}
