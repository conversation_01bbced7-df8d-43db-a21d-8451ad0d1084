import {NavigateFunction, useNavigate, useParams} from 'react-router-dom';
import {LoadingIndicator, PageShell, useAuth} from '@creator-campus/common-components';
import {ReactElement, useEffect, useState} from 'react';
import Stack from '@mui/joy/Stack';
import Box from '@mui/joy/Box';
import {CreatorCampusLogo} from '../components/CreatorCampusLogo.tsx';

interface Props<T> {
  fetchItem: (itemId: string) => Promise<T | null>;
  content: (item: T | null) => ReactElement;
  ifLoggedIn?: (itemId: string, navigate: NavigateFunction) => void;
}

export default function SharePage<T>({fetchItem, content, ifLoggedIn}: Props<T>) {
  const [loading, setLoading] = useState<boolean>(true);
  const [item, setItem] = useState<T | null>(null);

  const params = useParams();
  const {currentUser} = useAuth();
  const navigate = useNavigate();

  const itemId: string = params.id!;

  useEffect(() => {
    if (currentUser && ifLoggedIn !== undefined) {
      ifLoggedIn(itemId, navigate);
      return;
    }

    fetchItem(itemId).then((item) => {
      setItem(item);
      setLoading(false);
    });
  }, [currentUser]);

  return (
    <PageShell>
      <Stack
        spacing={2.5}
        sx={{width: '100%', height: 'calc(100vh - 80px)', alignItems: 'center', justifyContent: 'center'}}
      >
        <CreatorCampusLogo size='40px' />
        {loading ? <LoadingIndicator size='md' /> : <Box sx={{maxWidth: 720}}>{content(item)}</Box>}
      </Stack>
    </PageShell>
  );
}
