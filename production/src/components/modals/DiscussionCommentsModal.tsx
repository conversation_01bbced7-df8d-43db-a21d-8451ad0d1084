import Modal from '@mui/joy/Modal';
import Typography from '@mui/joy/Typography';
import {DialogContent, DialogTitle, ModalDialog} from '@mui/joy';
import {useState} from 'react';
import Box from '@mui/joy/Box';
import DiscussionPostCard from '../DiscussionPostCard.tsx';
import {DiscussionComment, DiscussionCommentConverter, DiscussionPost, firestore, NonexistentUser, University, User} from '@creator-campus/common';
import {FirestoreInfiniteScroll, useUser} from '@creator-campus/common-components';
import {collection} from 'firebase/firestore';
import ModalClose from '@mui/joy/ModalClose';
import DiscussionInput from '../DiscussionInput.tsx';

interface Props {
  post: DiscussionPost;
  cachedUsers: Record<string, User | NonexistentUser>;
  cachedProfilePicUrls: Record<string, string>;
  updateCachedUsers: (comments: string[]) => void;
  onClose: () => void;
  universityId?: string;
}

export default function DiscussionCommentsModal({post, cachedUsers, cachedProfilePicUrls, updateCachedUsers, onClose, universityId}: Props) {
  const commentHeight = 180; // Approx height of a comment in pixels

  // Newest comments first
  const [comments, setComments] = useState<DiscussionComment[]>([]);

  const {user} = useUser();

  if (!user) {
    return <></>;
  }

  return (
    <Modal
      open={true}
      onClose={onClose}
    >
      <ModalDialog sx={{width: '700px'}}>
        <DialogTitle>Comments</DialogTitle>
        <ModalClose />
        <DialogContent>
          <DiscussionPostCard
            initialPost={post}
            cachedUsers={cachedUsers}
            cachedProfilePicUrls={cachedProfilePicUrls}
            updateCachedUsers={updateCachedUsers}
            sx={{border: '2px solid grey'}}
            maxBodyLines={3}
            withAttachments={false}
            universityId={universityId}
          />

          <FirestoreInfiniteScroll
            col={universityId ? collection(firestore(), University.collectionName, universityId, 'discussion', post.id, 'comments') : collection(firestore(), 'global', 'discussion', 'discussion', post.id, 'comments')}
            order={{sortField: 'datePosted', direction: 'desc'}}
            itemHeight={commentHeight}
            converter={new DiscussionCommentConverter()}
            inverse={true}
            height={'40vh'}
            noItemsPlaceholder={
              <>
                <Box sx={{height: '40vh', display: 'flex', alignItems: 'center', justifyContent: 'center'}}>
                  <Typography level='body-sm'>No comments yet</Typography>
                </Box>
              </>
            }
            onFetchMore={(fetchedComments, allComments) => {
              setComments(allComments);
              updateCachedUsers(fetchedComments.map((c) => c.authorId));
            }}
            itemBuilder={(comment, i) => {
              return (
                <DiscussionPostCard
                  key={comment.id}
                  initialPost={comment}
                  cachedUsers={cachedUsers}
                  cachedProfilePicUrls={cachedProfilePicUrls}
                  updateCachedUsers={updateCachedUsers}
                  sx={{mb: i === 0 ? 2 : 0, mt: i === comments.length - 1 ? 2 : 1}}
                  universityId={universityId}
                />
              );
            }}
            sx={{display: 'flex', flexDirection: 'column-reverse', scrollbarWidth: 'none'}}
          />

          <DiscussionInput
            profilePicUrl={cachedProfilePicUrls[user.id]}
            post={post}
            universityId={universityId}
          />
        </DialogContent>
      </ModalDialog>
    </Modal>
  );
}
