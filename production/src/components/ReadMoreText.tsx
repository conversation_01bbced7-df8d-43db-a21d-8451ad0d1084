import {useRef, useState, useEffect} from 'react';
import {Typography, <PERSON>} from '@mui/joy';
import {enrichText} from '../utils.tsx';
import {useSnackbar} from '@creator-campus/common-components';

interface Props {
  text: string;
  maxLines: number;
  onReadMore?: () => void;
  startsExpanded?: boolean;
}

export function ReadMoreText({text, maxLines, onReadMore, startsExpanded = false}: Props) {
  const [overflowing, setOverflowing] = useState<boolean>(false);
  const [expanded, setExpanded] = useState<boolean>(startsExpanded);

  const textRef = useRef<HTMLDivElement | null>(null);
  const {showSnackbar} = useSnackbar();

  useEffect(() => {
    if (textRef.current) {
      const el = textRef.current;
      const lineHeight = parseFloat(getComputedStyle(el).lineHeight);
      const maxHeight = lineHeight * maxLines;
      if (el.scrollHeight > maxHeight) {
        setOverflowing(true);
      }
    }
  }, [text, maxLines]);

  return (
    <div>
      <Typography
        ref={textRef}
        level='body-sm'
        sx={{
          display: '-webkit-box',
          WebkitLineClamp: expanded ? 'unset' : maxLines,
          WebkitBoxOrient: 'vertical',
          overflow: 'hidden',
        }}
      >
        {text.split('\n').map((line, index) => (
          <span key={index}>
            {enrichText(line, showSnackbar)}
            <br />
          </span>
        ))}
      </Typography>

      {/*Read more button*/}
      {overflowing && !expanded && (
        <Link
          level='body-sm'
          onClick={onReadMore || (() => setExpanded(true))}
          sx={{cursor: 'pointer'}}
        >
          Read more
        </Link>
      )}

      {/*Read less button*/}
      {expanded && (
        <Link
          level='body-sm'
          onClick={() => setExpanded(false)}
          sx={{cursor: 'pointer'}}
        >
          Read less
        </Link>
      )}
    </div>
  );
}
