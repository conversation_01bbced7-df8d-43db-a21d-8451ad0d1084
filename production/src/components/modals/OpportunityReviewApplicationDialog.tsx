import {Button, DialogActions, DialogContent, DialogTitle, Divider, ModalDialog, Typography} from '@mui/joy';
import {HowToReg} from '@mui/icons-material';
import {logger, OpportunityApplication} from '@creator-campus/common';
import {useMyProjectsString, useSnackbar} from '@creator-campus/common-components';
import Modal from '@mui/joy/Modal';

interface Props {
  application: OpportunityApplication;
  onClose: (accepted: boolean) => void;
}

export function OpportunityReviewApplicationDialog({application, onClose}: Props) {
  const {showErrorSnackbar} = useSnackbar();

  async function handleAccept() {
    await application
      .accept()
      .then(() => {
        onClose(true);
      })
      .catch((e) => {
        logger.error(e);
        showErrorSnackbar(e, 'Error accepting application.');
      });
  }

  return (
    <Modal
      open={true}
      onClose={() => onClose(false)}
    >
      <ModalDialog
        variant='outlined'
        role='alertdialog'
      >
        <DialogTitle>
          <HowToReg />
          Accept this application?
        </DialogTitle>
        <Divider />
        <DialogContent>
          <Typography>Congratulations on filling this opportunity! Once you accept this application, this opportunity will be considered filled and will no longer appear in the Explore page. You will still be able to view it, and all the applications, here on this page until you delete the opportunity.</Typography>
        </DialogContent>
        <DialogActions>
          <Button
            variant='solid'
            color='success'
            onClick={handleAccept}
          >
            Accept
          </Button>
          <Button
            variant='plain'
            color='neutral'
            onClick={() => onClose(false)}
          >
            {useMyProjectsString().cancel}
          </Button>
        </DialogActions>
      </ModalDialog>
    </Modal>
  );
}
