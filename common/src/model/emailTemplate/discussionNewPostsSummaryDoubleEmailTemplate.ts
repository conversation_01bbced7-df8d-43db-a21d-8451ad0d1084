import {type EmailTemplate} from './emailTemplate.js';
import {DiscussionPostBasic} from '../discussion/discussionPostBasic';

interface Data {
  username: string;
  university: {
    name: string;
    summary: string;
    topPost: DiscussionPostBasic;
  };
  global: {
    summary: string;
    topPost: DiscussionPostBasic;
  };
}

export class DiscussionNewPostsSummaryDoubleEmailTemplate implements EmailTemplate {
  readonly template: string = 'discussion_new_posts_summary_double';

  constructor(readonly data: Data) {}
}
