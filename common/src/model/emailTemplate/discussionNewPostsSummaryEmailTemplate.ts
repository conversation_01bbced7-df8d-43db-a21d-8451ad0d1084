import {type EmailTemplate} from './emailTemplate.js';
import {DiscussionPostBasic} from '../discussion/discussionPostBasic';

interface Data {
  username: string;
  variant: 'university' | 'global';
  universityName: string;
  summary: string;
  topPost: DiscussionPostBasic;
  header?: string;
}

export class DiscussionNewPostsSummaryEmailTemplate implements EmailTemplate {
  readonly template: string = 'discussion_new_posts_summary';

  constructor(readonly data: Data) {
    data.header = data.variant === 'university' ? `Here's a quick summary of ${data.universityName}'s recent discussion on Creator Campus 💫` : `Check out what people are saying in the global discussion channel 🌍`;
  }
}
