import Stack from '@mui/joy/Stack';
import {Typography} from '@mui/joy';
import Tooltip from '@mui/joy/Tooltip';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';

interface Props {
  label: string;
  value: string | number;
  tooltip?: string;
}

export function NumericMetric({label, value, tooltip}: Props) {
  return (
    <Stack>
      <Stack
        direction={'row'}
        spacing={0.5}
        alignItems={'center'}
      >
        <Typography level='body-md'>{label}</Typography>
        {tooltip && (
          <Tooltip
            sx={{maxWidth: 300}}
            title={tooltip}
            variant='solid'
          >
            <InfoOutlinedIcon fontSize='large' />
          </Tooltip>
        )}
      </Stack>
      <Typography
        level='h3'
        sx={{fontWeight: 'bold'}}
      >
        {value}
      </Typography>
    </Stack>
  );
}
