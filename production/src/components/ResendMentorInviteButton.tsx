import {MentorInviteEmailTemplate, sendEmail, University} from '@creator-campus/common';
import {LoadingIndicator, useSnackbar} from '@creator-campus/common-components';
import {buildMentorInviteLink, DAYS_MENTOR_INVITE_VALID} from './modals/InviteMentorsModal.tsx';
import IconButton from '@mui/joy/IconButton';
import {Email} from '@mui/icons-material';
import Tooltip from '@mui/joy/Tooltip';
import {useState} from 'react';

interface Props {
  mentorEmail: string;
  university: University;
}

export function ResendMentorInviteButton({mentorEmail, university}: Props) {
  const [loading, setLoading] = useState<boolean>(false);

  const {showSnackbar} = useSnackbar();

  async function handleResendInviteButton() {
    setLoading(true);

    const pendingMentors = await university.getPendingMentors();
    const pendingMentor = pendingMentors.filter((m) => m.email === mentorEmail)[0];

    await sendEmail(
      mentorEmail,
      new MentorInviteEmailTemplate({
        daysValid: DAYS_MENTOR_INVITE_VALID,
        link: buildMentorInviteLink(mentorEmail, pendingMentor.token),
        universityName: university.name,
      }),
    );

    showSnackbar('Invite sent!', 'success');
    setLoading(false);
  }

  return (
    <Tooltip
      sx={{maxWidth: 300}}
      title={`Re-send invite email to ${mentorEmail}`}
      variant='solid'
    >
      <IconButton
        disabled={loading}
        size='sm'
        variant='soft'
        color='neutral'
        onClick={handleResendInviteButton}
      >
        {loading ? <LoadingIndicator size={'sm'} /> : <Email />}
      </IconButton>
    </Tooltip>
  );
}
