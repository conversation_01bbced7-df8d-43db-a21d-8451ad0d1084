import Stack from '@mui/joy/Stack';
import {useAuth, useScreenWidth, useUser} from '@creator-campus/common-components';
import Box from '@mui/joy/Box';
import {SignOutButton} from '../components/SignOutButton.tsx';
import {OnboardingStagesOverview} from '../components/OnboardingStagesOverview.tsx';
import {getOnboardingStages, OnboardingStageConfig, OnboardingStageKey} from '../model/onboardingConfig.tsx';
import {useState} from 'react';
import {OnboardingStage} from '../components/OnboardingStage.tsx';
import {LoadingPage} from './LoadingPage.tsx';

export default function OnboardingPage() {
  const INTERMEDIATE_STAGE_BUTTON_TEXT = 'Next';
  const FINAL_STAGE_BUTTON_TEXT = 'Finish';

  const {currentUser} = useAuth();
  const {user, university} = useUser();

  const screenWidth = useScreenWidth();
  const isSmallScreen = screenWidth < 400;

  if (!currentUser || !user || !university) {
    return <LoadingPage />;
  }

  const [completedStages, setCompletedStages] = useState<Set<OnboardingStageKey>>(new Set());
  const [stages, _setStages] = useState<OnboardingStageConfig[]>(getOnboardingStages(user, university));

  const currentStage = stages.find((s) => !completedStages.has(s.key));
  if (!currentStage) {
    return <></>;
  }

  const currentIndex = stages.findIndex((s) => s.key === currentStage.key);

  return (
    <>
      <SignOutButton sx={{position: 'absolute', top: 16, right: 16}} />
      <Stack
        spacing={2.5}
        sx={{width: '100%', height: '100vh', alignItems: 'center'}}
      >
        <Box sx={{height: '100%', maxWidth: 650, px: isSmallScreen ? 2 : 3, pt: 6}}>
          <OnboardingStagesOverview
            numStages={stages.length}
            currentStageIndex={currentIndex}
          />
          <OnboardingStage contentOptions={currentStage.contentOptions}>
            {currentStage.render(stages.length === currentIndex + 1 ? FINAL_STAGE_BUTTON_TEXT : INTERMEDIATE_STAGE_BUTTON_TEXT, () => {
              setCompletedStages((prev) => new Set([...prev, currentStage.key]));
            })}
          </OnboardingStage>
        </Box>
      </Stack>
    </>
  );
}
