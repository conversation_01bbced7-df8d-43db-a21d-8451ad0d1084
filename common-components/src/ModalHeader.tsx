import Typography from '@mui/joy/Typography';
import Box from '@mui/joy/Box';

interface Props {
  title: string;
  subtitle?: string;
  sx?: object;
}

export function ModalHeader({title, subtitle, sx}: Props) {
  return (
    <Box sx={sx}>
      <Typography
        component='h2'
        level='h4'
        textColor='inherit'
        fontWeight='lg'
        sx={{mb: 0.5}}
      >
        {title}
      </Typography>
      {subtitle ? (
        <Typography
          textColor='text.tertiary'
          level='body-sm'
        >
          {subtitle}
        </Typography>
      ) : (
        <></>
      )}
    </Box>
  );
}
