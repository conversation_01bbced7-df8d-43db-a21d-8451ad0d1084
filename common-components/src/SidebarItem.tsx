import {ListItem, ListItemButton, ListItemContent, Typography} from '@mui/joy';
import {ReactNode} from 'react';
import {Link as RouterLink} from 'react-router-dom';
import {closeSidebar} from './Sidebar';
import Tooltip from '@mui/joy/Tooltip';
import {useScreenWidth} from './providers/DimensionsProvider';

interface Props {
  text: string;
  leadingIcon?: ReactNode;
  trailingIcon?: ReactNode;
  selected: boolean;
  href: string;
  openInNewTab?: boolean;
  enabled?: boolean;
  tooltip?: string;
}

export function SidebarItem({text, leadingIcon, trailingIcon, selected, href, openInNewTab, enabled = true, tooltip}: Props) {
  const screenWidth = useScreenWidth();

  return (
    <Tooltip
      title={tooltip}
      variant={'solid'}
      placement={screenWidth < 600 ? 'bottom' : 'right'}
    >
      <ListItem>
        <ListItemButton
          disabled={!enabled}
          selected={selected}
          role='menuitem'
          component={RouterLink}
          to={href}
          target={openInNewTab ? '_blank' : undefined}
          onClick={closeSidebar}
        >
          {leadingIcon}
          <ListItemContent>
            <Typography level='title-sm'>{text}</Typography>
          </ListItemContent>
          {trailingIcon}
        </ListItemButton>
      </ListItem>
    </Tooltip>
  );
}
