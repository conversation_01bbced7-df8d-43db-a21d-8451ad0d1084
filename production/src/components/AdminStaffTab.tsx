import {Grid, Input, Stack} from '@mui/joy';
import {generateRandomId, StaffRole, University, User} from '@creator-campus/common';
import {LoadingIndicator, useSnackbar, useUser} from '@creator-campus/common-components';
import {Fragment, useEffect, useState} from 'react';
import Option from '@mui/joy/Option';
import Select from '@mui/joy/Select';
import {setDoc} from 'firebase/firestore';
import Button from '@mui/joy/Button';
import IconButton from '@mui/joy/IconButton';
import {Delete} from '@mui/icons-material';
import {useNavigate} from 'react-router-dom';
import Typography from '@mui/joy/Typography';
import Divider from '@mui/joy/Divider';
import FormControl from '@mui/joy/FormControl';

interface Props {
  university: University;
}

export function AdminStaffTab({university}: Props) {
  const [savedStaff, setSavedStaff] = useState<User[] | null>(null);
  const [editedStaff, setEditedStaff] = useState<User[] | null>(null);
  const [couldNotFindEmails, setCouldNotFindEmails] = useState<string[]>([]);
  const [hasBeenEdited, setHasBeenEdited] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);

  const {user} = useUser();
  const {showSnackbar} = useSnackbar();
  const navigate = useNavigate();

  useEffect(() => {
    university.getStaff(true).then((users) => {
      setSavedStaff(users);
      setEditedStaff([...users, defaultUser()]);
    });
  }, [university]);

  /**
   * This is usually used to put a blank input box at the end of the list.
   */
  function defaultUser() {
    return User.defaults(generateRandomId(), '', university.id).copyWith({name: 'User', staffRole: StaffRole.baseRole()});
  }

  async function cleanAndValidateData() {
    if (!editedStaff) {
      return null;
    }

    // Remove duplicate emails
    const noDuplicateStaff = Array.from(new Map(editedStaff.map((s) => [s!.email, s])).values());

    const uncleanedStaff: (User | null)[] = await Promise.all(
      noDuplicateStaff
        // Remove staff with empty emails
        .filter((s) => s.email.trim() !== '')
        .map(async (s) => {
          const u = await User.fromEmail(s.email);
          if (u?.universityId !== university.id) {
            setCouldNotFindEmails((prev) => [...prev, s.email]);
            showSnackbar('Some users could not be found. Make sure they sign up to Creator Campus first!', 'danger', 5000);
            return null;
          }

          return u.copyWith({staffRole: s.staffRole});
        }),
    );

    // If any users weren't found, stop here
    if (uncleanedStaff.some((s) => s === null)) {
      return null;
    }

    // We can be sure there are no 'null' staff now
    const staff = uncleanedStaff as User[];

    // Make sure there is at least one university owner assigned
    const anyOwners = staff.some((s) => s.staffRole?.id === StaffRole.OWNER.id);
    if (!anyOwners) {
      showSnackbar('You must assign at least one university owner.', 'danger');
      return null;
    }

    return staff;
  }

  async function handleSubmit() {
    setCouldNotFindEmails([]);
    const staff = await cleanAndValidateData();

    if (!staff || !savedStaff) {
      return;
    }

    const me = staff.find((s) => user!.email === s.email);

    if (!me) {
      if (!confirm('You have removed yourself as a staff member so will lose access to this page if you confirm. Are you sure you want to continue?')) {
        return;
      }
    } else if (me.staffRole !== StaffRole.OWNER) {
      if (!confirm('You have removed yourself as an owner so will lose access to this page if you confirm. Are you sure you want to continue?')) {
        return;
      }
    }

    // Find staff to remove
    const staffIds = staff.map((s) => s.id);
    const toRemove = savedStaff.filter((s) => !staffIds.includes(s.id));

    // Update user docs with new staff roles
    for (const s of [...staff, ...toRemove.map((s) => s.copyWith({staffRole: null}))]) {
      if (s.id === user!.id) {
        // Don't update ourselves yet, we do this in the final step
        continue;
      }

      await setDoc(s._doc, s);
    }

    setEditedStaff([...staff, defaultUser()]);
    setSavedStaff(staff);
    setHasBeenEdited(false);

    if (!me) {
      // Remove ourselves as staff as the very last step
      await setDoc(user!._doc, user!.copyWith({staffRole: null}));
      navigate('/startups');
      return;
    } else if (me.staffRole !== StaffRole.OWNER) {
      // Update our own staff role as the very last step
      await setDoc(me._doc, me);
      navigate('/admin');
      return;
    }

    showSnackbar('Changes saved.', 'success');
  }

  function handleRoleChange(staffId: string, staffRole: StaffRole) {
    setHasBeenEdited(true);
    setEditedStaff((prev) => prev!.map((member) => (member.id === staffId ? member.copyWith({staffRole: staffRole, applicationStatus: 'accepted'}) : member)));
  }

  function handleEmailChange(staffId: string, email: string) {
    setHasBeenEdited(true);
    setEditedStaff((prev) => {
      const updatedStaff = prev!.map((member) => (member.id === staffId ? member.copyWith({email: email, name: defaultUser().name}) : member));

      // Remove trailing empty inputs
      while (!updatedStaff[updatedStaff.length - 1].email && updatedStaff.length > 1) {
        updatedStaff.pop();
      }

      // If the last input is being edited and isn't empty, append a new empty input
      const lastInput = updatedStaff[updatedStaff.length - 1];
      if (lastInput.email) {
        updatedStaff.push(defaultUser());
      }

      return updatedStaff;
    });
  }

  function handleDelete(staffId: string) {
    setHasBeenEdited(true);

    setEditedStaff((prev) => prev!.filter((prevStaff) => prevStaff.id !== staffId));
  }

  if (editedStaff === null || !user) {
    return <LoadingIndicator size={'md'} />;
  }

  return (
    <Stack
      spacing={1.5}
      sx={{maxWidth: 750}}
    >
      <Grid
        container
        sx={{
          display: 'grid',
          gap: 0.5,
          gridTemplateColumns: {
            xs: '1fr', // One column on xs screens
            sm: '100px 1fr', // Two columns on small screens and up
          },
        }}
      >
        {StaffRole.values().map((role, i) => (
          <Fragment key={i}>
            {/* Left-hand column for labels */}
            <Typography level={'title-sm'}>{role.label}</Typography>
            {/* Right-hand column for descriptions */}
            <Typography
              level={'body-sm'}
              sx={{mb: {xs: 1.5, sm: 0}}}
            >
              {role.description}
            </Typography>
          </Fragment>
        ))}
      </Grid>

      <Divider />

      <Stack
        spacing={1.5}
        pt={2}
      >
        {editedStaff.map((s, i) => (
          <Stack
            key={s.id}
            direction='row'
            spacing={2}
            alignItems='start'
          >
            <Select
              value={s.staffRole}
              size='sm'
              onChange={(_, newRole) => {
                if (newRole) {
                  handleRoleChange(s.id, newRole);
                }
              }}
              sx={{width: 100}}
            >
              {StaffRole.values().map((role) => (
                <Option
                  key={role.id}
                  value={role}
                >
                  {role.label}
                </Option>
              ))}
            </Select>
            <FormControl error={couldNotFindEmails.includes(s.email)}>
              <Stack spacing={0.2}>
                <Input
                  value={s.email}
                  placeholder='Email'
                  size='sm'
                  onChange={(e) => handleEmailChange(s.id, e.target.value)}
                  sx={{width: {xs: 270, sm: 350}}}
                  disabled={savedStaff!.some((savedStaff) => savedStaff.id === s.id)}
                />
                <Typography
                  level={'body-xs'}
                  color={couldNotFindEmails.includes(s.email) ? 'danger' : 'neutral'}
                >
                  {couldNotFindEmails.includes(s.email) ? 'User not found' : s.name}
                </Typography>
              </Stack>
            </FormControl>
            {i < editedStaff.length - 1 && (
              <IconButton
                size='sm'
                variant='soft'
                color='neutral'
                onClick={() => handleDelete(s.id)}
              >
                <Delete />
              </IconButton>
            )}
          </Stack>
        ))}
      </Stack>

      <Button
        disabled={loading || !hasBeenEdited || editedStaff.length === 0}
        onClick={async () => {
          setLoading(true);
          await handleSubmit();
          setLoading(false);
        }}
        sx={{width: 60}}
      >
        {loading ? <LoadingIndicator size='sm' /> : 'Save'}
      </Button>
    </Stack>
  );
}
