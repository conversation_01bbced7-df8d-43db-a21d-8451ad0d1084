import {<PERSON><PERSON>, Box, Chip, Link} from '@mui/joy';
import {useEffect, useState} from 'react';
import {useNavigate} from 'react-router-dom';
import {logger, Project, User, UserHit} from '@creator-campus/common';

interface Props {
  userHit: UserHit;
  withLink?: boolean;
}

export default function ProjectChip({userHit, withLink = true}: Props) {
  const [project, setProject] = useState<Project | null>(null);
  const [logoUrl, setLogoUrl] = useState<string>('');

  const navigate = useNavigate();

  useEffect(() => {
    User.fetch(userHit.id)
      .then(async (u) => {
        if (!u || u.projectIds.length === 0) {
          return;
        }

        Project.fetch(u.projectIds[0])
          .then((p) => {
            setProject(p);

            if (!p) {
              return;
            }

            p.getLogoUrl()
              .then(setLogoUrl)
              .catch((e) => logger.error(e));
          })
          .catch((e) => logger.error(e));
      })
      .catch((e) => logger.error(e));
  }, []);

  if (!project) {
    return null;
  }

  const chipComponent = (
    <Box sx={{display: 'flex', justifyContent: 'flex-start', alignItems: 'center'}}>
      <Chip
        color='neutral'
        size='md'
        variant='soft'
        startDecorator={
          <Avatar
            size='md'
            src={logoUrl}
          />
        }
      >
        {project.name}
      </Chip>
    </Box>
  );

  if (withLink) {
    return (
      <Link
        onClick={() => {
          navigate(`/${project.getLink(false)}`, {replace: true});
        }}
      >
        {chipComponent}
      </Link>
    );
  }

  return chipComponent;
}
