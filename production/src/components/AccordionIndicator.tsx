import AccordionGroup from '@mui/joy/AccordionGroup';
import Accordion from '@mui/joy/Accordion';
import AccordionDetails from '@mui/joy/AccordionDetails';
import AccordionSummary, {accordionSummaryClasses} from '@mui/joy/AccordionSummary';
import AddIcon from '@mui/icons-material/Add';
import {Typography} from '@mui/joy';

interface Props {
  summary: string;
  text: string;
}

export default function AccordionIndicator(props: Props) {
  return (
    <AccordionGroup
      sx={{
        // maxWidth: 400,
        [`& .${accordionSummaryClasses.indicator}`]: {
          transition: '0.2s',
        },
        [`& [aria-expanded="true"] .${accordionSummaryClasses.indicator}`]: {
          transform: 'rotate(45deg)',
        },
        marginTop: '-5px',
      }}
    >
      <Accordion sx={{mt: 1}}>
        <AccordionSummary indicator={<AddIcon />}>
          <Typography level='body-sm'>{props.summary}</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography level='body-sm'>{props.text}</Typography>
        </AccordionDetails>
      </Accordion>
    </AccordionGroup>
  );
}
