import Button from '@mui/joy/Button';
import {DialogActions, DialogContent, DialogTitle, ModalDialog} from '@mui/joy';
import {firestore, getMembershipApprovedTemplate, MembershipApplication, MembershipApprovedCustomEmailTemplate, MembershipApprovedEmailTemplate, MembershipRejectedEmailTemplate, Project, sendEmail, User} from '@creator-campus/common';
import {useSnackbar, useUser, LoadingIndicator} from '@creator-campus/common-components';
import Modal from '@mui/joy/Modal';
import Stack from '@mui/joy/Stack';
import {useEffect, useState} from 'react';
import Typography from '@mui/joy/Typography';
import {doc, getDoc, updateDoc} from 'firebase/firestore';
import ProjectCard from 'production/src/components/ProjectCard.tsx';
import ProfileCard from 'production/src/components/ProfileCard.tsx';

interface Props {
  user: User;
  onClose: () => void;
}

export default function UserApplicationModal({user, onClose}: Props) {
  const [applicationQuestions, setApplicationQuestions] = useState<MembershipApplication | null>(null);
  const [applicationProject, setApplicationProject] = useState<Project | null>(null);
  const [loading, setLoading] = useState<'accept' | 'reject' | null>(null);

  const {showSnackbar, showErrorSnackbar} = useSnackbar();
  const me = useUser();

  useEffect(() => {
    getDoc(MembershipApplication.doc(user.id)).then(async (snapshot) => {
      const questions = snapshot.data()!;
      setApplicationQuestions(questions);

      const project = questions.projectId ? await Project.fetch(questions.projectId) : null;
      setApplicationProject(project);
    });
  }, [user]);

  async function handleSubmit(decision: 'accept' | 'reject') {
    setLoading(decision);
    const handler = decision === 'accept' ? handleAccept : handleReject;

    console.log(`Handling decision (${decision})...`);
    await handler()
      .then(() => {
        showSnackbar(`Application ${decision}ed.`, 'success');
        onClose();
      })
      .catch((e) => showErrorSnackbar(e, 'Error handling application.'))
      .finally(() => setLoading(null));
  }

  async function handleAccept() {
    console.log('Updating user doc...');
    await updateDoc(user._doc, {
      applicationStatus: 'accepted',
    });

    console.log('Checking for custom approval email template...');
    const customApprovalEmailTemplate = await getDoc(doc(firestore(), 'email_templates', getMembershipApprovedTemplate(user.universityId)));

    if (customApprovalEmailTemplate.exists()) {
      // Send the university's custom welcome email
      console.log('Sending custom welcome email...');
      await sendEmail(user.email, new MembershipApprovedCustomEmailTemplate(user.universityId, {name: user.name}));
    } else {
      // Send default welcome email
      console.log('Sending default welcome email...');
      await sendEmail(user.email, new MembershipApprovedEmailTemplate({name: user.name}));
    }
  }

  async function handleReject() {
    console.log('Updating user doc...');
    await updateDoc(user._doc, {
      applicationStatus: 'rejected',
    });

    console.log('Sending rejection email...');
    await sendEmail(user.email, new MembershipRejectedEmailTemplate({name: user.name}));
  }

  return (
    <>
      <Modal
        open={true}
        onClose={onClose}
      >
        <ModalDialog>
          <DialogTitle>Review application</DialogTitle>
          <DialogContent>
            <Stack
              direction={'row'}
              spacing={0.5}
              alignItems={'center'}
            >
              <Typography level={'title-sm'}>Applicant type:</Typography>
              <Typography level={'body-sm'}>{!applicationQuestions ? '...' : applicationQuestions.graduationYear ? 'Graduate' : 'Student'}</Typography>
            </Stack>

            <ProfileCard
              me={me.user!}
              user={user}
              withActionButton={false}
            />

            <Typography
              level={'title-lg'}
              sx={{mt: 3}}
            >
              Application questions
            </Typography>

            <Typography
              level={'title-sm'}
              sx={{mt: 1}}
            >
              Where did you hear about Creator Campus?
            </Typography>

            <Typography level={'body-sm'}>{applicationQuestions?.whyJoin}</Typography>

            {applicationQuestions?.graduationYear && (
              <>
                <Typography
                  level={'title-sm'}
                  sx={{mt: 1}}
                >
                  Graduation year
                </Typography>

                <Typography level={'body-sm'}>{applicationQuestions.graduationYear}</Typography>
              </>
            )}

            <Typography
              level={'title-lg'}
              sx={{mt: 5, mb: 2}}
            >
              Startup submission
            </Typography>

            {applicationProject ? (
              <ProjectCard
                projectId={applicationProject.id}
                hit={applicationProject}
                editMode={false}
              />
            ) : (
              <Typography
                level={'title-sm'}
                sx={{mt: 5, mb: 2}}
              >
                (No startup submitted)
              </Typography>
            )}
          </DialogContent>
          <DialogActions>
            <Button
              variant='soft'
              color='success'
              onClick={() => handleSubmit('accept')}
            >
              {loading === 'accept' ? <LoadingIndicator size={'sm'} /> : 'Accept'}
            </Button>
            <Button
              variant='soft'
              color='danger'
              onClick={() => handleSubmit('reject')}
            >
              {loading === 'reject' ? <LoadingIndicator size={'sm'} /> : 'Reject'}
            </Button>
            <Button
              variant='plain'
              color='neutral'
              onClick={onClose}
            >
              Close
            </Button>
          </DialogActions>
        </ModalDialog>
      </Modal>
    </>
  );
}
