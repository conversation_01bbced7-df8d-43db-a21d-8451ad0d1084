import {useLocation} from 'react-router-dom';
import {PageLayout, Sidebar} from '@creator-campus/common-components';
import SidebarContent from './SidebarContent';
import {Box} from '@mui/joy';

export function LayoutWrapper() {
  const location = useLocation();
  const isOnboarding = location.pathname === '/onboarding';

  return (
    <Box sx={{display: 'flex', minHeight: '100dvh'}}>
      {!isOnboarding && (
        <Sidebar>
          <SidebarContent />
        </Sidebar>
      )}
      <PageLayout />
    </Box>
  );
}
