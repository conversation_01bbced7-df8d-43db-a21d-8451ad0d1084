import {collection, doc, DocumentReference, FieldValue, type FirestoreDataConverter, getDoc, getDocs, query, QueryDocumentSnapshot, setDoc, type SnapshotOptions, Timestamp, updateDoc, where, type WithFieldValue} from 'firebase/firestore';
import {UserHit} from './userHit';
import {firestore, storage} from '../../firebase';
import {ref, type StorageReference, uploadBytes, deleteObject, getDownloadURL} from 'firebase/storage';
import * as auth from 'firebase/auth';
import {verifyBeforeUpdateEmail} from 'firebase/auth';
import {sendEmail} from '../../utils/email';
import {ConnectionEmailTemplate} from '../emailTemplate/connectionEmailTemplate';
import {ConnectionReceiptEmailTemplate} from '../emailTemplate/connectionReceiptEmailTemplate';
import {logger} from '../../logger';
import {StaffRole, StaffRoleId} from '../university/staffRole';
import {Persona, PersonaId} from './persona';
import {Role, RoleId} from './role';
import {UserNotifications} from './userNotifications';
import {UserApplicationStatus} from './userApplicationStatus';
import {MembershipApplication} from './membershipApplication';
import {CREATOR_CAMPUS_ADMINS} from '../../constants';
import {Service, ServiceId} from './service';

/**
 * Represents a Firestore record for a user.
 */
export class User extends UserHit {
  static readonly collectionName: string = 'people';
  static readonly MIN_BIO_LEN = 100;
  static readonly MAX_BIO_LEN = 500;
  static readonly MAX_BIO_LINES = 2;
  static readonly MIN_WHY_JOIN_LEN = 1;
  static readonly MAX_WHY_JOIN_LEN = 250;
  static readonly MAX_SERVICES = 3;

  readonly _doc: DocumentReference<User, UserDbModel>;
  readonly _cvStorage: StorageReference;

  constructor(
    override readonly id: string,
    readonly applicationStatus: UserApplicationStatus,
    readonly avatarUpdatedAt: Date,
    override readonly bio: string,
    readonly cvUploaded: boolean,
    readonly dateJoined: Date,
    readonly email: string,
    readonly emailVerified: boolean,
    override readonly featuredUntil: Date | null,
    override readonly founder: boolean,
    readonly github: string | null,
    readonly hasUnreadOppApplications: boolean,
    readonly hideProfile: boolean,
    override readonly karma: number,
    readonly lastOnline: Date,
    readonly lastViewedDiscussion: Date,
    readonly linkedin: string | null,
    override readonly name: string,
    readonly notifications: UserNotifications,
    readonly numCompleteProfileRemindersSent: number,
    override readonly openToWork: boolean,
    override readonly persona: Persona | null,
    override readonly profileCompleted: boolean,
    readonly projectIds: string[],
    override readonly role: Role | null,
    readonly score: number,
    override readonly services: Service[],
    override readonly staffRole: StaffRole | null,
    // The universityEmail field is null for graduates who no longer have access to their student email
    readonly universityEmail: string | null,
    override readonly universityId: string,
    override readonly website: string | null,
  ) {
    super(id, bio, featuredUntil, founder, github, karma, linkedin, name, openToWork, persona, profileCompleted, role, services, staffRole, universityId, website);

    this._doc = User.doc(id);
    this._cvStorage = ref(storage(), `/people/${id}/cv`);
  }

  public static doc(id: string) {
    return doc(firestore(), User.collectionName, id).withConverter<User, UserDbModel>(new UserConverter());
  }

  public static defaults(uid: string, email: string, universityId: string) {
    return new User(
      uid,
      'inProgress',
      new Date(),
      '',
      false,
      new Date(),
      email,
      false,
      null,
      false,
      null,
      false,
      false,
      0,
      new Date(),
      new Date(),
      null,
      '',
      {
        discussionNewPosts: 'daily',
        discussionPostReplies: 'realTime',
      },
      0,
      false,
      null,
      false,
      [],
      null,
      0,
      [],
      null,
      email,
      universityId,
      null,
    );
  }

  public copyWith(user: Partial<User>) {
    return new User(
      user.id === undefined ? this.id : user.id,
      user.applicationStatus === undefined ? this.applicationStatus : user.applicationStatus,
      user.avatarUpdatedAt === undefined ? this.avatarUpdatedAt : user.avatarUpdatedAt,
      user.bio === undefined ? this.bio : user.bio,
      user.cvUploaded === undefined ? this.cvUploaded : user.cvUploaded,
      user.dateJoined === undefined ? this.dateJoined : user.dateJoined,
      user.email === undefined ? this.email : user.email,
      user.emailVerified === undefined ? this.emailVerified : user.emailVerified,
      user.featuredUntil === undefined ? this.featuredUntil : user.featuredUntil,
      user.founder === undefined ? this.founder : user.founder,
      user.github === undefined ? this.github : user.github,
      user.hasUnreadOppApplications === undefined ? this.hasUnreadOppApplications : user.hasUnreadOppApplications,
      user.hideProfile === undefined ? this.hideProfile : user.hideProfile,
      user.karma === undefined ? this.karma : user.karma,
      user.lastOnline === undefined ? this.lastOnline : user.lastOnline,
      user.lastViewedDiscussion === undefined ? this.lastViewedDiscussion : user.lastViewedDiscussion,
      user.linkedin === undefined ? this.linkedin : user.linkedin,
      user.name === undefined ? this.name : user.name,
      user.notifications === undefined ? this.notifications : user.notifications,
      user.numCompleteProfileRemindersSent === undefined ? this.numCompleteProfileRemindersSent : user.numCompleteProfileRemindersSent,
      user.openToWork === undefined ? this.openToWork : user.openToWork,
      user.persona === undefined ? this.persona : user.persona,
      user.profileCompleted === undefined ? this.profileCompleted : user.profileCompleted,
      user.projectIds === undefined ? this.projectIds : user.projectIds,
      user.role === undefined ? this.role : user.role,
      user.score === undefined ? this.score : user.score,
      user.services === undefined ? this.services : user.services,
      user.staffRole === undefined ? this.staffRole : user.staffRole,
      user.universityEmail === undefined ? this.universityEmail : user.universityEmail,
      user.universityId === undefined ? this.universityId : user.universityId,
      user.website === undefined ? this.website : user.website,
    );
  }

  public static async create(uid: string, email: string, universityId: string, options?: Partial<User>) {
    const user = User.defaults(uid, email, universityId).copyWith(options || {});
    await setDoc(User.doc(uid), user);
    await MembershipApplication.create(user.id);
    return user;
  }

  public static async fromEmail(email: string) {
    const q = query(collection(firestore(), User.collectionName), where('email', '==', email)).withConverter(new UserConverter());

    const snapshot = await getDocs(q);
    if (snapshot.empty) {
      return null;
    }

    return snapshot.docs[0]!.data();
  }

  public static async fetch(id: string) {
    const snapshot = await getDoc(User.doc(id));
    return snapshot.data() || null;
  }

  public getFirstNameLastInitial() {
    const words = this.name.split(' ');

    if (words.length === 0) {
      return this.name;
    }

    const firstName = words[0];
    const lastInitial = words
      .slice(1)
      .map((word) => word.charAt(0).toUpperCase() + '.')
      .join(' ');

    return `${firstName} ${lastInitial}`;
  }

  public async sendConnectionRequest(toUser: UserHit, message: string) {
    await sendEmail(
      toUser.id,
      new ConnectionEmailTemplate({
        message: message,
        address: this.email,
        name: this.name,
        link: this.getLink(),
        senderUniversityId: this.universityId,
        receiverUniversityId: toUser.universityId,
      }),
    );

    await sendEmail(
      this.email,
      new ConnectionReceiptEmailTemplate({
        message: message,
        name: toUser.name,
        link: toUser.getLink(),
      }),
    );
  }

  public static override getLink(userId: string, absolute: boolean = true) {
    return UserHit.getLink(userId, absolute);
  }

  public override getLink(absolute: boolean = true) {
    return UserHit.getLink(this.id, absolute);
  }

  public async updateAvatar(profilePic: File) {
    await uploadBytes(this._avatarUploadStorage, profilePic);
    // Wait to make sure the file has uploaded before we notify listeners
    await new Promise((f) => setTimeout(f, 50));
    await this.notifyAvatarUpdated();
  }

  private async notifyAvatarUpdated() {
    await updateDoc(this._doc, {
      avatarUpdatedAt: new Date(),
    });
  }

  public static async existsWithUniversityEmail(universityEmail: string) {
    const snapshot = await getDocs(query(collection(firestore(), User.collectionName), where('universityEmail', '==', universityEmail)));

    return snapshot.docs.length > 0;
  }

  public async verifyBeforeUpdateEmail(currentUser: auth.User, newEmail: string) {
    if (!newEmail.includes('@')) {
      return 'Email is invalid.';
    }

    if (newEmail === currentUser.email) {
      return "You're already using that email.";
    }

    return await verifyBeforeUpdateEmail(currentUser, newEmail)
      .then(async () => {
        return null;
      })
      .catch((e) => {
        logger.error(e);
        if (e.code === 'auth/missing-new-email') {
          return 'Please enter your email.';
        }

        return 'Sorry, something went wrong.';
      });
  }

  public async ensureSyncedWithAuthEmail(authEmail: string) {
    if (this.email !== authEmail) {
      await updateDoc(this._doc, {
        email: authEmail,
      });
    }
  }

  public async setOppApplicationsRead() {
    if (this.hasUnreadOppApplications) {
      await updateDoc(this._doc, {
        hasUnreadOppApplications: false,
      });
    }
  }

  public isCreatorCampusAdmin() {
    return CREATOR_CAMPUS_ADMINS.includes(this.email);
  }

  // TODO: refactor these two functions to have better names
  public hasFullAccessToApp() {
    return this.profileCompleted && this.applicationStatus === 'accepted';
  }

  public hasSomeAccessToApp() {
    return this.hasFullAccessToApp() || this.staffRole === StaffRole.OWNER;
  }

  public static isProfileComplete(userData: UserDbModelSafe | WithFieldValue<UserDbModel>) {
    const requiredFields: (keyof UserDbModelSafe)[] = ['bio', 'name', 'persona', 'role'];
    if (userData.role === Role.MENTOR.id) {
      requiredFields.push('website');
    }

    return requiredFields.every((field) => userData[field] !== '' && userData[field] !== null);
  }

  public async uploadCv(cv: File) {
    await uploadBytes(this._cvStorage, cv).then(async () => {
      await updateDoc(this._doc, {
        cvUploaded: true,
      });
    });
  }

  public async removeCv() {
    await deleteObject(this._cvStorage).then(async () => {
      await updateDoc(this._doc, {
        cvUploaded: false,
      });
    });
  }

  public async getCvDownloadUrl() {
    return await getDownloadURL(this._cvStorage);
  }
}

export interface UserDbModelNoDates {
  applicationStatus: UserApplicationStatus;
  bio: string;
  cvUploaded: boolean;
  email: string;
  emailVerified: boolean;
  fake: boolean;
  founder: boolean;
  githubUsername: string | null;
  hasUnreadOppApplications: boolean;
  hideProfile: boolean;
  karma: number;
  linkedinUsername: string | null;
  name: string;
  notifications: UserNotifications;
  numCompleteProfileRemindersSent: number;
  openToWork: boolean;
  persona: PersonaId | null;
  profileCompleted: boolean;
  projectIds: string[];
  role: RoleId | null;
  score: number;
  services: ServiceId[];
  staffRole: StaffRoleId | null;
  universityEmail: string | null;
  universityId: string;
  website: string | null;
}

export interface UserDbModel extends UserDbModelNoDates {
  avatarUpdatedAt: Timestamp;
  dateJoined: Timestamp;
  featuredUntil: Timestamp | null;
  lastOnline: Timestamp;
  lastViewedDiscussion: Timestamp;
}

export interface UserDbModelSafe extends UserDbModelNoDates {
  avatarUpdatedAt: any;
  dateJoined: any;
  featuredUntil: any | null;
  lastOnline: any;
  lastViewedDiscussion: any;
}

export class UserConverter implements FirestoreDataConverter<User, UserDbModel> {
  toFirestore(user: WithFieldValue<User>): WithFieldValue<UserDbModel> {
    return {
      applicationStatus: user.applicationStatus,
      avatarUpdatedAt: user.avatarUpdatedAt instanceof FieldValue ? user.avatarUpdatedAt : Timestamp.fromDate(user.avatarUpdatedAt as Date),
      bio: user.bio,
      cvUploaded: user.cvUploaded,
      dateJoined: user.dateJoined instanceof FieldValue ? user.dateJoined : Timestamp.fromDate(user.dateJoined as Date),
      email: user.email,
      emailVerified: user.emailVerified,
      fake: false,
      featuredUntil: user.featuredUntil === null || user.featuredUntil instanceof FieldValue ? user.featuredUntil : Timestamp.fromDate(user.featuredUntil as Date),
      founder: user.founder,
      githubUsername: user.githubUsername,
      hasUnreadOppApplications: user.hasUnreadOppApplications,
      hideProfile: user.hideProfile,
      karma: user.karma,
      lastOnline: user.lastOnline instanceof FieldValue ? user.lastOnline : Timestamp.fromDate(user.lastOnline as Date),
      lastViewedDiscussion: user.lastViewedDiscussion instanceof FieldValue ? user.lastViewedDiscussion : Timestamp.fromDate(user.lastViewedDiscussion as Date),
      linkedinUsername: user.linkedinUsername,
      name: user.name,
      notifications: user.notifications,
      numCompleteProfileRemindersSent: user.numCompleteProfileRemindersSent,
      openToWork: user.openToWork,
      persona: user.persona instanceof FieldValue ? user.persona : user.persona?.id || null,
      profileCompleted: user.profileCompleted,
      projectIds: user.projectIds,
      role: user.role instanceof FieldValue ? user.role : user.role?.id || null,
      score: user.score,
      services: user.services instanceof FieldValue ? user.services : user.services.map((s) => (s as Service).id),
      staffRole: user.staffRole instanceof FieldValue ? user.staffRole : user.staffRole?.id || null,
      universityEmail: user.universityEmail,
      universityId: user.universityId,
      website: user.website,
    };
  }

  fromFirestore(snapshot: QueryDocumentSnapshot, options: SnapshotOptions): User {
    const data = snapshot.data(options) as UserDbModel;
    return new User(
      snapshot.id,
      data.applicationStatus,
      data.avatarUpdatedAt.toDate(),
      data.bio,
      data.cvUploaded,
      data.dateJoined.toDate(),
      data.email,
      data.emailVerified,
      data.featuredUntil?.toDate() || null,
      data.founder,
      data.githubUsername,
      data.hasUnreadOppApplications,
      data.hideProfile,
      data.karma,
      data.lastOnline.toDate(),
      data.lastViewedDiscussion.toDate(),
      data.linkedinUsername,
      data.name,
      data.notifications,
      data.numCompleteProfileRemindersSent,
      data.openToWork,
      Persona.fromId(data.persona),
      data.profileCompleted,
      data.projectIds,
      Role.fromId(data.role),
      data.score,
      data.services.map((s) => Service.fromId(s)!),
      StaffRole.fromId(data.staffRole),
      data.universityEmail,
      data.universityId,
      data.website,
    );
  }
}
