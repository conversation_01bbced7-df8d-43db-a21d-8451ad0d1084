import {useState} from 'react';
import {UserHit} from '@creator-campus/common';
import {AlgoliaIndexExplorer, PageHeader, PageShell} from '@creator-campus/common-components';
import Button from '@mui/joy/Button';
import {Add} from '@mui/icons-material';
import UserCard from '../components/UserCard.tsx';
import AddUserModal from '../components/AddUserModal.tsx';

export default function ManageUsersPage() {
  const [addUserModalOpen, setAddUserModalOpen] = useState<boolean>(false);

  return (
    <PageShell footer={false}>
      <PageHeader
        title='Manage Users'
        subtitle='Watch over the empire!'
      />
      <Button
        variant='outlined'
        startDecorator={<Add />}
        sx={{mb: 2}}
        onClick={() => setAddUserModalOpen(true)}
      >
        Add user
      </Button>
      <AddUserModal
        open={addUserModalOpen}
        setOpen={setAddUserModalOpen}
      />
      <AlgoliaIndexExplorer
        index={'people'}
        itemBuilder={(userHit) => (
          <UserCard
            key={userHit.id}
            userHit={UserHit.fromAlgoliaHit(userHit)}
            sx={{mb: 2}}
          />
        )}
      />
    </PageShell>
  );
}
