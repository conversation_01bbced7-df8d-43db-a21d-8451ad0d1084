import Stack from '@mui/joy/Stack';
import Typography from '@mui/joy/Typography';

interface Props {
  title: string;
  subtitle?: string;
  sx?: object;
}

export function PageHeader({title, subtitle, sx}: Props) {
  return (
    <Stack sx={{mb: 2, ...sx}}>
      <Stack
        direction='row'
        justifyContent='space-between'
        sx={{width: '100%'}}
      >
        <Typography level='h2'>{title}</Typography>
      </Stack>
      {subtitle && (
        <Typography
          level='body-md'
          color='neutral'
        >
          {subtitle}
        </Typography>
      )}
    </Stack>
  );
}
