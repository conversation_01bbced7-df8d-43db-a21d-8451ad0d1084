import Typography from '@mui/joy/Typography';
import {formatDistance} from 'date-fns';
import Box from '@mui/joy/Box';
import IconButton from '@mui/joy/IconButton';
import {CommentRounded, PushPin, PushPinOutlined} from '@mui/icons-material';
import {TiArrowUpOutline, TiArrowUpThick} from 'react-icons/ti';
import Card from '@mui/joy/Card';
import DiscussionPostTitle from './DiscussionPostTitle.tsx';
import {DiscussionComment, DiscussionPost, DiscussionPostBase, firestore, functions, isUrlValid, logger, NonexistentUser, University, User} from '@creator-campus/common';
import {LoadingIndicator, useSnackbar, useUser} from '@creator-campus/common-components';
import DiscussionCommentsModal from './modals/DiscussionCommentsModal.tsx';
import {useEffect, useRef, useState} from 'react';
import {arrayRemove, arrayUnion, onSnapshot, runTransaction} from 'firebase/firestore';
import DiscussionPostText from './DiscussionPostText.tsx';
import {APIResponse, LinkPreview} from '@dhaiwat10/react-link-preview';
import {httpsCallable} from 'firebase/functions';
import {useTheme} from '@mui/material/styles';
import {discussionFeedWidth} from './DiscussionContent.tsx';
import Stack from '@mui/joy/Stack';
import DiscussionAttachmentCard from './DiscussionAttachmentCard.tsx';
import {normaliseUrl} from '../utils.tsx';
import Tooltip from '@mui/joy/Tooltip';
import useMediaQuery from '@mui/material/useMediaQuery';

interface Props {
  initialPost: DiscussionPost | DiscussionComment;
  withCommentsButton?: boolean;
  sx?: object;
  maxBodyLines?: number;
  cachedUsers: Record<string, User | NonexistentUser>;
  cachedProfilePicUrls: Record<string, string>;
  updateCachedUsers: (comments: string[]) => void;
  withAttachments?: boolean;
  universityId?: string;
}

export default function DiscussionPostCard({initialPost, withCommentsButton = false, sx, maxBodyLines, cachedUsers, cachedProfilePicUrls, updateCachedUsers, withAttachments = true, universityId}: Props) {
  const urlMetadataMaxTitleLength = 50;
  const urlMetadataMaxDescLength = 100;

  const [post, setPost] = useState<DiscussionPostBase>(initialPost);
  const [commentsModalOpen, setCommentsModalOpen] = useState<boolean>(false);
  const [pinLoading, setPinLoading] = useState<boolean>(false);
  const cachedUrlMetadata = useRef<Record<string, APIResponse | null>>({});

  const {showErrorSnackbar} = useSnackbar();
  const {user} = useUser();
  const lightTheme = useTheme().palette.mode === 'light';
  const isSmallScreen = useMediaQuery('(max-width: 450px)');

  // null means the author's data is still being fetched
  if (!post?.authorId || !user) {
    return <></>;
  }

  const author: User | NonexistentUser | null = cachedUsers[post.authorId];
  const profilePicUrl: string | null = cachedProfilePicUrls[post.authorId];
  const postUrls = post.text
    .split(/(\s+)/)
    .filter(isUrlValid)
    .map(normaliseUrl)
    .filter((url) => url !== null);

  const attachments = post.attachments.filter((a) => a.type === 'attachment');
  const images = post.attachments.filter((a) => a.type === 'image');

  let now = new Date();

  async function toggleLike() {
    await post
      .upVote(user!)
      .then(async () => {
        await post
          .refresh()
          .then((updatedPost) => {
            setPost(updatedPost);
          })
          .catch((e) => {
            logger.error(e);
            showErrorSnackbar(e, 'Error fetching post updates.');
          });
      })
      .catch((e) => {
        logger.error(e);
        showErrorSnackbar(e, 'Error liking post.');
      });
  }

  async function fetchUrlMetadata(url: string) {
    if (cachedUrlMetadata.current[url] !== undefined) {
      // Return cached metadata if available
      return cachedUrlMetadata.current[url];
    }

    // Fetch metadata via Firebase Function
    const metadata = await httpsCallable(
      functions(),
      'getUrlMetadata',
    )({
      url,
    })
      .then((response) => {
        const data = response?.data as APIResponse | null;
        if (!data) {
          logger.error('(getUrlMetadata) Response data is null.');
          return null;
        }

        if (data.title && data.title.length > urlMetadataMaxTitleLength) {
          data.title = `${data.title.substring(0, urlMetadataMaxTitleLength).trimEnd()}...`;
        }

        return data;
      })
      .catch((e) => {
        logger.error(e);
        return null;
      });

    // Update the cache
    cachedUrlMetadata.current[url] = metadata;

    return metadata;
  }

  useEffect(() => {
    const unsub = onSnapshot(post.doc(), (docSnap) => {
      setPost(docSnap.data()!);
    });

    return () => unsub();
  }, []);

  // Update post date text every minute
  useEffect(() => {
    const interval = setInterval(() => {
      now = new Date();
    }, 60000);

    return () => clearInterval(interval);
  }, []);

  const canPinPost = post instanceof DiscussionPost && (user.isCreatorCampusAdmin() || (!!user.staffRole && !!universityId));

  return (
    <Card
      key={post.id}
      color={post instanceof DiscussionPost && post.pinned ? 'primary' : undefined}
      size='sm'
      sx={sx}
    >
      {/*Time posted text*/}
      <Typography sx={{fontSize: {xs: 12, sm: 14}, position: 'absolute', top: {xs: undefined, sm: 14}, bottom: {xs: 10, sm: undefined}, right: {xs: 12, sm: 18}}}>{formatDistance(post.datePosted, now, {addSuffix: true})}</Typography>
      {canPinPost && (
        <Box sx={{position: 'absolute', top: -16, right: 8}}>
          <Tooltip
            enterTouchDelay={0}
            variant='solid'
            title={`${post.pinned ? 'Stop spotlighting' : 'Spotlight'} this post at the top of your discussion page.`}
          >
            <IconButton
              variant={'soft'}
              sx={{width: 2}}
              size={'sm'}
              onClick={async () => {
                setPinLoading(true);
                const newValue = !post.pinned;

                await runTransaction(firestore(), async (transaction) => {
                  transaction.update(post.doc(), {pinned: newValue});

                  const discussionDoc = post.universityId ? University.doc(post.universityId) : DiscussionPost.globalDoc();
                  transaction.update(discussionDoc, {
                    pinnedPosts: newValue ? arrayUnion(post.id) : arrayRemove(post.id),
                  });
                });

                setPinLoading(false);
              }}
            >
              {pinLoading ? <LoadingIndicator size={'sm'} /> : post.pinned ? <PushPin color={'primary'} /> : <PushPinOutlined />}
            </IconButton>
          </Tooltip>
        </Box>
      )}

      {/*User profile picture and name*/}
      <DiscussionPostTitle
        author={author}
        profilePicUrl={profilePicUrl}
      />

      {/*Post text*/}
      <DiscussionPostText
        text={post.text}
        maxBodyLines={maxBodyLines}
      />

      {withAttachments && (
        <Stack
          direction={isSmallScreen ? 'column' : 'row'}
          flexWrap={'wrap'}
          gap={0.5}
          alignItems={'center'}
        >
          {images.length > 0
            ? images.map((a) => (
                <DiscussionAttachmentCard
                  key={a.id}
                  attachmentId={a.id}
                  universityId={universityId}
                  fileType={a.type}
                  downloadable={{
                    post,
                    metadata: a,
                  }}
                />
              ))
            : postUrls.slice(postUrls.length - 2).map((postUrl, i) => (
                <LinkPreview
                  key={`${postUrl}-${i}`}
                  url={postUrl}
                  descriptionLength={urlMetadataMaxDescLength}
                  width={isSmallScreen ? '99%' : '49%'}
                  imageHeight={Math.min(discussionFeedWidth / 3, window.innerWidth / 3)}
                  backgroundColor={lightTheme ? 'white' : 'black'}
                  primaryTextColor={lightTheme ? 'black' : 'white'}
                  secondaryTextColor={lightTheme ? 'dimgray' : 'lightgray'}
                  borderColor={lightTheme ? 'lightgray' : 'dimgray'}
                  fallbackImageSrc={' '}
                  fetcher={fetchUrlMetadata}
                />
              ))}
        </Stack>
      )}

      {withAttachments && (
        <Stack
          direction={'row'}
          flexWrap={'wrap'}
          gap={0.5}
          alignItems={'center'}
        >
          {attachments.map((a) => (
            <DiscussionAttachmentCard
              key={a.id}
              attachmentId={a.id}
              universityId={universityId}
              fileType={a.type}
              downloadable={{
                post,
                metadata: a,
              }}
            />
          ))}
        </Stack>
      )}

      <Box sx={{display: 'flex', gap: 1}}>
        {/*Like button*/}
        <Box sx={{display: 'flex', alignItems: 'center'}}>
          <IconButton onClick={toggleLike}>
            {/*@ts-ignore*/}
            {post.upVotes.includes(user.id) ? (
              <TiArrowUpThick
                color={'#f66520'}
                size={28}
              />
            ) : (
              <TiArrowUpOutline size={22} />
            )}
          </IconButton>
          <Typography
            level='body-md'
            sx={{fontWeight: 'bold'}}
          >
            {post.upVotes.length}
          </Typography>
        </Box>

        {/*Comment button*/}
        {post instanceof DiscussionPost && withCommentsButton && (
          <Box sx={{display: 'flex', alignItems: 'center'}}>
            <IconButton onClick={() => setCommentsModalOpen(!commentsModalOpen)}>
              <CommentRounded color='primary' />
            </IconButton>
            <Typography
              level='body-md'
              sx={{fontWeight: 'bold'}}
            >
              {post.numComments}
            </Typography>
            {commentsModalOpen && (
              <DiscussionCommentsModal
                post={post}
                cachedUsers={cachedUsers}
                cachedProfilePicUrls={cachedProfilePicUrls}
                updateCachedUsers={updateCachedUsers}
                onClose={() => setCommentsModalOpen(false)}
                universityId={universityId}
              />
            )}
          </Box>
        )}
      </Box>
    </Card>
  );
}
