import Link from '@mui/joy/Link';
import Typography from '@mui/joy/Typography';
import Stack from '@mui/joy/Stack';
import {useNavigate, useSearchParams} from 'react-router-dom';
import {useEffect, useState} from 'react';
import {ApprovedMentor, functions, Role} from '@creator-campus/common';
import {LoadingIndicator} from '@creator-campus/common-components';
import {httpsCallable} from 'firebase/functions';
import CreateAccountFromLinkForm from '../components/CreateAccountFromLinkForm.tsx';
import {ProductionLandingPageShell} from '../components/ProductionLandingPageShell.tsx';

export default function MentorSignupPage() {
  const [approvedMentor, setApprovedMentor] = useState<ApprovedMentor | null>(null);

  const [searchParams] = useSearchParams();
  const mentorId = searchParams.get('mentorId');
  const token = searchParams.get('token');

  const navigate = useNavigate();

  useEffect(() => {
    if (!mentorId || !token) {
      navigate('/login');
      return;
    }

    httpsCallable(
      functions(),
      'validateMentorToken',
    )({mentorId, token})
      .then((response) => {
        const data = response.data as any;

        if (data.valid) {
          setApprovedMentor(data as ApprovedMentor);
        } else {
          navigate('/login');
          return;
        }
      })
      .catch(() => {
        navigate('/login');
        return;
      });
  }, []);

  return (
    <ProductionLandingPageShell>
      <Stack gap={1}>
        <Typography level='h3'>Create mentor account</Typography>
        <Typography level='body-sm'>
          Already have an account?{' '}
          <Link
            href={'/login'}
            level='title-sm'
          >
            Sign in.
          </Link>
        </Typography>
      </Stack>
      {approvedMentor === null ? (
        <LoadingIndicator size='md' />
      ) : (
        <CreateAccountFromLinkForm
          email={approvedMentor.email}
          universityId={approvedMentor.universityId}
          role={Role.MENTOR}
        />
      )}
    </ProductionLandingPageShell>
  );
}
