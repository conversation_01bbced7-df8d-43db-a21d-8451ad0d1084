import {onCall} from 'firebase-functions/v2/https';
import {config} from 'dotenv';
import urlMetadata from 'url-metadata';
import {onSchedule} from 'firebase-functions/v2/scheduler';
import * as admin from 'firebase-admin';
import {Timestamp} from 'firebase-admin/firestore';
import {ProjectDbModelSafe, UserDbModelSafe} from '@creator-campus/common';

// Load .env if running locally
const usingEmulators = !!process.env.FIRESTORE_EMULATOR_HOST;
if (usingEmulators) {
  config();
}

interface GetUrlMetadataPayload {
  url: string;
}

export const getUrlMetadata = onCall<GetUrlMetadataPayload, Promise<object | null>>({minInstances: 1}, async (request) => {
  const {url} = request.data;

  // Parse the URL to extract hostname
  const parsedUrl = new URL(url);
  const hostName = parsedUrl.hostname; // e.g., "example.com"

  try {
    const metadata = await urlMetadata(url);
    return {
      title: metadata.title || null,
      description: metadata.description || metadata.title || null,
      image: metadata.image || metadata['og:image'] || '',
      siteName: metadata['og:site_name'] || metadata.title || null,
      hostname: hostName,
    };
  } catch (e) {
    console.error(e);
    return null;
  }
});

export const cleanUpFeaturedContent = onSchedule('every day 00:00', async () => {
  for (const col of ['projects', 'people']) {
    console.log(`Checking ${col}...`);

    const snapshot = await admin.firestore().collection(col).where('featuredUntil', '!=', null).get();
    console.log(`Found ${snapshot.size} featured docs to check.`);

    const now = new Date();
    for (const doc of snapshot.docs) {
      const data = doc.data() as UserDbModelSafe | ProjectDbModelSafe;
      const featuredUntil = data.featuredUntil as Timestamp;
      if (featuredUntil.toDate() < now) {
        console.log(`${doc.id} will no longer featured.`);
        await doc.ref.update({
          featuredUntil: null,
        });
      } else {
        console.log(`${doc.id} will remain featured.`);
      }
    }
  }
});
