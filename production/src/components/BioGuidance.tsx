import Stack from '@mui/joy/Stack';
import Typography from '@mui/joy/Typography';

interface Props {
  founder: boolean;
  talent: boolean;
  supporter: boolean;
}

export function BioGuidance({founder, talent, supporter}: Props) {
  return (
    <Stack spacing={1}>
      {!founder && !talent && !supporter && <Typography level={'body-sm'}>What’s your background and how would you contribute to this community of emerging entrepreneurial and creative talent?</Typography>}
      {founder && (
        <Typography level='body-sm'>
          {'As a '}
          <Typography level={'title-sm'}>Founder</Typography>, tell us your story! Try to include what sectors you’re interested in and your unique strengths. This helps others know when to reach out for your expertise or support. Don’t include too much information about your startup - save that for when you add your
          startup.
        </Typography>
      )}
      {talent && (
        <Typography level='body-sm'>
          {'As '}
          <Typography level={'title-sm'}>Startup Talent</Typography>, tell us about yourself! Try to include what skills you would bring to a team, what sectors you’re interested in, and any relevant experience. Further, what type of startup are you looking to join? This will help people match with you for the right
          opportunities.
        </Typography>
      )}
      {supporter && (
        <Typography level='body-sm'>
          {'As a '}
          <Typography level={'title-sm'}>Supporter</Typography>, what’s your background and how would you contribute to this community of emerging entrepreneurial and creative talent?
        </Typography>
      )}
    </Stack>
  );
}
