import {<PERSON>, But<PERSON>, Divider, Stack, Typography} from '@mui/joy';
import {HowToReg, OpenInNew} from '@mui/icons-material';
import {Opportunity, OpportunityApplication, User} from '@creator-campus/common';
import {LoadingIndicator, ModalShell} from '@creator-campus/common-components';
import {useEffect, useState} from 'react';
import ProfileCard from './ProfileCard.tsx';
import Chip from '@mui/joy/Chip';

interface Props {
  opp: Opportunity;
  application: OpportunityApplication;
  onAccept: () => void;
}

export function ApplicantCard({opp, application, onAccept}: Props) {
  const [showProfileModal, setShowProfileModal] = useState<boolean>(false);
  const [applicant, setApplicant] = useState<User | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    User.fetch(application.applicantId).then((user) => {
      setApplicant(user);
      setLoading(false);
    });
  }, []);

  return (
    <>
      {showProfileModal && applicant && (
        <ModalShell
          onClose={() => setShowProfileModal(false)}
          closeOnBackgroundClick={true}
          withCloseButton={true}
          withPadding={false}
        >
          <Box
            pr={6}
            pt={3}
            pb={3}
            pl={3}
          >
            <ProfileCard
              user={applicant}
              withCard={false}
              withActionButton={false}
            />
          </Box>
        </ModalShell>
      )}
      <Box sx={{mb: 2, p: 2}}>
        {application.dateAccepted && (
          <Box sx={{mb: 1}}>
            <Chip
              startDecorator={<HowToReg />}
              color='success'
              variant='soft'
            >
              Accepted
            </Chip>
          </Box>
        )}
        <Stack
          direction={'row'}
          spacing={1}
          alignItems={'center'}
          justifyContent={'space-between'}
        >
          <Stack>
            <Typography
              level='body-md'
              fontWeight='bold'
            >
              {application.applicantName}
            </Typography>
            <Typography level='body-sm'>{application.applicantEmail}</Typography>
          </Stack>
          <Stack direction={'row'}>
            {application.cvDownloadUrl && (
              <Button
                variant={'plain'}
                startDecorator={<OpenInNew />}
                onClick={() => window.open(application.cvDownloadUrl!, '_blank')}
              >
                View CV
              </Button>
            )}
            <Button
              disabled={loading}
              variant={'plain'}
              onClick={() => setShowProfileModal(true)}
            >
              {loading ? <LoadingIndicator size={'sm'} /> : 'View profile'}
            </Button>
          </Stack>
        </Stack>
        <Typography
          level='body-sm'
          sx={{mt: 1}}
        >
          {application.message}
        </Typography>
        {opp.fillDate ? null : (
          <Button
            color='success'
            variant='soft'
            onClick={onAccept}
            sx={{mt: 1}}
          >
            Accept
          </Button>
        )}
        <Divider sx={{mt: 2, mb: -3}} />
      </Box>
    </>
  );
}
