import Button from '@mui/joy/Button';
import ButtonGroup from '@mui/joy/ButtonGroup';
import {ModalShell, ModalHeader} from '@creator-campus/common-components';
import {Blocker} from 'react-router-dom';

interface Props {
  blocker: Blocker;
}

export function BlockNavigationModal({blocker}: Props) {
  return (
    <ModalShell onClose={() => (blocker.reset ? blocker.reset() : null)}>
      <ModalHeader
        title='You have unsaved changes'
        subtitle='Are you sure you want to leave? Any unsaved changes will be lost.'
      />

      <ButtonGroup
        spacing={1}
        sx={{mt: 2}}
      >
        <Button
          variant='solid'
          color='primary'
          onClick={() => (blocker.proceed ? blocker.proceed() : null)}
        >
          Confirm
        </Button>
        <Button
          variant='outlined'
          color='neutral'
          onClick={() => (blocker.reset ? blocker.reset() : null)}
        >
          Cancel
        </Button>
      </ButtonGroup>
    </ModalShell>
  );
}
