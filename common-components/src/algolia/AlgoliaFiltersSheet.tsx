import {ReactElement, useState} from 'react';
import Drawer from '@mui/joy/Drawer';
import Button from '@mui/joy/Button';
import DialogTitle from '@mui/joy/DialogTitle';
import DialogContent from '@mui/joy/DialogContent';
import ModalClose from '@mui/joy/ModalClose';
import Divider from '@mui/joy/Divider';
import Stack from '@mui/joy/Stack';
import Sheet from '@mui/joy/Sheet';
import TuneIcon from '@mui/icons-material/TuneRounded';
import IconButton from '@mui/joy/IconButton';
import {Clear} from '@mui/icons-material';
import Tooltip from '@mui/joy/Tooltip';

export type AlgoliaFilters = Record<string, string | string[]>;
export type UpdateAlgoliaFilters = (key: string, filter: string | string[] | null) => void;

interface Props {
  filterPanel: ReactElement;
  filtering: boolean;
  clearFilters: () => void;
}

export function AlgoliaFiltersSheet({filterPanel, filtering, clearFilters}: Props) {
  const [open, setOpen] = useState(false);

  return (
    <Stack
      useFlexGap
      direction='row'
      spacing={{xs: 0, sm: 2}}
      justifyContent={{xs: 'space-between'}}
      flexWrap='wrap'
      sx={{minWidth: 0}}
    >
      <Stack
        direction={'row'}
        spacing={1}
        alignItems={'center'}
      >
        <Button
          variant='outlined'
          color='neutral'
          startDecorator={<TuneIcon />}
          onClick={() => setOpen(true)}
        >
          Filter
        </Button>
        {filtering && (
          <Tooltip
            title={'Clear filters'}
            placement={'right'}
            variant='solid'
          >
            <IconButton
              variant={'outlined'}
              onClick={clearFilters}
            >
              <Clear sx={{height: 20}} />
            </IconButton>
          </Tooltip>
        )}
      </Stack>
      <Drawer
        size='md'
        variant='plain'
        open={open}
        anchor={'right'}
        onClose={() => setOpen(false)}
        slotProps={{
          content: {
            sx: {
              bgcolor: 'transparent',
              p: {md: 3, sm: 2, xs: 2},
              width: {sm: '400px', xs: '90%'},
              boxShadow: 'none',
            },
          },
        }}
      >
        <Sheet
          sx={{
            borderRadius: 'md',
            p: 2,
            display: 'flex',
            flexDirection: 'column',
            gap: 2,
            height: '100%',
            overflow: 'auto',
          }}
        >
          <DialogTitle>Filters</DialogTitle>
          <ModalClose />
          <Divider sx={{mt: 'auto'}} />
          <DialogContent sx={{gap: 2}}>{filterPanel}</DialogContent>

          <Divider sx={{mt: 'auto'}} />
          <Stack
            direction='row'
            justifyContent='space-between'
            useFlexGap
            spacing={1}
          >
            <Button onClick={() => setOpen(false)}>Show results</Button>
          </Stack>
        </Sheet>
      </Drawer>
    </Stack>
  );
}
