import {onDocumentCreated, onDocumentDeleted, onDocumentUpdated} from 'firebase-functions/v2/firestore';
import * as admin from 'firebase-admin';
import {getInfluxWriter, newPoint} from './influxdb';
import {onSchedule} from 'firebase-functions/v2/scheduler';
import {FieldValue, QueryDocumentSnapshot, Timestamp} from 'firebase-admin/firestore';
import {
  CompleteProfileReminderEmailTemplate,
  CREATOR_CAMPUS_ADMINS,
  InactiveProfileReminderEmailTemplate,
  MembershipApplication,
  MembershipApplicationDbModel,
  OpportunityApplication,
  PartnershipData,
  PendingMembershipApplicationsEmailTemplate,
  Project,
  REAPPLICATION_COOLDOWN_DAYS,
  Role,
  StaffRole,
  University,
  UniversityDbModelSafe,
  UniversityPartnershipDbModel,
  User,
  UserDbModelSafe,
  UserLimitAlmostReachedEmailTemplate,
  UserLimitReachedEmailTemplate,
} from '@creator-campus/common';
import {calculateProfileScore, calculateProjectScore, emailC<PERSON><PERSON>ampusAdmins, fetchUniversityOwners, sendEmail, initSentryForFunction} from './utils';
import {onCall} from 'firebase-functions/v2/https';
import allUniversities from './uk_universities.json';
import * as Sentry from '@sentry/node';

async function checkUniHasMentors(universityId: string) {
  console.log(`Checking if university ${universityId} has any mentors...`);
  const mentorsSnapshot = await admin.firestore().collection(User.collectionName).where('universityId', '==', universityId).where('role', '==', Role.MENTOR.id).where('profileCompleted', '==', true).count().get();
  const numMentors = mentorsSnapshot.data().count;

  console.log(`University ${universityId} has ${numMentors} mentors. Updating Firestore doc...`);
  await admin
    .firestore()
    .collection(University.collectionName)
    .doc(universityId)
    .update({
      hasMentors: numMentors > 0,
    });
}

async function handleUserLimitReached(universityId: string, userLimit: number) {
  console.log(`User limit reached for university ${universityId}.`);
  await admin.firestore().collection(University.collectionName).doc(universityId).update({
    reachedUserLimit: true,
  });

  console.log('Fetching university owners...');
  const owners = await fetchUniversityOwners(universityId);
  const ownerEmails = owners.map((o) => o.email);

  console.log(`Sending warning email (UserLimitReachedEmailTemplate) to university owners: ${ownerEmails}`);
  await Promise.all(ownerEmails.map((email) => sendEmail(email, new UserLimitReachedEmailTemplate({userLimit})).catch(console.error)));
}

async function handleUserLimitAlmostReached(universityId: string, userLimit: number, numUsers: number) {
  console.log(`User limit almost reached for university ${universityId}. Updating Firestore doc...`);
  await admin.firestore().collection(University.collectionName).doc(universityId).update({
    almostReachedUserLimit: true,
  });

  console.log('Fetching university owners...');
  const owners = await fetchUniversityOwners(universityId);
  const ownerEmails = owners.map((o) => o.email);

  console.log(`Sending warning email (UserLimitAlmostReachedEmailTemplate) to university owners: ${ownerEmails}`);
  await Promise.all(ownerEmails.map((email) => sendEmail(email, new UserLimitAlmostReachedEmailTemplate({userLimit, numUsers})).catch(console.error)));
}

async function checkUniversityReachedUserLimit(universityId: string) {
  // Count the number of users for this university
  const numUsersSnapshot = await admin.firestore().collection(User.collectionName).where('universityId', '==', universityId).where('profileCompleted', '==', true).count().get();
  const numUsers = numUsersSnapshot.data().count;
  console.log(`Found ${numUsers} users for university ${universityId}.`);

  // Fetch user limit for this university
  const partnershipDataSnapshot = await admin.firestore().collection(University.collectionName).doc(universityId).collection('private').doc('partnership').get();
  const userLimit = partnershipDataSnapshot.data()?.userLimit;
  console.log(`Found user limit of ${userLimit} for university ${universityId}.`);

  if (userLimit) {
    if (numUsers > userLimit) {
      await handleUserLimitReached(universityId, userLimit);
    } else if (numUsers > University.getUserLimitWarningThreshold(userLimit)) {
      await handleUserLimitAlmostReached(universityId, userLimit, numUsers);
    } else {
      console.log(`User limit not reached for university ${universityId}.`);
    }
  } else {
    console.error(`No user limit found for university ${universityId}.`);
  }
}

export const onUserCreated = onDocumentCreated(
  {
    document: 'people/{userId}',
    secrets: ['REACT_APP_INFLUX_DB_TOKEN'],
  },
  async (event) => {
    initSentryForFunction('onUserCreated');

    if (!event.data) {
      console.error('Event data is undefined!');
      return;
    }

    const user = event.data.data() as UserDbModelSafe;
    const userId = event.params.userId;
    Sentry.setUser({id: userId, ...user});
    console.log(`Detected user ${userId} created.`);

    // Auto-accept mentors
    if (user.role === Role.MENTOR.id) {
      await admin.firestore().collection(User.collectionName).doc(userId).update({
        applicationStatus: 'accepted',
      });
    }

    await ensureUniversityDocExists(user.universityId);
    await checkUniversityReachedUserLimit(user.universityId);

    const influxToken = String(process.env.REACT_APP_INFLUX_DB_TOKEN);
    const influxWriter = getInfluxWriter(influxToken);

    const point = newPoint().intField('numUsers', 1).tag('university', user.universityId);
    influxWriter.writePoint(point);
  },
);

export const onUserUpdated = onDocumentUpdated('people/{userId}', async (event) => {
  initSentryForFunction('onUserUpdated');

  if (!event.data) {
    console.error('Event data is undefined!');
    return;
  }

  const userId = event.params.userId;
  const before = event.data.before.data() as UserDbModelSafe;
  const after = event.data.after.data() as UserDbModelSafe;
  Sentry.setUser({id: userId, ...after});
  Sentry.setContext('userBefore', {...before});
  Sentry.setContext('userAfter', {...after});

  console.log(`Detected user ${userId} updated.`);

  // Update opportunity applications if user details changed
  const cvRemoved = before.cvUploaded && !after.cvUploaded;
  if (before.email !== after.email || before.name !== after.name || cvRemoved) {
    console.log('Detected user email or name changed.');
    await updateUserApplications(userId, after.name, after.email, cvRemoved);
  }

  // Show hidden projects if user has been accepted
  if (before.applicationStatus !== 'accepted' && after.applicationStatus === 'accepted') {
    console.log('Detected membership application accepted.');
    if (after.projectIds.length > 0) {
      await showHiddenProjects(after.projectIds);
    }
  }

  if (before.applicationStatus !== 'submitted' && after.applicationStatus === 'submitted') {
    console.log('Detected membership application submitted.');
    await emailCreatorCampusAdmins('New User Application', `${after.name} from ${after.universityId} has applied to join Creator Campus. Please log in to the admin app (admin.creatorcampus.io) to review their application.`);
  }

  const {profileCompleteStatusChanged, isProfileCompletedNow} = checkProfileCompletionChanged(after);
  console.log(`Profile complete status changed: ${profileCompleteStatusChanged}. Is profile completed now: ${isProfileCompletedNow}.`);

  if ((before.role === Role.MENTOR.id) !== (after.role === Role.MENTOR.id)) {
    // Role changed to or from mentor
    await checkUniHasMentors(after.universityId);
  }

  // Update project scores if user's karma or last online changed
  const projectScoresChanged = before.karma !== after.karma || before.lastOnline !== after.lastOnline;
  if (projectScoresChanged) {
    for (const projectId of after.projectIds) {
      const newScore = await calculateProjectScore(projectId);
      console.log(`Project ${projectId} score changed to ${newScore}. Updating...`);
      await admin.firestore().collection(Project.collectionName).doc(projectId).update({
        score: newScore,
      });
    }
  }

  const {founderStatusChanged, isFounderNow} = checkFounderStatusChanged(after);
  const lastOnlineChangedSignificantly = Math.abs((before.lastOnline as Timestamp).toMillis() - (after.lastOnline as Timestamp).toMillis()) > 1000 * 60 * 60 * 24; // 1 day
  const profileScoreChanged = before.karma !== after.karma || lastOnlineChangedSignificantly || before.bio.length !== after.bio.length || !!before.linkedinUsername !== !!after.linkedinUsername || !!before.githubUsername !== !!after.githubUsername || !!before.website !== !!after.website;

  if (founderStatusChanged || profileCompleteStatusChanged || profileScoreChanged) {
    const newScore = profileScoreChanged ? await calculateProfileScore(userId) : null;
    if (newScore) {
      console.log(`Profile score changed from ${before.score} to ${newScore}.`);
    }

    const userUpdate = {
      ...(founderStatusChanged && {founder: isFounderNow}),
      ...(profileCompleteStatusChanged && {profileCompleted: isProfileCompletedNow}),
      ...(newScore && {score: newScore}),
    };

    console.log(`Updating user ${userId} with data:`, userUpdate);
    return event.data.after.ref.update(userUpdate);
  }

  return null;
});

async function updateUserApplications(userId: string, name: string, email: string, cvRemoved: boolean) {
  const applications = await admin.firestore().collection(OpportunityApplication.collectionName).where('applicantId', '==', userId).get();

  console.log(`Updating ${applications.size} opportunity applications...`);
  const updates = applications.docs.map((doc) => doc.ref.update({applicantName: name, applicantEmail: email, ...(cvRemoved && {cvDownloadUrl: null})}));
  await Promise.all(updates);
}

async function ensureUniversityDocExists(universityId: string) {
  console.log(`Ensuring university doc exists for ${universityId}...`);
  const universityDoc = admin.firestore().collection('universities').doc(universityId);
  const snapshot = await universityDoc.get();

  if (snapshot.exists) {
    return;
  }

  console.log(`User university ${universityId} does not yet exist. Creating it now...`);
  const uni = allUniversities.find((u) => u.name === universityId);

  if (!uni) {
    console.error(`Error finding university ${universityId} in world universities list.`);
    return;
  }

  const newUniDoc: UniversityDbModelSafe = {
    almostReachedUserLimit: false,
    branding: null,
    dateJoined: Timestamp.now(),
    domain: uni.domains[0],
    hasMentors: false,
    lastDiscussionPost: Timestamp.now(),
    name: uni.name,
    partner: false,
    partnerSince: null,
    paymentState: 'notPaying',
    pinnedPosts: [],
    reachedUserLimit: false,
    selfManagedAlumniApplications: false,
    showcaseEnabled: false,
    sidebarLinks: null,
  };

  console.log('Setting new university doc:', newUniDoc);
  await universityDoc.set(newUniDoc);

  const partnershipData: UniversityPartnershipDbModel = {
    monthlyPriceGbp: 100,
    lastPaymentDate: null,
    paymentInterval: null,
    userLimit: 300,
  };

  console.log('Setting new partnership data doc:', partnershipData);
  await admin.firestore().doc(`${universityDoc.path}/private/${PartnershipData.collectionName}`).set(partnershipData);
}

async function showHiddenProjects(projectIds: string[]) {
  // Expecting only one projectId (only one project submission is allowed), but loop just in case
  for (const projectId of projectIds) {
    await admin.firestore().collection(Project.collectionName).doc(projectId).update({hidden: false});
  }
}

function checkFounderStatusChanged(after: UserDbModelSafe) {
  const isFounderNow = after.projectIds.length > 0;
  const noLongerFounder = after.founder && !isFounderNow;
  const justBecameFounder = !after.founder && isFounderNow;

  const founderStatusChanged = after.applicationStatus === 'accepted' && (noLongerFounder || justBecameFounder);

  return {founderStatusChanged, isFounderNow};
}

function checkProfileCompletionChanged(after: UserDbModelSafe) {
  const isProfileCompletedNow = User.isProfileComplete(after);
  const profileCompleteStatusChanged = after.profileCompleted !== isProfileCompletedNow;

  return {profileCompleteStatusChanged, isProfileCompletedNow};
}

export const onUserDeleted = onDocumentDeleted('people/{userId}', async (event) => {
  initSentryForFunction('onUserDeleted');

  if (!event.data) {
    console.error('Event data is undefined!');
    return;
  }

  const userId = event.params.userId;
  console.log(`Detected user ${userId} deleted.`);
  Sentry.setUser({id: userId, ...event.data.data()});

  // Delete user's projects
  const userProjectsSnapshot = await admin.firestore().collection(Project.collectionName).where('ownerId', '==', userId).get();
  console.log(`Deleting ${userProjectsSnapshot.size} projects for user ${userId}...`);
  for (const project of userProjectsSnapshot.docs) {
    console.log(`Deleting project: ${project.id}`);
    await project.ref.delete();
  }

  // Delete user's opportunity applications
  const userApplicationsSnapshot = await admin.firestore().collection(OpportunityApplication.collectionName).where('applicantId', '==', userId).get();
  console.log(`Deleting ${userApplicationsSnapshot.size} opportunity applications for user ${userId}...`);
  for (const application of userApplicationsSnapshot.docs) {
    console.log(`Deleting application: ${application.id}`);
    await application.ref.delete();
  }

  // Delete user's private collection
  const privateColSnapshot = await admin.firestore().collection(User.collectionName).doc(userId).collection('private').get();
  console.log(`Deleting ${privateColSnapshot.size} private docs for user ${userId}...`);
  for (const doc of privateColSnapshot.docs) {
    console.log(`Deleting private doc: ${doc.id}`);
    await doc.ref.delete();
  }
});

export const sendProfileCompletionReminders = onSchedule('every day 09:00', async () => {
  initSentryForFunction('sendProfileCompletionReminders');

  try {
    const now = new Date();
    const oneDayAgo = new Date(now);
    oneDayAgo.setDate(now.getDate() - 1);

    const sevenDaysAgo = new Date(now);
    sevenDaysAgo.setDate(now.getDate() - 7);

    // Fetch users eligible for the 24-hour reminder
    const twentyFourHourSnapshot = await admin
      .firestore()
      .collection(User.collectionName)
      .where('profileCompleted', '==', false)
      .where('dateJoined', '<=', oneDayAgo) // Users who joined at least 1 day ago
      .where('numCompleteProfileRemindersSent', '==', 0) // And haven't had any reminders yet
      .get();

    // Fetch users eligible for the 7-day reminder
    const sevenDaySnapshot = await admin
      .firestore()
      .collection(User.collectionName)
      .where('profileCompleted', '==', false)
      .where('dateJoined', '<=', sevenDaysAgo) // Users who joined at least 7 days ago
      .where('numCompleteProfileRemindersSent', '==', 1) // And have had one reminder so far
      .get();

    interface Reminder {
      docSnap: QueryDocumentSnapshot;
      type: '7-day' | '24-hour';
    }

    // Combine results
    const allReminders = [...twentyFourHourSnapshot.docs.map((docSnap): Reminder => ({docSnap, type: '24-hour'})), ...sevenDaySnapshot.docs.map((docSnap): Reminder => ({docSnap, type: '7-day'}))];

    if (allReminders.length === 0) {
      console.log('No users found who require profile completion reminders.');
      return;
    }

    console.log(`Sending reminders to ${allReminders.length} users.`);

    // Send reminder emails
    const emailPromises = allReminders.map(async ({docSnap, type}) => {
      const user = docSnap.data();

      await sendEmail(
        user.email,
        new CompleteProfileReminderEmailTemplate({
          displayName: user.name || 'User',
          uid: docSnap.id,
          reminderType: type,
        }),
      ).catch(console.error);

      console.log(`Sent ${type} reminder to ${docSnap.id} (${user.email}).`);

      // Update user fields in Firestore
      await docSnap.ref.update({
        lastCompleteProfileReminderTs: now,
        numCompleteProfileRemindersSent: FieldValue.increment(1),
      });
    });

    await Promise.all(emailPromises);

    console.log('Profile completion reminders sent successfully.');
  } catch (error) {
    console.error('Error sending profile completion reminders:', error);
  }
});

export const cleanUpRejectedUsers = onSchedule('every day 09:00', async () => {
  initSentryForFunction('cleanUpRejectedUsers');

  console.log(`Fetching rejected users with accounts >= ${REAPPLICATION_COOLDOWN_DAYS} days old...`);

  const now = new Date();
  const deleteUsersCreatedBefore = new Date(now);
  deleteUsersCreatedBefore.setDate(now.getDate() - REAPPLICATION_COOLDOWN_DAYS);

  const usersToDelete = await admin.firestore().collection(User.collectionName).where('applicationStatus', '==', 'rejected').where('dateJoined', '<=', Timestamp.fromDate(deleteUsersCreatedBefore)).get();
  console.log(`Found ${usersToDelete.size} user docs to delete: ${usersToDelete.docs.map((u) => u.id)}`);

  const deletePromises: Promise<any>[] = [];

  for (const doc of usersToDelete.docs) {
    console.log(`Deleting ${doc.id}...`);
    deletePromises.push(doc.ref.delete().catch((e) => console.error('Failed to delete doc:', e)));
  }

  await Promise.all(deletePromises);
});

export const notifyCCAdminsOfPendingUsers = onSchedule('every day 18:00', async () => {
  initSentryForFunction('notifyCCAdminsOfPendingUsers');

  console.log(`Fetching pending applications...`);

  const snapshot = await admin.firestore().collection(User.collectionName).where('applicationStatus', '==', 'submitted').count().get();
  const numPendingApplications = snapshot.data().count;

  console.log(`Found ${numPendingApplications} pending applications.`);

  if (numPendingApplications > 0) {
    console.log(`Emailing Creator Campus admins: ${CREATOR_CAMPUS_ADMINS}`);
    for (const email of CREATOR_CAMPUS_ADMINS) {
      await sendEmail(email, new PendingMembershipApplicationsEmailTemplate({numPendingApplications}));
    }
  }
});

interface CreateUserPayload {
  userType: 'production' | 'demo';
  email: string;
  password: string;
  universityId: string;
  firstName: string;
  lastName: string;
  willBeOwner: boolean;
}

export const createUser = onCall<CreateUserPayload, Promise<string | null>>({cors: true, secrets: ['DEMO_FIREBASE_PROJECT_CLIENT_EMAIL', 'DEMO_FIREBASE_PROJECT_PRIVATE_KEY']}, async (request) => {
  initSentryForFunction('createUser');

  const requestAuth = request.auth?.token;

  if (!requestAuth?.email || !requestAuth?.email_verified || !CREATOR_CAMPUS_ADMINS.includes(requestAuth.email)) {
    console.log(`Unauthorized email: ${requestAuth?.email}`);
    return 'Unauthorized.';
  }

  const data = request.data;
  console.log('Received data:', data);

  const demoApp =
    admin.apps.find((app) => app?.name === 'demo') ||
    admin.initializeApp(
      {
        projectId: 'creator-campus-demo',
        credential: admin.credential.cert({
          projectId: 'creator-campus-demo',
          clientEmail: String(process.env.DEMO_FIREBASE_PROJECT_CLIENT_EMAIL),
          privateKey: String(process.env.DEMO_FIREBASE_PROJECT_PRIVATE_KEY).replace(/\\n/g, '\n'),
        }),
      },
      'demo',
    );

  console.log('Creating user...');
  const firebaseAuth = data.userType === 'demo' ? admin.auth(demoApp) : admin.auth();
  const authUser = await firebaseAuth
    .createUser({
      email: data.email,
      password: data.password,
      emailVerified: true,
    })
    .catch((e) => {
      return `${e}`;
    });

  if (typeof authUser === 'string') {
    // Error creating auth user
    return authUser;
  }

  console.log(`Created auth user ${authUser.uid}`);
  const user = User.defaults(authUser.uid, data.email, data.universityId).copyWith({name: `${data.firstName} ${data.lastName}`, staffRole: data.willBeOwner ? StaffRole.OWNER : undefined, applicationStatus: data.willBeOwner ? 'accepted' : undefined});
  const db = data.userType === 'demo' ? admin.firestore(demoApp) : admin.firestore();

  console.log('Adding user doc...');
  const userData: UserDbModelSafe = {
    applicationStatus: user.applicationStatus,
    avatarUpdatedAt: Timestamp.fromDate(user.avatarUpdatedAt),
    bio: user.bio,
    cvUploaded: user.cvUploaded,
    dateJoined: Timestamp.fromDate(user.dateJoined),
    email: user.email,
    emailVerified: true,
    fake: false,
    featuredUntil: null,
    founder: user.founder,
    githubUsername: user.githubUsername,
    hasUnreadOppApplications: user.hasUnreadOppApplications,
    hideProfile: user.hideProfile,
    karma: user.karma,
    lastOnline: Timestamp.fromDate(user.lastOnline),
    lastViewedDiscussion: Timestamp.fromDate(user.lastViewedDiscussion),
    linkedinUsername: user.linkedinUsername,
    name: user.name,
    notifications: user.notifications,
    numCompleteProfileRemindersSent: user.numCompleteProfileRemindersSent,
    openToWork: user.openToWork,
    persona: user.persona?.id || null,
    profileCompleted: user.profileCompleted,
    projectIds: user.projectIds,
    role: user.role?.id || null,
    score: user.score,
    services: user.services.map((s) => s.id),
    staffRole: user.staffRole?.id || null,
    universityEmail: user.universityEmail,
    universityId: user.universityId,
    website: user.website,
  };

  const writeResult = await db
    .collection(User.collectionName)
    .doc(user.id)
    .set(userData)
    .catch((e) => {
      return `${e}`;
    });

  if (typeof writeResult === 'string') {
    // Error writing to Firestore
    return writeResult;
  }

  console.log('Adding private doc to user...');
  const applicationData: MembershipApplicationDbModel = {
    whyJoin: '',
    graduationYear: null,
    projectId: null,
    opportunityId: null,
  };

  await admin.firestore().collection(User.collectionName).doc(user.id).collection('private').doc(MembershipApplication.docName).set(applicationData);

  console.log('Done!');
  return null;
});

async function fetchInactiveUsers(daysAgo: number) {
  const now = new Date();
  const sixMonthsAgo = new Date(now);
  sixMonthsAgo.setDate(now.getDate() - daysAgo);

  console.log(`Fetching users who have been inactive for over ${daysAgo} days...`);
  const snapshot = await admin.firestore().collection(User.collectionName).where('lastOnline', '<', sixMonthsAgo).get();
  console.log(`Found ${snapshot.size} users.`);

  return snapshot.docs;
}

export const sendInactiveProfileReminders = onSchedule(
  {
    schedule: 'every day 18:00',
  },
  async () => {
    initSentryForFunction('sendInactiveProfileReminders');

    // Fetch users who haven't been online for over 5 months
    const inactiveUsers = await fetchInactiveUsers(30 * 5);

    // Send reminder email to each user
    const promises = [];
    for (const user of inactiveUsers.map((d) => d.data() as UserDbModelSafe)) {
      promises.push(
        sendEmail(
          user.email,
          new InactiveProfileReminderEmailTemplate({
            username: user.name,
          }),
        ).catch(console.error),
      );
    }

    console.log(`Emailing ${inactiveUsers.length} users inactive profile reminders...`);
    await Promise.all(promises);
    console.log('Done!');
  },
);

export const hideInactiveProfiles = onSchedule(
  {
    schedule: 'every day 18:00',
  },
  async () => {
    initSentryForFunction('hideInactiveProfiles');

    // Fetch users who haven't been online for over 6 months
    const inactiveUsers = await fetchInactiveUsers(30 * 6);

    // Hide profile of inactive users
    const promises = [];
    for (const user of inactiveUsers) {
      promises.push(user.ref.update({hideProfile: true} as Partial<UserDbModelSafe>));
    }

    // Hide associated projects
    for (const user of inactiveUsers) {
      const userDoc = user.data() as UserDbModelSafe;
      promises.push(...userDoc.projectIds.map((projectId) => admin.firestore().collection(Project.collectionName).doc(projectId).update({hidden: true})));
    }

    console.log(`Hiding ${inactiveUsers.length} inactive users...`);
    await Promise.all(promises);
    console.log('Done!');
  },
);
