import {onInit, setGlobalOptions} from 'firebase-functions/v2';
import * as admin from 'firebase-admin';
import * as Sentry from '@sentry/node';

const isProduction = !process.env.FIRESTORE_EMULATOR_HOST;

onInit(async () => {
  Sentry.init({
    dsn: 'https://<EMAIL>/4508771968417872',
    environment: 'functions',
    tracesSampleRate: isProduction ? 0.1 : 1.0,
    enabled: isProduction,
  });

  Sentry.setTags({
    codebase: 'functions',
  });
});

if (admin.apps.length === 0) {
  admin.initializeApp();
  setGlobalOptions({region: 'europe-west2', memory: '2GiB'});
}

export * from './alumni';
export * from './discussion';
export * from './discussionComment';
export * from './discussionPost';
export * from './mail';
export * from './maintenanceMode';
export * from './mentor';
export * from './migration';
export * from './misc';
export * from './opportunity';
export * from './opportunityApplication';
export * from './project';
export * from './stripe';
export * from './university';
export * from './user';
