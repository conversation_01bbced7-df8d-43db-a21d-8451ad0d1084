import Button from '@mui/joy/Button';
import ButtonGroup from '@mui/joy/ButtonGroup';
import {ModalHeader, ModalShell} from '@creator-campus/common-components';
import {useState} from 'react';
import {User} from '@creator-campus/common';
import {updateDoc} from 'firebase/firestore';

interface Props {
  user: User;
  onConfirm: () => void;
  onCancel: () => void;
}

export function FounderWithoutStartupWarningModal({user, onConfirm, onCancel}: Props) {
  const [loading, setLoading] = useState<boolean>(false);

  return (
    <ModalShell onClose={onCancel}>
      <ModalHeader
        title='Founder without startup!'
        subtitle="If you skip this step, your Founder role will be temporarily set to Supporter until you upload your startup. Don't worry, you can still submit your application and upload your startup later."
      />

      <ButtonGroup
        spacing={1}
        sx={{mt: 2}}
      >
        <Button
          disabled={loading}
          variant='solid'
          color='primary'
          onClick={async () => {
            setLoading(true);
            await updateDoc(user._doc, {founder: false});
            onConfirm();
          }}
        >
          Confirm
        </Button>
        <Button
          variant='outlined'
          color='neutral'
          onClick={onCancel}
        >
          Cancel
        </Button>
      </ButtonGroup>
    </ModalShell>
  );
}
