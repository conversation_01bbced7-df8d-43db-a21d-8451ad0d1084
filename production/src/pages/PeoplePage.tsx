import {OnboardingPathway, StaffRole, UserHit} from '@creator-campus/common';
import {AlgoliaFilters, AlgoliaIndexExplorer, LoadingIndicator, PageHeader, PageShell, UpdateAlgoliaFilters, useActivateCommunityString, usePeopleString, useUser} from '@creator-campus/common-components';
import PeopleFilterOptions from '../components/PeopleFilterOptions.tsx';
import {Box, ButtonGroup, Grid, Stack} from '@mui/joy';
import ProfileCard from '../components/ProfileCard.tsx';
import Button from '@mui/joy/Button';
import {Check} from '@mui/icons-material';
import {BlurryOverlay} from '../components/BlurryOverlay.tsx';
import {useNavigate, useSearchParams} from 'react-router-dom';

export default function PeoplePage() {
  const {user, university} = useUser();
  const strings = useActivateCommunityString();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  function getButtonFilters(filters: AlgoliaFilters, onFilterChange: UpdateAlgoliaFilters, sx: object) {
    return OnboardingPathway.values().map((pathway) => {
      let isFiltering = false;
      switch (pathway) {
        case OnboardingPathway.TALENT:
          isFiltering = filters['openToWork'] === ':true';
          break;
        case OnboardingPathway.FOUNDER:
          isFiltering = filters['founder'] === ':true';
          break;
        case OnboardingPathway.SUPPORTER:
          isFiltering = filters['openToWork'] === ':false' && filters['founder'] === ':false';
          break;
      }

      return (
        <Button
          key={pathway.id}
          color={isFiltering ? 'primary' : 'neutral'}
          variant={isFiltering ? 'solid' : 'soft'}
          onClick={() => {
            if (isFiltering) {
              onFilterChange('openToWork', null);
              onFilterChange('founder', null);
              return;
            }

            switch (pathway) {
              case OnboardingPathway.TALENT:
                onFilterChange('openToWork', ':true');
                onFilterChange('founder', null);
                break;
              case OnboardingPathway.FOUNDER:
                onFilterChange('openToWork', null);
                onFilterChange('founder', ':true');
                break;
              case OnboardingPathway.SUPPORTER:
                onFilterChange('openToWork', ':false');
                onFilterChange('founder', ':false');
                break;
            }
          }}
          sx={{gap: 1, width: `${Math.floor(100 / OnboardingPathway.values().length)}%`, ...sx}}
        >
          {<pathway.icon />}
          {pathway.labelPlural}
          {isFiltering && (
            <Box sx={{position: 'absolute', right: 12, top: 8, display: {xs: 'none', sm: 'block'}}}>
              <Check />
            </Box>
          )}
        </Button>
      );
    });
  }

  return (
    <PageShell banners={{profileIncomplete: {hideChildrenIfShown: false}, activateCommunity: {hideChildrenIfShown: false}}}>
      <PageHeader
        title={usePeopleString().header}
        subtitle={usePeopleString().subheading}
      />

      {!user || !university ? (
        <LoadingIndicator />
      ) : (
        <AlgoliaIndexExplorer
          index={'people'}
          initialFilters={{
            profileCompleted: ':true',
            hideProfile: ':false',
            applicationStatus: ':accepted',
            fake: ':false',
            // For regular users from partner universities, 'my university' is the default filter
            // For pre-activation owners, 'my university' is also the default filter (since they are
            // getting a sneak peek at the partner experience).
            // For non-staff users from non-partner universities, there are no university filters,
            // so show the global view by default.
            ...(searchParams.get('tab') !== 'global' && (university.partner || user.staffRole === StaffRole.OWNER) ? {universityId: `:"${university.id}"`} : {}),
            // Isolate Oxford
            ...(user.universityId === 'University of Oxford' ? {universityId: `:"University of Oxford"`} : {'NOT universityId': ':"University of Oxford"'}),
          }}
          filterPanel={(filters, onFilterChange) => (
            <PeopleFilterOptions
              user={user}
              university={university}
              filters={filters}
              onFilterChange={onFilterChange}
            />
          )}
          buttonFilters={(filters, onFilterChange) => (
            <Box
              mt={1}
              width={'100%'}
            >
              <Stack
                direction={'row'}
                sx={{
                  display: {xs: 'none', sm: 'flex'},
                  justifyContent: 'center',
                  gap: {sm: 2, lg: 4, xl: 5},
                }}
              >
                {getButtonFilters(filters, onFilterChange, {px: 2.5, width: '100%'})}
              </Stack>
              <ButtonGroup
                sx={{
                  display: {xs: 'block', sm: 'none'},
                }}
              >
                {getButtonFilters(filters, onFilterChange, {px: 1.5})}
              </ButtonGroup>
            </Box>
          )}
          placeholderPrefix={"I'm looking for"}
          searchPlaceholders={['a robotics engineer', 'a digital artist', 'a freelance designer', 'a data scientist', 'a full-stack developer', 'an ai engineer', 'a social entrepreneur', 'a cybersecurity expert', 'a sustainability expert', 'an illustrator', 'a graphic designer']}
          placeholderOffset={152}
          contentOverlay={(filters, content) =>
            user.staffRole === StaffRole.OWNER && !university.partner && filters['universityId'] ? (
              <BlurryOverlay
                subtitle={strings.overlay.people_page_subtitle}
                button={'activate'}
              >
                {content}
              </BlurryOverlay>
            ) : !user?.profileCompleted ? (
              <BlurryOverlay
                title={'Complete your profile'}
                subtitle={'Complete your profile to connect with entrepreneurs at your university.'}
                button={{text: 'Edit profile', action: () => navigate('/profile')}}
              >
                {content}
              </BlurryOverlay>
            ) : user.applicationStatus === 'submitted' ? (
              <BlurryOverlay
                title={'Your application is being reviewed'}
                subtitle={'You can browse profiles once we accept your application.'}
              >
                {content}
              </BlurryOverlay>
            ) : user.applicationStatus === 'rejected' ? (
              <BlurryOverlay title={'Your application was unsuccessful'}>{content}</BlurryOverlay>
            ) : null
          }
          itemBuilder={(hit, _i, filters) => {
            const userHit = UserHit.fromAlgoliaHit(hit);

            return (
              <Grid
                xs={12}
                sm={6}
                md={6}
                lg={6}
                xl={4}
                key={userHit.id}
                sx={{mb: 2}}
              >
                <ProfileCard
                  key={userHit.id}
                  me={user}
                  user={userHit}
                  withFeatureButton={true}
                  withStaffChip={!!filters['universityId']}
                />
              </Grid>
            );
          }}
        />
      )}
    </PageShell>
  );
}
