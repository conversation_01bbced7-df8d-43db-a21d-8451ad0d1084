import Chip from '@mui/joy/Chip';
import {AlumniApplication} from '@creator-campus/common';
import {useEffect, useState} from 'react';

interface Props {
  universityId: string;
}

export function AlumniCountChip({universityId}: Props) {
  const [numAlumniApplications, setNumAlumniApplications] = useState<number | null>(null);

  useEffect(() => {
    AlumniApplication.getCount(universityId).then((count) => {
      setNumAlumniApplications(count);
    });
  }, []);

  if (numAlumniApplications === null || numAlumniApplications === 0) {
    return <></>;
  }

  return <Chip color='warning'>{numAlumniApplications}</Chip>;
}
