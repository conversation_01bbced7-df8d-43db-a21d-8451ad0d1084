import {type EmailTemplate} from '../model/emailTemplate/emailTemplate';
import {addDoc, collection} from 'firebase/firestore';
import {firestore} from '../firebase';
import {logger} from '../logger';

export function getEmailContent(recipient: string, template: EmailTemplate) {
  return {
    ...(recipient.includes('@') ? {to: recipient} : {toUids: [recipient]}),
    from: 'Creator Campus <<EMAIL>>',
    template: {
      name: template.template,
      data: template.data,
      attachments: template.attachments || [],
    },
  };
}

export async function sendEmail(recipient: string, template: EmailTemplate) {
  await addDoc(collection(firestore(), 'mail'), getEmailContent(recipient, template));
  logger.debug(`Sent ${template.template} email to ${recipient} with data:`, template.data);
}

export function getDomainFromEmail(email: string): string {
  const parts = email.split('@');
  if (parts.length === 2) {
    return parts[1]!;
  } else {
    return email;
  }
}
