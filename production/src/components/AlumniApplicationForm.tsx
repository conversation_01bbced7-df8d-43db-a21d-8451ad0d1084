import {FormEvent, useEffect, useState} from 'react';
import Button from '@mui/joy/Button';
import FormControl from '@mui/joy/FormControl';
import FormLabel from '@mui/joy/FormLabel';
import Input from '@mui/joy/Input';
import Typography from '@mui/joy/Typography';
import Stack from '@mui/joy/Stack';
import {firestore, functions, University, UniversityConverter, usingEmulators} from '@creator-campus/common';
import {LoadingIndicator, useSnackbar} from '@creator-campus/common-components';
import {EMAIL_INPUT_SLOT_PROPS} from '../constants/styles.ts';
import {httpsCallable} from 'firebase/functions';
import {TermsAndConditionsCheckbox} from './TermsAndConditionsCheckbox.tsx';
import {collection, getDocs, query, where} from 'firebase/firestore';
import Autocomplete from '@mui/joy/Autocomplete';
import {AlumniApplicationSubmittedModal} from './modals/AlumniApplicationSubmittedModal.tsx';
import {useNavigate} from 'react-router-dom';

interface FormElements extends HTMLFormControlsCollection {
  name: HTMLInputElement;
  email: HTMLInputElement;
  studentNumber: HTMLInputElement;
  graduationYear: HTMLInputElement;
}

interface ApplicationFormElement extends HTMLFormElement {
  readonly elements: FormElements;
}

interface AlumniApplication {
  name: string;
  email: string;
  universityId: string;
  studentNumber: string;
  graduationYear: string;
}

export default function AlumniApplicationForm() {
  const [universities, setUniversities] = useState<University[]>([]);
  const [university, setUniversity] = useState<University | null>(null);
  const [loading, setLoading] = useState(false);
  const [showSubmissionModal, setShowSubmissionModal] = useState(false);

  const {showSnackbar, showErrorSnackbar} = useSnackbar();
  const navigate = useNavigate();

  useEffect(() => {
    const q = query(collection(firestore(), University.collectionName), where('selfManagedAlumniApplications', '==', true)).withConverter(new UniversityConverter());

    getDocs(q).then((snapshot) => {
      setUniversities(snapshot.docs.map((d) => d.data()));
    });
  }, []);

  function checkFormValid(graduationYear: string) {
    const graduationYearNumber = parseInt(graduationYear);
    if (isNaN(graduationYearNumber)) {
      showSnackbar('Graduation year is not a number.', 'danger');
      return false;
    }

    const currentYear = new Date().getFullYear();
    if (graduationYearNumber < 1900 || graduationYearNumber > currentYear) {
      showSnackbar(`Graduation year must be between 1900 and ${currentYear}.`, 'danger');
      return false;
    }

    return true;
  }

  interface ApplicationSubmissionResponse {
    success: boolean;
    message: string;
  }

  async function submitApplication(application: AlumniApplication) {
    const response = await httpsCallable(
      functions(),
      'submitAlumniApplication',
    )({
      ...application,
      usingEmulators: usingEmulators,
    });
    const data = response.data as ApplicationSubmissionResponse;
    showSnackbar(data.message, data.success ? 'success' : 'danger');
    setShowSubmissionModal(data.success);
  }

  return (
    <Stack
      gap={4}
      sx={{mt: 2}}
    >
      {showSubmissionModal && (
        <AlumniApplicationSubmittedModal
          university={university!}
          onClose={() => {
            setShowSubmissionModal(false);
            navigate('/login');
          }}
        />
      )}
      <form
        onSubmit={async (event: FormEvent<ApplicationFormElement>) => {
          event.preventDefault();

          if (!university) {
            showSnackbar('Please select a university from the dropdown.', 'danger');
            return;
          }

          const formElements = event.currentTarget.elements;
          const application: AlumniApplication = {
            name: formElements.name.value,
            email: formElements.email.value,
            universityId: university.id,
            studentNumber: formElements.studentNumber.value,
            graduationYear: formElements.graduationYear.value,
          };

          if (checkFormValid(application.graduationYear)) {
            setLoading(true);
            await submitApplication(application).catch((e) => showErrorSnackbar(e, 'Sorry, something went wrong.'));
            setLoading(false);
          }
        }}
      >
        <FormControl required>
          <FormLabel>Full name</FormLabel>
          <Input
            name='name'
            autoComplete='name'
          />
        </FormControl>
        <FormControl required>
          <FormLabel>Personal email</FormLabel>
          <Input
            type='email'
            name='email'
            autoComplete='email'
            slotProps={EMAIL_INPUT_SLOT_PROPS}
          />
        </FormControl>
        <FormControl required>
          <FormLabel>University</FormLabel>
          <Autocomplete
            required
            getOptionLabel={(u) => u.name}
            isOptionEqualToValue={(u1, u2) => u1.id === u2.id}
            options={universities}
            sx={{width: '250px', zIndex: 100}}
            value={university}
            placeholder='Select university...'
            size='sm'
            onChange={(_, value) => {
              if (value) {
                setUniversity(value);
              }
            }}
          />
        </FormControl>
        <FormControl required>
          <FormLabel>Student ID</FormLabel>
          <Input name='studentNumber' />
        </FormControl>
        <FormControl required>
          <FormLabel>Graduation Year</FormLabel>
          <Input
            name='graduationYear'
            slotProps={{input: {maxLength: 4}}}
          />
        </FormControl>
        <Stack
          gap={3}
          sx={{mt: 1}}
        >
          <TermsAndConditionsCheckbox />
          <Button
            disabled={loading}
            type='submit'
            fullWidth
          >
            {loading ? <LoadingIndicator size='sm' /> : 'Submit'}
          </Button>
        </Stack>
        <Typography
          level='body-xs'
          textAlign='center'
        >
          You will receive an email when a university admin makes a decision on your application.
        </Typography>
      </form>
    </Stack>
  );
}
