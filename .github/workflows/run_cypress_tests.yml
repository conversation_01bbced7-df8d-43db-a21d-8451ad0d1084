# NOTE: This workflow is currently broken. There are issues when
# this workflow tries to authenticate with Firebase and run the
# emulators.
name: Run Cypress tests

on:
  push:
    paths:
      - '.github/workflows/run_cypress_tests.yml' # TODO: Remove this when the workflow is working

jobs:
  cypress-run:
    runs-on: ubuntu-latest
    # Runs tests in parallel with matrix strategy https://docs.cypress.io/guides/guides/parallelization
    # https://docs.github.com/en/actions/using-jobs/using-a-matrix-for-your-jobs
    # Also see warning here https://github.com/cypress-io/github-action#parallel
    strategy:
      fail-fast: false # https://github.com/cypress-io/github-action/issues/48
      matrix:
        containers: [1, 2] # Uses 2 parallel instances
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Yarn install
        run: yarn install

      - name: Install firebase-tools
        run: npm install -g firebase-tools

      - name: Authenticate Firebase CLI
        run: |
          echo '${{ secrets.FIREBASE_SERVICE_ACCOUNT_CREATOR_CAMPUS_APP }}' > $HOME/firebase-key.json
          export GOOGLE_APPLICATION_CREDENTIALS="$HOME/firebase-key.json"

      - name: Run Cypress tests
        # Uses the Cypress GitHub action https://github.com/cypress-io/github-action
        uses: cypress-io/github-action@v6
        with:
          start: npm run cypress
          wait-on: 'http://localhost:5173' # Waits for above
          record: true
        env:
          CYPRESS_RECORD_KEY: ${{ secrets.CYPRESS_RECORD_KEY }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          GOOGLE_APPLICATION_CREDENTIALS: ${{ secrets.FIREBASE_SERVICE_ACCOUNT_CREATOR_CAMPUS_APP }}
          FIREBASE_TOKEN: ${{ secrets.FIREBASE_SERVICE_ACCOUNT_CREATOR_CAMPUS_APP }}
