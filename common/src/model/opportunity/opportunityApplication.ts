import {doc, DocumentReference, FieldValue, type FirestoreDataConverter, QueryDocumentSnapshot, setDoc, type SnapshotOptions, Timestamp, updateDoc, type WithFieldValue} from 'firebase/firestore';
import {firestore} from '../../firebase';
import {Opportunity, OpportunityConverter, type OpportunityDbModel} from './opportunity';
import {User} from '../user/user';
import {generateRandomId} from '../../utils/misc';

export class OpportunityApplication {
  static readonly collectionName = 'opportunityApplications';

  readonly _doc: DocumentReference<OpportunityApplication, OpportunityApplicationDbModel>;
  readonly _oppDoc: DocumentReference<Opportunity, OpportunityDbModel>;

  constructor(
    readonly id: string,
    readonly applicantEmail: string,
    readonly applicantId: string,
    readonly applicantName: string,
    readonly applicantUniversityId: string,
    readonly cvDownloadUrl: string | null,
    readonly dateAccepted: Date | null,
    readonly dateApplied: Date,
    readonly message: string,
    readonly opportunityId: string,
    readonly opportunityUniversityId: string,
    readonly projectId: string,
    readonly projectOwnerId: string,
  ) {
    this._doc = OpportunityApplication.doc(id).withConverter<OpportunityApplication, OpportunityApplicationDbModel>(new OpportunityApplicationConverter());
    this._oppDoc = Opportunity.doc(opportunityId).withConverter<Opportunity, OpportunityDbModel>(new OpportunityConverter());
  }

  public static doc(id: string) {
    return doc(firestore(), OpportunityApplication.collectionName, id).withConverter(new OpportunityApplicationConverter());
  }

  public static async create(applicant: User, message: string, oppId: string, projectId: string, projectOwnerId: string, oppUniId: string, cvDownloadUrl: string | null) {
    const id = generateRandomId();
    const application = new OpportunityApplication(id, applicant.email, applicant.id, applicant.name, applicant.universityId, cvDownloadUrl, null, new Date(), message, oppId, oppUniId, projectId, projectOwnerId);

    await setDoc(OpportunityApplication.doc(id), application);

    return application;
  }

  public async accept() {
    const fillDate = new Date();

    await updateDoc(this._doc, {
      dateAccepted: fillDate,
    });

    await updateDoc(this._oppDoc, {
      fillDate: fillDate,
    });
  }
}

export interface OpportunityApplicationDbModelNoDates {
  applicantEmail: string;
  applicantId: string;
  applicantName: string;
  applicantUniversityId: string;
  cvDownloadUrl: string | null;
  message: string;
  opportunityId: string;
  opportunityUniversityId: string;
  projectId: string;
  projectOwnerId: string;
}

export interface OpportunityApplicationDbModelSafe extends OpportunityApplicationDbModelNoDates {
  dateAccepted: any | null;
  dateApplied: any;
}

export interface OpportunityApplicationDbModel extends OpportunityApplicationDbModelNoDates {
  dateAccepted: Timestamp | null;
  dateApplied: Timestamp;
}

export class OpportunityApplicationConverter implements FirestoreDataConverter<OpportunityApplication, OpportunityApplicationDbModel> {
  toFirestore(app: WithFieldValue<OpportunityApplication>): WithFieldValue<OpportunityApplicationDbModel> {
    return {
      applicantEmail: app.applicantEmail,
      applicantId: app.applicantId,
      applicantName: app.applicantName,
      applicantUniversityId: app.applicantUniversityId,
      cvDownloadUrl: app.cvDownloadUrl,
      dateAccepted: app.dateAccepted === null ? null : app.dateAccepted instanceof FieldValue ? app.dateAccepted : Timestamp.fromDate(app.dateAccepted as Date),
      dateApplied: app.dateApplied instanceof FieldValue ? app.dateApplied : Timestamp.fromDate(app.dateApplied as Date),
      message: app.message,
      opportunityId: app.opportunityId,
      opportunityUniversityId: app.opportunityUniversityId,
      projectId: app.projectId,
      projectOwnerId: app.projectOwnerId,
    };
  }

  fromFirestore(snapshot: QueryDocumentSnapshot, options: SnapshotOptions): OpportunityApplication {
    const data = snapshot.data(options) as OpportunityApplicationDbModel;
    return new OpportunityApplication(
      snapshot.id,
      data.applicantEmail,
      data.applicantId,
      data.applicantName,
      data.applicantUniversityId,
      data.cvDownloadUrl,
      data.dateAccepted?.toDate() || null,
      data.dateApplied.toDate(),
      data.message,
      data.opportunityId,
      data.opportunityUniversityId,
      data.projectId,
      data.projectOwnerId,
    );
  }
}
