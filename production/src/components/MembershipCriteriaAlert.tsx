import Typography from '@mui/joy/Typography';
import Link from '@mui/joy/Link';
import {Alert} from '@mui/joy';
import {useState} from 'react';
import {MembershipCriteriaModal} from './modals/MembershipCriteriaModal.tsx';

export function MembershipCriteriaAlert() {
  const [modalOpen, setModalOpen] = useState<boolean>(false);

  return (
    <>
      {modalOpen && <MembershipCriteriaModal onClose={() => setModalOpen(false)} />}
      <Alert>
        <Typography level='title-sm'>
          Do you meet our{' '}
          <Link
            onClick={() => {
              setModalOpen(true);
            }}
          >
            membership criteria?
          </Link>
        </Typography>
      </Alert>
    </>
  );
}
