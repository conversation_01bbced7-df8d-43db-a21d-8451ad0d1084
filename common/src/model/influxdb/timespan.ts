const dateFormat = new Intl.DateTimeFormat('en-GB', {
  day: '2-digit',
  month: 'short',
  year: 'numeric',
  timeZone: 'UTC',
});

const timeFormat = new Intl.DateTimeFormat('en-GB', {
  hour: '2-digit',
  minute: '2-digit',
  hour12: false,
});

export class Timespan {
  // static readonly LAST_HOUR = new Timespan('last hour', 3600, '10m', timeFormat);
  static readonly LAST_DAY = new Timespan('last 24 hours', 86400, '2h', timeFormat);
  static readonly LAST_WEEK = new Timespan('last week', 604800 + 86400, '12h', dateFormat);
  static readonly LAST_2_WEEKS = new Timespan('last 2 weeks', 2 * 604800 + 86400, '1d', dateFormat);
  static readonly LAST_MONTH = new Timespan('last month', 4 * 604800 + 86400, '2d', dateFormat);

  private constructor(
    readonly label: string,
    readonly secsDuration: number,
    readonly step: string,
    readonly formatter: Intl.DateTimeFormat,
  ) {}

  toString() {
    return this.label;
  }

  static values(): Timespan[] {
    return Object.values(Timespan);
  }
}
