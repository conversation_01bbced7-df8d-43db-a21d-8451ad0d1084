import {onDocumentCreated, onDocumentDeleted, onDocumentUpdated} from 'firebase-functions/v2/firestore';
import {DocumentReference, FieldValue} from 'firebase-admin/firestore';
import * as admin from 'firebase-admin';
import {DiscussionComment, DiscussionCommentDbModelSafe, DiscussionPostBase, DiscussionPostDbModelSafe, DiscussionPostReplyEmailTemplate, University, User, UserDbModelSafe} from '@creator-campus/common';
import {deletePostAttachments, movePendingAttachments, sendEmail} from './utils';

async function processCreatedComment(comment: DiscussionCommentDbModelSafe, commentId: string, postId: string, universityId?: string) {
  const discussionDoc = universityId ? admin.firestore().collection(University.collectionName).doc(universityId) : admin.firestore().collection('global').doc('discussion');
  const postDoc = discussionDoc.collection('discussion').doc(postId);
  await postDoc.update({
    numComments: FieldValue.increment(1),
  });

  // Check if we should notify the author of the post that a comment was made
  const postSnapshot = await postDoc.get();
  const post = postSnapshot.data() as DiscussionPostDbModelSafe;
  const postAuthorSnapshot = await admin.firestore().collection(User.collectionName).doc(post.authorId).get();
  const postAuthor = postAuthorSnapshot.data() as UserDbModelSafe;

  if (postAuthor.notifications.discussionPostReplies === 'realTime' && comment.authorId !== post.authorId) {
    const commenterSnapshot = await admin.firestore().collection(User.collectionName).doc(comment.authorId).get();
    const commenter = commenterSnapshot.data() as UserDbModelSafe;

    await sendEmail(
      postAuthor.email,
      new DiscussionPostReplyEmailTemplate({
        commentText: comment.text,
        commenterName: commenter.name,
        postCommentsLink: `https://app.creatorcampus.io/discussion/post/${universityId || 'global'}/${postId}`,
      }),
    ).catch(console.error);
  }

  // Move any pending attachments from temporary storage to the created comment's storage folder
  const originFolder = DiscussionPostBase.getPendingUploadsFolder(universityId);
  const destinationFolder = DiscussionComment.getUploadsFolder(postId, commentId, universityId);
  await movePendingAttachments(comment, originFolder, destinationFolder);
}

export const onUniDiscussionCommentCreated = onDocumentCreated('universities/{universityId}/discussion/{postId}/comments/{commentId}', async (event) => {
  if (!event.data) {
    console.error('Event data was undefined!');
    return;
  }

  const comment = event.data.data() as DiscussionCommentDbModelSafe;
  const commentId = event.params.commentId;
  const postId = event.params.postId;
  const universityId = event.params.universityId;

  await processCreatedComment(comment, commentId, postId, universityId);
});

export const onGlobalDiscussionCommentCreated = onDocumentCreated('global/discussion/discussion/{postId}/comments/{commentId}', async (event) => {
  if (!event.data) {
    console.error('Event data was undefined!');
    return;
  }

  const comment = event.data.data() as DiscussionCommentDbModelSafe;
  const commentId = event.params.commentId;
  const postId = event.params.postId;

  await processCreatedComment(comment, commentId, postId);
});

async function processUpdatedComment(comment: DiscussionCommentDbModelSafe, commentRef: DocumentReference) {
  const upVotesBefore = comment.numUpVotes;
  const upVotesAfter = comment.upVotes.length;

  if (upVotesBefore !== upVotesAfter) {
    // Update author's karma
    await admin
      .firestore()
      .collection(User.collectionName)
      .doc(comment.authorId)
      .update({
        karma: FieldValue.increment(upVotesAfter - upVotesBefore),
      });

    // Update comment's numLikes field
    return commentRef.update({
      numUpVotes: upVotesAfter,
    });
  }

  return null;
}

export const onUniDiscussionCommentUpdated = onDocumentUpdated('universities/{universityId}/discussion/{postId}/comments/{commentId}', async (event) => {
  if (!event.data) {
    console.error('Event data was undefined!');
    return;
  }

  const comment = event.data.after.data() as DiscussionCommentDbModelSafe;
  return await processUpdatedComment(comment, event.data.after.ref);
});

export const onGlobalDiscussionCommentUpdated = onDocumentUpdated('global/discussion/discussion/{postId}/comments/{commentId}', async (event) => {
  if (!event.data) {
    console.error('Event data was undefined!');
    return;
  }

  const comment = event.data.after.data() as DiscussionCommentDbModelSafe;
  return await processUpdatedComment(comment, event.data.after.ref);
});

async function processDeletedComment(comment: DiscussionCommentDbModelSafe, postId: string, commentId: string, universityId?: string) {
  const discussionDoc = universityId ? admin.firestore().collection(University.collectionName).doc(universityId) : admin.firestore().collection('global').doc('discussion');

  await discussionDoc
    .collection('discussion')
    .doc(postId)
    .update({
      numComments: FieldValue.increment(-1),
    });

  const storageFolder = DiscussionComment.getUploadsFolder(postId, commentId, universityId);
  await deletePostAttachments(comment, postId, storageFolder);
}

export const onUniDiscussionCommentDeleted = onDocumentDeleted('universities/{universityId}/discussion/{postId}/comments/{commentId}', async (event) => {
  if (!event.data) {
    console.error('Event data was undefined!');
    return;
  }

  const comment = event.data.data() as DiscussionCommentDbModelSafe;
  const commentId = event.params.commentId;
  const postId = event.params.postId;
  const universityId = event.params.universityId;

  await processDeletedComment(comment, postId, commentId, universityId);
});

export const onGlobalDiscussionCommentDeleted = onDocumentDeleted('global/discussion/discussion/{postId}/comments/{commentId}', async (event) => {
  if (!event.data) {
    console.error('Event data was undefined!');
    return;
  }

  const comment = event.data.data() as DiscussionCommentDbModelSafe;
  const commentId = event.params.commentId;
  const postId = event.params.postId;

  await processDeletedComment(comment, postId, commentId);
});
