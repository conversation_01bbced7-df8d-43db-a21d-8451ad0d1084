import {arrayRemove, arrayUnion, CollectionReference, DocumentReference, Timestamp, updateDoc} from 'firebase/firestore';
import {User} from '../user/user.js';
import {DiscussionAttachment} from './discussionAttachment.js';

export abstract class DiscussionPostBase {
  protected constructor(
    readonly id: string,
    readonly attachments: DiscussionAttachment[],
    readonly authorId: string,
    readonly datePosted: Date,
    readonly text: string,
    readonly universityId: string | null,
    readonly upVotes: string[],
  ) {}

  public abstract doc(): DocumentReference<DiscussionPostBase, DiscussionPostBaseDbModel>;

  public abstract collection(): CollectionReference<DiscussionPostBase, DiscussionPostBaseDbModel>;

  public abstract refresh(): Promise<DiscussionPostBase>;

  public abstract getUploadsFolder(): string;

  public async upVote(user: User) {
    await updateDoc(this.doc(), {
      upVotes: this.upVotes.includes(user.id) ? arrayRemove(user.id) : arrayUnion(user.id),
    });
  }

  public static getPendingUploadsFolder(universityId?: string) {
    if (universityId) {
      return `universities/${universityId}/discussion/pending-uploads`;
    }

    return 'global/discussion/pending-uploads';
  }
}

export interface DiscussionPostBaseDbModelNoDates {
  attachments: DiscussionAttachment[];
  authorId: string;
  numUpVotes: number;
  text: string;
  upVotes: string[];
}

export interface DiscussionPostBaseDbModel extends DiscussionPostBaseDbModelNoDates {
  datePosted: Timestamp;
}

export interface DiscussionPostBaseDbModelSafe extends DiscussionPostBaseDbModelNoDates {
  datePosted: any;
}
