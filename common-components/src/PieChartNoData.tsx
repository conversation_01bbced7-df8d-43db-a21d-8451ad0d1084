import {<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>} from 'recharts';
import Box from '@mui/joy/Box';
import Typography from '@mui/joy/Typography';

interface Props {
  size: {width: number; height: number};
  margin: {top: number; right: number; bottom: number; left: number};
  outerRadius: number;
}

export function PieChartNoData({size, margin, outerRadius}: Props) {
  const data = [
    {name: 'a', value: 3},
    {name: 'b', value: 5},
    {name: 'c', value: 8},
  ];

  return (
    <Box
      sx={{
        position: 'relative',
        display: 'inline-block',
        width: size.width,
        height: size.height,
      }}
    >
      <PieChart
        width={size.width}
        height={size.height}
        margin={margin}
      >
        <Pie
          data={data}
          dataKey='value'
          nameKey='name'
          cx='50%'
          cy='50%'
          outerRadius={outerRadius}
          labelLine={false}
          opacity={0.2}
        >
          {data.map((entry, i) => {
            const shade = (255 * (i + 2)) / (data.length + 4);
            return (
              <Cell
                key={`cell-${entry.name}`}
                fill={`rgb(${shade}, ${shade}, ${shade})`}
              />
            );
          })}
        </Pie>
      </PieChart>
      {/* Centered text */}
      <Typography
        sx={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          fontWeight: 'bold',
        }}
      >
        No data yet.
      </Typography>
    </Box>
  );
}
