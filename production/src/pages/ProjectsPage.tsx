import MyProjects from '../components/MyProjects';
import {Project, StaffRole} from '@creator-campus/common';
import {LoadingIndicator, PageShell, useActivateCommunityString, useUser, AlgoliaIndexExplorer} from '@creator-campus/common-components';
import ProjectsHeader from '../components/ProjectsHeader.tsx';
import ProjectFilterOptions from '../components/ProjectFilterOptions.tsx';
import ProjectCard from '../components/ProjectCard.tsx';
import {Alert, Box, Button} from '@mui/joy';
import {AddBoxRounded} from '@mui/icons-material';
import {useNavigate, useSearchParams} from 'react-router-dom';
import Stack from '@mui/joy/Stack';
import Tooltip from '@mui/joy/Tooltip';
import {BlurryOverlay} from '../components/BlurryOverlay.tsx';

interface Props {
  tab: 'explore' | 'my-startups';
}

export default function ProjectsPage({tab}: Props) {
  const navigate = useNavigate();
  const {user, university} = useUser();
  const strings = useActivateCommunityString();
  const [searchParams] = useSearchParams();

  return (
    <PageShell banners={{profileIncomplete: {hideChildrenIfShown: false}, activateCommunity: {hideChildrenIfShown: false}}}>
      <ProjectsHeader selectedTab={tab} />
      {!user || !university ? (
        <LoadingIndicator />
      ) : tab === 'explore' ? (
        <AlgoliaIndexExplorer
          index={'projects'}
          filterPanel={(filters, onFilterChange) => (
            <ProjectFilterOptions
              user={user}
              university={university}
              filters={filters}
              onFilterChange={onFilterChange}
            />
          )}
          initialFilters={{
            hidden: ':false',
            fake: ':false',
            // For regular users from partner universities, 'my university' is the default filter
            // For pre-activation owners, 'my university' is also the default filter (since they are
            // getting a sneak peek at the partner experience).
            // For non-staff users from non-partner universities, there are no university filters,
            // so show the global view by default.
            ...(searchParams.get('tab') !== 'global' && (university.partner || user.staffRole === StaffRole.OWNER) ? {universityId: `:"${university.id}"`} : {}),
            // Isolate Oxford
            ...(user.universityId === 'University of Oxford' ? {universityId: `:"University of Oxford"`} : {'NOT universityId': ':"University of Oxford"'}),
          }}
          placeholderPrefix={"I'm looking for"}
          searchPlaceholders={['a carbon emissions app', 'a b2b saas platform', 'a freelance art marketplace', 'a skill share platform', 'a digital marketplace']}
          placeholderOffset={152}
          contentOverlay={(filters, content) =>
            user.staffRole === StaffRole.OWNER && !university.partner && filters['universityId'] ? (
              <BlurryOverlay
                subtitle={strings.overlay.startups_page_subtitle}
                button={'activate'}
              >
                {content}
              </BlurryOverlay>
            ) : !user?.profileCompleted ? (
              <BlurryOverlay
                title={'Complete your profile'}
                subtitle={'Complete your profile to browse startups at your university.'}
                button={{text: 'Edit profile', action: () => navigate('/profile')}}
              >
                {content}
              </BlurryOverlay>
            ) : user.applicationStatus === 'submitted' ? (
              <BlurryOverlay
                title={'Your application is being reviewed'}
                subtitle={'You can browse startups once we accept your application.'}
              >
                {content}
              </BlurryOverlay>
            ) : user.applicationStatus === 'rejected' ? (
              <BlurryOverlay title={'Your application was unsuccessful'}>{content}</BlurryOverlay>
            ) : null
          }
          itemBuilder={(hit, i) => {
            const project = Project.fromDbModel(hit.objectID, hit);

            return (
              <Stack
                key={project.id}
                spacing={2}
                sx={{mb: 2}}
              >
                {i === 0 && user?.projectIds.length === 0 && (
                  <Alert
                    startDecorator={<AddBoxRounded />}
                    variant='soft'
                    color='neutral'
                    endDecorator={
                      <Tooltip title={user?.hasFullAccessToApp() ? '' : 'Complete your profile to add your own startup.'}>
                        <Box>
                          <Button
                            color='primary'
                            sx={{mr: 1}}
                            disabled={!user?.hasFullAccessToApp()}
                            onClick={() => navigate('/startups/my-startups')}
                          >
                            Create startup
                          </Button>
                        </Box>
                      </Tooltip>
                    }
                  >
                    You don't have any startups yet. Create your own here!
                  </Alert>
                )}
                <Box>
                  <ProjectCard
                    projectId={project.id}
                    hit={project}
                    withFeatureButton={true}
                  />
                </Box>
              </Stack>
            );
          }}
        />
      ) : (
        <MyProjects />
      )}
    </PageShell>
  );
}
