export type StaffRoleId = 'owner' | 'admin' | 'member';

export class StaffRole {
  static readonly OWNER = new StaffRole('owner', 'Owner', 3, 'Can edit roles, manage mentors, view insights, edit branding, edit the startup showcase, and approve alumni requests.');
  static readonly ADMIN = new StaffRole('admin', 'Admin', 2, 'Can manage mentors, view insights, edit branding, edit the startup showcase, and approve alumni requests.');
  static readonly MEMBER = new StaffRole('member', 'Member', 1, 'Can view insights.');

  private constructor(
    readonly id: StaffRoleId,
    readonly label: string,
    readonly rank: number,
    readonly description: string,
  ) {}

  static fromId(id: string | null): StaffRole | null {
    return this.values().find((c) => c.id === id) || null;
  }

  static values(): StaffRole[] {
    return Object.values(StaffRole);
  }

  static baseRole() {
    return StaffRole.MEMBER;
  }

  public canViewInsights() {
    return true;
  }

  public canReviewAlumniApplications() {
    return [StaffRole.OWNER, StaffRole.ADMIN].includes(this);
  }

  public canEditBranding() {
    return [StaffRole.OWNER, StaffRole.ADMIN].includes(this);
  }

  public canEditShowcase() {
    return [StaffRole.OWNER, StaffRole.ADMIN].includes(this);
  }

  public canManageMentors() {
    return [StaffRole.OWNER, StaffRole.ADMIN].includes(this);
  }

  public canEditRoles() {
    return [StaffRole.OWNER].includes(this);
  }

  public canManageSubscription() {
    return [StaffRole.OWNER].includes(this);
  }
}
