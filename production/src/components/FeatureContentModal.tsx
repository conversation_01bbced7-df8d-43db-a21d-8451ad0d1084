import {Timestamp, updateDoc} from 'firebase/firestore';
import {Project, UserHit} from '@creator-campus/common';
import {ModalShell, ModalHeader, useSnackbar} from '@creator-campus/common-components';
import {Button, ButtonGroup} from '@mui/joy';
import {useState} from 'react';

interface Props {
  content: UserHit | Project;
  onClose: () => void;
}

export function FeatureContentModal({content, onClose}: Props) {
  const DAYS_TO_FEATURE = 7;

  const [loading, setLoading] = useState<boolean>(false);
  const {showSnackbar} = useSnackbar();

  return (
    <ModalShell onClose={onClose}>
      <ModalHeader
        title={'Feature content'}
        subtitle={content.featuredUntil ? `Stop featuring this content at the top of the page? It is currently set to stop being featured at ${new Date(content.featuredUntil).toISOString()}.` : `Feature this content at the top of the page for ${DAYS_TO_FEATURE} days?`}
      />
      <ButtonGroup
        spacing={1}
        sx={{mt: 2}}
      >
        <Button
          disabled={loading}
          variant='solid'
          color='primary'
          onClick={async () => {
            setLoading(true);

            if (content.featuredUntil) {
              //@ts-ignore
              await updateDoc(content._doc, {
                featuredUntil: null,
              });
              showSnackbar('Content unfeatured (please refresh the page).', 'success');
            } else {
              //@ts-ignore
              await updateDoc(content._doc, {
                featuredUntil: Timestamp.fromDate(new Date(Date.now() + DAYS_TO_FEATURE * 24 * 60 * 60 * 1000)),
              });
              showSnackbar('Content featured (please refresh the page).', 'success');
            }

            setLoading(false);
            onClose();
          }}
        >
          Confirm
        </Button>
        <Button
          variant='outlined'
          color='neutral'
          onClick={onClose}
        >
          Cancel
        </Button>
      </ButtonGroup>
    </ModalShell>
  );
}
