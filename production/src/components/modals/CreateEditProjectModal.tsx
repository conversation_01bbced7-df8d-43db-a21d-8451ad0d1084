import {Project} from '@creator-campus/common';
import {ModalHeader, ModalShell, useNewProjectModalString} from '@creator-campus/common-components';
import CreateEditProjectForm from '../CreateEditProjectForm.tsx';

interface Props {
  initialProject?: Project;
  onClose: () => void;
  onSave: (event: 'add' | 'edit', createdProject: Project | null) => void;
  hiddenProject?: boolean;
}

export default function CreateEditProjectModal({initialProject, onSave, onClose, hiddenProject = false}: Props) {
  return (
    <>
      <ModalShell onClose={onClose}>
        <ModalHeader
          title={initialProject ? "Update your project's details" : useNewProjectModalString().heading}
          subtitle={initialProject ? "Update your project's public-facing details to ensure you're attracting the right talent." : useNewProjectModalString().subheading}
        />
        <CreateEditProjectForm
          initialProject={initialProject}
          onClose={onClose}
          onSave={onSave}
          hiddenProject={hiddenProject}
        />
      </ModalShell>
    </>
  );
}
