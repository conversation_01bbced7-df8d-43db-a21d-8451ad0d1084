import {useProfileOnboardingString, ModalShell, ModalHeader} from '@creator-campus/common-components';
import {UpdateEmailForm} from '../UpdateEmailForm.tsx';
import {Stack} from '@mui/joy';

interface Props {
  onClose: () => void;
}

export default function UpdateEmailModal({onClose}: Props) {
  return (
    <ModalShell
      onClose={onClose}
      withCloseButton={true}
      closeOnBackgroundClick={true}
    >
      <ModalHeader
        title={useProfileOnboardingString().update_email_title}
        subtitle={useProfileOnboardingString().update_email_subtitle}
      />
      <Stack
        spacing={1}
        sx={{width: '100%', justifyItems: 'end'}}
      >
        <UpdateEmailForm
          primaryButtonText={'Done'}
          secondaryButtonText={'Cancel'}
          primaryButtonAction={onClose}
          secondaryButtonAction={onClose}
        />
      </Stack>
    </ModalShell>
  );
}
