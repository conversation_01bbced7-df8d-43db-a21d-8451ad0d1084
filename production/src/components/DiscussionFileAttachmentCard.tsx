import {LoadingIndicator} from '@creator-campus/common-components';
import Stack from '@mui/joy/Stack';
import Typography from '@mui/joy/Typography';
import {LinearProgress} from '@mui/joy';
import Card from '@mui/joy/Card';
import {DownloadableSettings, UploadableSettings} from './DiscussionAttachmentCard.tsx';
import {Clear, Download, ErrorOutline} from '@mui/icons-material';
import {useState} from 'react';

interface Props {
  uploadable?: UploadableSettings;
  downloadable?: DownloadableSettings;
  downloadInProgress: boolean;
  uploadComplete: boolean;
  uploadProgress: number | null;
  downloadAttachment: () => void;
}

export default function DiscussionFileAttachmentCard({downloadable, uploadable, downloadInProgress, uploadComplete, uploadProgress, downloadAttachment}: Props) {
  const [hovering, setHovering] = useState<boolean>(false);

  function getIcon() {
    const sx = {fontSize: 'xl'};

    if (hovering) {
      if (downloadable) {
        return <Download sx={sx} />;
      } else {
        return <Clear sx={sx} />;
      }
    }

    if (uploadProgress === null) {
      return <ErrorOutline sx={sx} />;
    }

    return <></>;
  }

  function getText() {
    if (hovering) {
      return downloadable ? 'Download' : 'Remove';
    }

    if (uploadProgress === null) {
      return 'Upload error!';
    }

    return downloadable ? downloadable.metadata.name : uploadable!.file.name;
  }

  return (
    <Card
      sx={{
        width: 200,
        p: 1,
        textAlign: 'center',
        cursor: 'pointer',
        transition: 'background-color 0.3s',
        backgroundColor: hovering ? 'rgba(246,101,32,0.3)' : 'white',
      }}
      onMouseEnter={() => setHovering(true)}
      onMouseLeave={() => setHovering(false)}
      onClick={uploadable ? uploadable.removeFile : downloadAttachment}
    >
      {/* Attachment name */}
      {downloadInProgress ? (
        <LoadingIndicator size={'sm'} />
      ) : (
        <Stack
          direction={'row'}
          spacing={0.5}
          alignItems={'center'}
          justifyContent={'center'}
        >
          {getIcon()}
          <Typography
            level='body-sm'
            noWrap
          >
            {getText()}
          </Typography>
        </Stack>
      )}

      {/* Upload progress */}
      {uploadable && (
        <LinearProgress
          determinate
          color={uploadProgress === null ? 'danger' : uploadComplete ? 'success' : 'primary'}
          value={uploadProgress === null ? 100 : uploadProgress * 100}
        />
      )}
    </Card>
  );
}
