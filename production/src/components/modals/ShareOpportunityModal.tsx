import ButtonGroup from '@mui/joy/ButtonGroup';
import Button from '@mui/joy/Button';
import ConfettiExplosion from 'react-confetti-explosion';
import Box from '@mui/joy/Box';
import {InsertLink} from '@mui/icons-material';
import {Project} from '@creator-campus/common';
import {ModalShell, ModalHeader, useSnackbar} from '@creator-campus/common-components';

interface Props {
  onClose: () => void;
  project: Project;
}

export default function ShareOpportunityModal({onClose, project}: Props) {
  const {showSnackbar} = useSnackbar();

  return (
    <>
      <ModalShell
        onClose={onClose}
        withCloseButton={true}
        closeOnBackgroundClick={true}
      >
        <ModalHeader
          title='Opportunity created!'
          subtitle='Would you like to share this opportunity now?'
        />
        <Box sx={{width: 350, height: 2, justifyItems: 'center', position: 'relative', top: -window.innerHeight * 0.55}}>
          <ConfettiExplosion
            width={1000}
            zIndex={995}
            force={0.4}
          />
        </Box>
        <ButtonGroup
          spacing={1}
          sx={{mt: 2}}
        >
          <Button
            variant='solid'
            color='primary'
            startDecorator={<InsertLink />}
            onClick={async () => {
              await navigator.clipboard.writeText(project.getLink());
              showSnackbar('Opportunity link copied to clipboard.');
              onClose();
            }}
          >
            Copy link
          </Button>
          <Button
            onClick={onClose}
            variant='outlined'
          >
            Skip
          </Button>
        </ButtonGroup>
      </ModalShell>
    </>
  );
}
