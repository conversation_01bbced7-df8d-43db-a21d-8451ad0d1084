import Box from '@mui/joy/Box';
import Typography from '@mui/joy/Typography';
import {isUrlValid, logger, MembershipApplication, OnboardingPathway, Persona, Role, User} from '@creator-campus/common';
import {LoadingIndicator, SnackbarWithDecorators, useProfileString, useSnackbar, useUser} from '@creator-campus/common-components';
import Divider from '@mui/joy/Divider';
import Stack from '@mui/joy/Stack';
import FormLabel from '@mui/joy/FormLabel';
import FormControl from '@mui/joy/FormControl';
import Input from '@mui/joy/Input';
import Tooltip from '@mui/joy/Tooltip';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import Select from '@mui/joy/Select';
import Option from '@mui/joy/Option';
import EmailRoundedIcon from '@mui/icons-material/EmailRounded';
import Button from '@mui/joy/Button';
import {GitHub, InfoOutlined, LinkedIn, Web} from '@mui/icons-material';
import FormHelperText from '@mui/joy/FormHelperText';
import {FormEvent, useEffect, useState} from 'react';
import AvatarWithUpload from './AvatarWithUpload.tsx';
import {EMAIL_INPUT_SLOT_PROPS, NAME_INPUT_SLOT_PROPS} from '../constants/styles.ts';
import UpdateEmailModal from './modals/UpdateEmailModal.tsx';
import {isValidGithubUsername, isValidLinkedinUsername, normaliseUrl} from '../utils.tsx';
import {updateDoc} from 'firebase/firestore';
import {InputWithChip} from './InputWithChip.tsx';
import Textarea from '@mui/joy/Textarea';
import {CardActions, CardOverflow} from '@mui/joy';
import OnboardingPathwaysToggleGroup from './OnboardingPathwaysToggleGroup.tsx';
import {BioGuidance} from './BioGuidance.tsx';
import Switch, {switchClasses} from '@mui/joy/Switch';
import {AddStartupAndOppBoxes} from './AddStartupAndOppBoxes.tsx';
import {FounderWithoutStartupWarningModal} from './modals/FounderWithoutStartupWarningModal.tsx';
import {useBlocker} from 'react-router-dom';
import {BlockNavigationModal} from './modals/BlockNavigationModal.tsx';
import {ProfileFormHeader} from './ProfileFormHeader.tsx';
import {UploadCVButton} from './UploadCVButton.tsx';

interface Props {
  variant?: 'default' | 'onboarding';
  buttonText?: string;
  withRevertChanges?: boolean;
  withNavigationBlocking?: boolean;
  onSave?: () => void;
}

export default function EditProfileForm({variant = 'default', buttonText, withRevertChanges = true, withNavigationBlocking = true, onSave}: Props) {
  const {user, university, profilePicUrl, membershipApplication} = useUser();

  if (!user || !university || (!membershipApplication && user.role !== Role.MENTOR)) {
    return <></>;
  }

  const [name, setName] = useState<string>(user.name);
  const [persona, setPersona] = useState<Persona | null>(user.persona);
  const [githubUsername, setGithubUsername] = useState<string>(user.githubUsername || '');
  const [linkedinUsername, setLinkedinUsername] = useState<string>(user.linkedinUsername || '');
  const [website, setWebsite] = useState<string>(user.website || '');
  // const [services, setServices] = useState<Service[]>(user.services || []);
  const [bio, setBio] = useState<string>(user.bio);
  const [whyJoin, setWhyJoin] = useState<string>(membershipApplication?.whyJoin || '');
  const [founder, setFounder] = useState<boolean>(user.founder);
  const [talent, setTalent] = useState<boolean>(user.openToWork);
  const [role, setRole] = useState<Role | null>(user.role);
  const [chosenAvatar, setChosenAvatar] = useState<File | null>(null);

  const [newOnboardingPathwaySelected, setNewOnboardingPathwaySelected] = useState<boolean>(false);
  const supporter = !founder && !talent && (variant === 'default' || newOnboardingPathwaySelected);

  const [githubOkay, setGithubOkay] = useState(true);
  const [linkedInOkay, setLinkedInOkay] = useState(true);
  // const [servicesOkay, setServicesOkay] = useState(true);
  const [websiteOkay, setWebsiteOkay] = useState(true);
  const [bioLenOkay, setBioLenOkay] = useState(true);
  const [whyJoinLenOkay, setWhyJoinLenOkay] = useState(true);
  const [profPicOkay, setProfPicOkay] = useState(true);
  const [onboardingPathwaysOkay, setOnboardingPathwaysOkay] = useState(true);

  const [loadingStatus, setLoadingStatus] = useState<'savingToFirestore' | 'uploadingAvatar' | null>(null);
  const [hasBeenEdited, setHasBeenEdited] = useState(false);
  const [showUpdateEmailModal, setShowUpdateEmailModal] = useState(false);
  const [showFounderWithoutStartupModal, setShowFounderWithoutStartupModal] = useState(false);

  const {showErrorSnackbar, showSnackbar} = useSnackbar();

  const blocker = useBlocker(({currentLocation, nextLocation}) => hasBeenEdited && currentLocation.pathname !== nextLocation.pathname);

  function revertChanges() {
    if (!user) {
      return;
    }

    setName(user.name);
    setPersona(user.persona);
    setGithubUsername(user.githubUsername || '');
    setLinkedinUsername(user.linkedinUsername || '');
    setWebsite(user.website || '');
    setBio(user.bio);
    setTalent(user.openToWork);
    setFounder(user.founder);
    setRole(user.role);
    setChosenAvatar(null);

    setGithubOkay(true);
    setLinkedInOkay(true);
    // setServicesOkay(true);
    setWebsiteOkay(true);
    setBioLenOkay(true);
    setProfPicOkay(true);

    setHasBeenEdited(false);
    showSnackbar('Changes reverted.', 'success');
  }

  function checkFormValid(linkedin: string, github: string) {
    const isOnboardingPathwaysOkay = founder || talent || supporter;
    setOnboardingPathwaysOkay(isOnboardingPathwaysOkay);

    if (!isOnboardingPathwaysOkay) {
      showSnackbar('Please select at least one role.', 'warning');
    }

    const isLinkedinValid = linkedin === '' || isValidLinkedinUsername(linkedin);
    setLinkedInOkay(isLinkedinValid);
    const isGithubValid = github === '' || isValidGithubUsername(github);
    setGithubOkay(isGithubValid);
    // const isServicesValid = services.length <= User.MAX_SERVICES;
    // setServicesOkay(isServicesValid);
    const isWebsiteValid = role === Role.MENTOR ? website.includes('calendly.com/') && isUrlValid(website) : website === '' || isUrlValid(website);
    setWebsiteOkay(isWebsiteValid);

    const isBioLenOkay = bio.length >= User.MIN_BIO_LEN && bio.length <= User.MAX_BIO_LEN;
    setBioLenOkay(isBioLenOkay);

    const isWhyJoinLenOkay = variant === 'default' || (whyJoin.length >= User.MIN_WHY_JOIN_LEN && whyJoin.length <= User.MAX_WHY_JOIN_LEN);
    setWhyJoinLenOkay(isWhyJoinLenOkay);

    const isProfPicOkay = chosenAvatar !== null || !!profilePicUrl;
    setProfPicOkay(isProfPicOkay);

    return isLinkedinValid && isGithubValid && isWebsiteValid && isBioLenOkay && isWhyJoinLenOkay && isProfPicOkay && isOnboardingPathwaysOkay;
  }

  function onProfPicSelected(profilePic: File) {
    setChosenAvatar(profilePic);
    setHasBeenEdited(true);
  }

  function AvatarComponent() {
    return (
      user && (
        <Stack
          spacing={0.2}
          width={140}
          alignItems={'center'}
        >
          <AvatarWithUpload
            profilePicUrl={profilePicUrl || undefined}
            selectedImageUrl={chosenAvatar ? URL.createObjectURL(chosenAvatar) : undefined}
            onImageSelected={onProfPicSelected}
          />
          <Box pt={1}>
            <UploadCVButton user={user} />
          </Box>
          <Stack
            direction={'row'}
            spacing={0.5}
            alignItems={'center'}
          >
            <Box sx={{visibility: 'hidden'}}>
              <InfoOutlinedIcon />
            </Box>
            <Typography
              level='body-sm'
              fontSize={'12px'}
            >
              (Optional)
            </Typography>
            <Tooltip
              title='After uploading your CV, you can choose to include it when you apply for opportunities.'
              variant='solid'
            >
              <InfoOutlinedIcon />
            </Tooltip>
          </Stack>
        </Stack>
      )
    );
  }

  // function handleServicesChanged(service: Service) {
  //   if (services.includes(service)) {
  //     setServices(services.filter((s) => s.id !== service.id));
  //   } else {
  //     setServices([...services, service]);
  //   }
  //   setServicesOkay(true);
  //   setHasBeenEdited(true);
  // }

  async function handleSubmit(user: User) {
    const cleanedLinkedin = linkedinUsername.split('/').join('').split(' ').join('');
    setLinkedinUsername(cleanedLinkedin);
    const cleanedGithub = githubUsername.split('/').join('').split(' ').join('');
    setGithubUsername(cleanedGithub);
    const cleanedWebsite = normaliseUrl(website);
    setWebsite(cleanedWebsite || website);

    if (checkFormValid(cleanedLinkedin, cleanedGithub)) {
      setLoadingStatus('savingToFirestore');

      if (variant === 'onboarding' && membershipApplication) {
        // Remove any startup if the user uploaded one but deselects themselves as a founder
        if (!founder && (membershipApplication.projectId || membershipApplication.opportunityId)) {
          await updateDoc(membershipApplication.doc(), {projectId: null, opportunityId: null});
        }
      }

      await updateDoc(user._doc, {
        bio,
        githubUsername: cleanedGithub === '' ? null : cleanedGithub,
        linkedinUsername: cleanedLinkedin === '' ? null : cleanedLinkedin,
        name,
        founder: founder,
        openToWork: talent,
        persona: persona!.id,
        role: role!.id,
        // services: services.map((s) => s.id),
        website: cleanedWebsite,
      })
        .then(async () => {
          if (chosenAvatar) {
            logger.debug('Updating profile image...');
            setLoadingStatus('uploadingAvatar');
            await user.updateAvatar(chosenAvatar).catch((e) => {
              logger.error(e);
              showErrorSnackbar(e, 'Error updating profile image. The rest of your profile was saved.');
            });
          }

          if (variant === 'onboarding') {
            await updateDoc(MembershipApplication.doc(user.id), {whyJoin});
            await updateDoc(user._doc, {
              applicationStatus: 'submitted',
            });
          }

          setHasBeenEdited(false);

          if (onSave) {
            onSave();
          } else {
            showSnackbar('Profile saved.', 'success');
          }
        })
        .catch((e) => {
          logger.error(e);
          showErrorSnackbar(e, 'Error updating profile.');
        });

      setLoadingStatus(null);
    }
  }

  useEffect(() => {
    if (variant === 'onboarding') {
      const unloadCallback = (event: BeforeUnloadEvent) => {
        if (hasBeenEdited) {
          event.preventDefault();
          return '';
        }
      };

      window.addEventListener('beforeunload', unloadCallback);
      return () => window.removeEventListener('beforeunload', unloadCallback);
    }
  }, [hasBeenEdited]);

  return (
    <>
      {withNavigationBlocking && blocker.state === 'blocked' && <BlockNavigationModal blocker={blocker} />}
      {showFounderWithoutStartupModal && (
        <FounderWithoutStartupWarningModal
          user={user}
          onConfirm={async () => {
            setShowFounderWithoutStartupModal(false);
            await handleSubmit(user);
          }}
          onCancel={() => setShowFounderWithoutStartupModal(false)}
        />
      )}
      {withRevertChanges && (
        <SnackbarWithDecorators
          text='You have unsaved changes.'
          isOpen={hasBeenEdited}
          color='warning'
          actionText={variant === 'default' ? 'Revert' : undefined}
          action={variant === 'default' ? revertChanges : undefined}
          autoHideMs={null}
        />
      )}
      <SnackbarWithDecorators
        text='You must upload a profile picture.'
        isOpen={!profPicOkay}
        color='warning'
        onClose={() => setProfPicOkay(true)}
      />
      <ProfileFormHeader variant={variant} />
      <Divider />
      <form
        style={{marginTop: '20px'}}
        onSubmit={async (event: FormEvent) => {
          event.preventDefault();

          if (variant === 'onboarding' && founder && membershipApplication && !membershipApplication.projectId) {
            setShowFounderWithoutStartupModal(true);
          } else {
            await handleSubmit(user);
          }
        }}
      >
        {/*Mobile view*/}
        <Stack
          spacing={1}
          sx={{display: {xs: 'flex', sm: 'none'}, alignItems: 'center'}}
        >
          <FormControl error={!profPicOkay}>
            <AvatarComponent />
          </FormControl>
        </Stack>

        <Stack
          direction='row'
          spacing={{xs: 0, sm: 3}}
          sx={{my: 1}}
        >
          {/*Desktop view*/}
          <Stack sx={{display: {xs: 'none', sm: 'flex'}}}>
            <FormControl error={!profPicOkay}>
              <AvatarComponent />
            </FormControl>
          </Stack>

          <Stack
            spacing={2}
            sx={{
              flexGrow: 1,
              maxWidth: '100%',
            }}
          >
            <Stack spacing={1}>
              <FormLabel>{useProfileString().name}</FormLabel>
              <FormControl>
                <Input
                  key={user.name}
                  value={name}
                  slotProps={NAME_INPUT_SLOT_PROPS}
                  required
                  placeholder='Jane Doe'
                  size='sm'
                  onChange={(event) => {
                    setName(event.target.value);
                    setHasBeenEdited(true);
                  }}
                />
              </FormControl>
            </Stack>
            <FormControl>
              <FormLabel>
                {useProfileString().background}
                <Tooltip
                  sx={{maxWidth: 300}}
                  enterTouchDelay={0}
                  title="If you're a coder, scientist, or engineer with the technical know-how to build a product select Technical. If you're doing something creative such as graphic design, select Creative. Otherwise, select Business."
                  variant='solid'
                >
                  <InfoOutlinedIcon fontSize='large' />
                </Tooltip>
              </FormLabel>
              <Select
                key='persona'
                value={persona}
                placeholder='Select your background'
                required
                sx={{minWidth: 200}}
                onChange={(_, value) => {
                  if (value) {
                    setPersona(value);
                    setHasBeenEdited(true);
                  }
                }}
                size='sm'
              >
                {Persona.values().map((persona: Persona) => (
                  <Option
                    key={persona.label}
                    value={persona}
                  >
                    {persona.label}
                  </Option>
                ))}
              </Select>
            </FormControl>
            <FormControl>
              <FormLabel>Status</FormLabel>
              <Select
                key='role'
                value={role}
                placeholder='Select your status'
                required
                disabled={role === Role.MENTOR}
                sx={{minWidth: 200}}
                onChange={(_, newRole) => {
                  if (newRole) {
                    setRole(newRole);
                    setHasBeenEdited(true);
                  }
                }}
                size='sm'
              >
                {Role.values()
                  .filter((r) => role === Role.MENTOR || r !== Role.MENTOR)
                  .map((r) => (
                    <Option
                      key={r.label}
                      value={r}
                    >
                      {r.label}
                    </Option>
                  ))}
              </Select>
            </FormControl>
            {role === Role.MENTOR && (
              <Stack spacing={1}>
                <FormLabel>
                  Calendly URL
                  <Tooltip
                    title={`Users from ${university.name} will use this link to book mentorship meetings with you. Look for the Event Type you want to share on your Calendly homepage, click 'Copy link', then come back and paste it here!`}
                    variant='solid'
                    sx={{maxWidth: 400}}
                  >
                    <InfoOutlinedIcon fontSize='large' />
                  </Tooltip>
                </FormLabel>
                <FormControl error={!websiteOkay}>
                  <Input
                    key='calendly-url'
                    value={website}
                    required
                    size='sm'
                    slotProps={{input: {maxLength: 500}}}
                    placeholder='https://calendly.com/your_scheduling_page'
                    startDecorator={<Web />}
                    onChange={(event) => {
                      setWebsite(event.target.value);
                      setHasBeenEdited(true);
                    }}
                  />
                  {!websiteOkay ? (
                    <FormHelperText>
                      {' '}
                      <InfoOutlined />
                      Invalid URL
                    </FormHelperText>
                  ) : (
                    <></>
                  )}
                </FormControl>
              </Stack>
            )}
            {user.role === Role.MENTOR ? (
              <></>
            ) : variant === 'default' ? (
              <FormControl>
                <FormLabel>
                  {useProfileString().opportunity_seeker}
                  <Typography level='body-sm'>{useProfileString().opportunity_guidance}</Typography>
                </FormLabel>
                <Box sx={{display: 'flex', alignItems: 'center'}}>
                  <Switch
                    sx={{
                      [`& .${switchClasses.thumb}`]: {
                        transition: 'width 0.2s, left 0.2s',
                      },
                      '--Switch-thumbSize': '17px',
                      '--Switch-trackWidth': '40px',
                      '--Switch-trackHeight': '22px',
                    }}
                    checked={talent}
                    onChange={(event) => {
                      setTalent(event.target.checked);
                      setHasBeenEdited(true);
                    }}
                  />
                </Box>
              </FormControl>
            ) : (
              <Stack sx={{position: 'relative'}}>
                <FormLabel>Role</FormLabel>
                <Typography level='body-sm'>Select the option(s) that describes you.</Typography>
                {!onboardingPathwaysOkay && (
                  <FormControl error>
                    <FormHelperText>
                      <InfoOutlined />
                      Please select at least one role.
                    </FormHelperText>
                  </FormControl>
                )}
                <OnboardingPathwaysToggleGroup
                  initialRoles={[...(talent ? [OnboardingPathway.TALENT] : []), ...(founder ? [OnboardingPathway.FOUNDER] : []), ...(supporter ? [OnboardingPathway.SUPPORTER] : [])]}
                  onRolesChanged={(selectedRoles) => {
                    setOnboardingPathwaysOkay(true);

                    const talentNow = selectedRoles.includes(OnboardingPathway.TALENT);
                    const founderNow = selectedRoles.includes(OnboardingPathway.FOUNDER);

                    setTalent(talentNow);
                    setFounder(founderNow);
                  }}
                  onRoleSelected={(role) => {
                    setNewOnboardingPathwaySelected(!!role);
                  }}
                />
                {founder && membershipApplication && (
                  <AddStartupAndOppBoxes
                    user={user}
                    membershipApplication={membershipApplication}
                  />
                )}
              </Stack>
            )}
            {variant === 'default' && (
              <FormControl sx={{flexGrow: 1}}>
                <FormLabel>{useProfileString().email}</FormLabel>
                <Stack direction='row'>
                  <Input
                    type='email'
                    disabled
                    size='sm'
                    slotProps={EMAIL_INPUT_SLOT_PROPS}
                    startDecorator={<EmailRoundedIcon />}
                    placeholder={user.email}
                    sx={{flexGrow: 1, borderTopRightRadius: 0, borderBottomRightRadius: 0, minHeight: 35}}
                  />
                  <Button
                    onClick={() => setShowUpdateEmailModal(true)}
                    sx={{width: 80, borderTopLeftRadius: 0, borderBottomLeftRadius: 0, fontSize: 13}}
                  >
                    Change
                  </Button>
                  {showUpdateEmailModal && <UpdateEmailModal onClose={() => setShowUpdateEmailModal(false)} />}
                </Stack>
              </FormControl>
            )}
            {/*{role !== Role.MENTOR && (*/}
            {/*  <Stack spacing={1}>*/}
            {/*    <FormLabel>Services offered (optional)</FormLabel>*/}
            {/*    <FormControl error={!servicesOkay}>*/}
            {/*      /!*Wrapped list of chips*!/*/}
            {/*      <Stack*/}
            {/*        spacing={1}*/}
            {/*        direction={'row'}*/}
            {/*        flexWrap={'wrap'}*/}
            {/*      >*/}
            {/*        <Typography level={'body-sm'}>Recommended:</Typography>*/}
            {/*        {user.persona &&*/}
            {/*          Service.recommended(user.persona).map((service: Service) => (*/}
            {/*            <Chip*/}
            {/*              key={service.id}*/}
            {/*              size={'sm'}*/}
            {/*              startDecorator={<service.icon />}*/}
            {/*              endDecorator={services.includes(service) ? <Check /> : null}*/}
            {/*              color={services.includes(service) ? 'primary' : 'neutral'}*/}
            {/*              variant={services.includes(service) ? 'solid' : 'outlined'}*/}
            {/*              onClick={() => handleServicesChanged(service)}*/}
            {/*            >*/}
            {/*              {service.label}*/}
            {/*            </Chip>*/}
            {/*          ))}*/}
            {/*      </Stack>*/}
            {/*      <Autocomplete*/}
            {/*        placeholder={services.length === 0 ? 'Add services...' : ''}*/}
            {/*        options={Service.values()}*/}
            {/*        getOptionLabel={(o) => o.label}*/}
            {/*        value={services}*/}
            {/*        onChange={(_, values) => {*/}
            {/*          setServices(values);*/}
            {/*          setHasBeenEdited(true);*/}
            {/*          // setServicesOkay(true);*/}
            {/*        }}*/}
            {/*        multiple*/}
            {/*        size='sm'*/}
            {/*        type={'search'}*/}
            {/*        sx={{mt: 1}}*/}
            {/*      />*/}
            {/*      {!servicesOkay ? (*/}
            {/*        <FormHelperText>*/}
            {/*          {' '}*/}
            {/*          <InfoOutlined />*/}
            {/*          Invalid URL*/}
            {/*        </FormHelperText>*/}
            {/*      ) : (*/}
            {/*        <></>*/}
            {/*      )}*/}
            {/*    </FormControl>*/}
            {/*  </Stack>*/}
            {/*)}*/}
            <Divider />
            <Stack spacing={1}>
              <FormLabel>LinkedIn (Recommended)</FormLabel>
              <FormControl error={!linkedInOkay}>
                <InputWithChip
                  key='linkedin'
                  value={linkedinUsername}
                  size='sm'
                  slotProps={{input: {maxLength: 100}}}
                  placeholder='username'
                  chipText={'linkedin.com/in/'}
                  chipIcon={<LinkedIn />}
                  onChange={(event) => {
                    setLinkedinUsername(event.target.value);
                    setHasBeenEdited(true);
                  }}
                />
                {!linkedInOkay ? (
                  <FormHelperText>
                    {' '}
                    <InfoOutlined />
                    Invalid LinkedIn URL
                  </FormHelperText>
                ) : (
                  <></>
                )}
              </FormControl>
            </Stack>
            <Stack spacing={1}>
              <FormLabel>{useProfileString().github}</FormLabel>
              <FormControl error={!githubOkay}>
                <InputWithChip
                  key='github'
                  value={githubUsername}
                  size='sm'
                  slotProps={{input: {maxLength: 100}}}
                  placeholder='username'
                  chipText={'github.com/'}
                  chipIcon={<GitHub />}
                  onChange={(event) => {
                    setGithubUsername(event.target.value);
                    setHasBeenEdited(true);
                  }}
                />
                {!githubOkay ? (
                  <FormHelperText>
                    {' '}
                    <InfoOutlined />
                    Invalid GitHub URL
                  </FormHelperText>
                ) : (
                  <></>
                )}
              </FormControl>
            </Stack>
            {role !== Role.MENTOR && (
              <Stack spacing={1}>
                <FormLabel>{useProfileString().website}</FormLabel>
                <FormControl error={!websiteOkay}>
                  <Input
                    key='website'
                    value={website}
                    size='sm'
                    slotProps={{input: {maxLength: 500}}}
                    placeholder='https://www.example.com'
                    startDecorator={<Web />}
                    onChange={(event) => {
                      setWebsite(event.target.value);
                      setHasBeenEdited(true);
                    }}
                  />
                  {!websiteOkay ? (
                    <FormHelperText>
                      {' '}
                      <InfoOutlined />
                      Invalid URL
                    </FormHelperText>
                  ) : (
                    <></>
                  )}
                </FormControl>
              </Stack>
            )}
          </Stack>
        </Stack>

        {/*Bio*/}
        <Stack
          spacing={2}
          sx={{flexGrow: 1, mt: 3, mb: 3}}
        >
          <Box sx={{mb: 1}}>
            <Typography
              level='title-md'
              sx={{mb: 0.5}}
            >
              {useProfileString().bio}
            </Typography>
            <BioGuidance
              founder={founder}
              talent={talent}
              supporter={supporter}
            />
          </Box>
          <Stack
            spacing={2}
            sx={{my: 1}}
          >
            <FormControl error={!bioLenOkay}>
              <Textarea
                key='bio'
                value={bio}
                required
                size='sm'
                minRows={4}
                maxRows={5}
                sx={{mt: 1.5}}
                onChange={(event) => {
                  setBio(event.target.value);
                  setHasBeenEdited(true);
                }}
                endDecorator={
                  <Typography
                    level='body-xs'
                    sx={{ml: 'auto'}}
                  >
                    {bio.length}
                  </Typography>
                }
              />
              {bioLenOkay ? (
                <></>
              ) : (
                <FormHelperText sx={{mt: 0.75, fontSize: 'xs'}}>
                  Bio must be between {User.MIN_BIO_LEN} and {User.MAX_BIO_LEN} characters long.
                </FormHelperText>
              )}
            </FormControl>
          </Stack>
        </Stack>

        <Divider sx={{mt: 3}} />
        <Divider />

        {/*Why join Creator Campus*/}
        {variant === 'onboarding' && (
          <Stack
            spacing={2}
            sx={{flexGrow: 1, mt: 3, mb: 3}}
          >
            <Box sx={{mb: 1}}>
              <Typography level='title-md'>Where did you hear about Creator Campus?</Typography>
              <Typography level='body-sm'>We aren't looking for an essay! A sentence or two will do. Your response will only be visible to Creator Campus admins and will help us to better support you.</Typography>
            </Box>
            <Stack
              spacing={2}
              sx={{my: 1}}
            >
              <FormControl error={!whyJoinLenOkay}>
                <Textarea
                  key='whyJoin'
                  value={whyJoin}
                  required
                  size='sm'
                  minRows={4}
                  maxRows={5}
                  sx={{mt: 1.5}}
                  onChange={(event) => {
                    setWhyJoin(event.target.value);
                    setHasBeenEdited(true);
                  }}
                  endDecorator={
                    <Typography
                      level='body-xs'
                      sx={{ml: 'auto'}}
                    >
                      {whyJoin.length}
                    </Typography>
                  }
                />
                {whyJoinLenOkay ? (
                  <></>
                ) : (
                  <FormHelperText sx={{mt: 0.75, fontSize: 'xs'}}>
                    Response must be between {User.MIN_WHY_JOIN_LEN} and {User.MAX_WHY_JOIN_LEN} characters long.
                  </FormHelperText>
                )}
              </FormControl>
            </Stack>
          </Stack>
        )}

        <CardOverflow sx={{borderTop: '1px solid', borderColor: 'divider', justifyContent: 'right'}}>
          <CardActions sx={{pt: 2, display: 'flex', justifyContent: 'right'}}>
            {variant === 'default' && withRevertChanges && (
              <Button
                size='sm'
                variant='plain'
                onClick={revertChanges}
                disabled={!hasBeenEdited || !!loadingStatus}
              >
                Revert changes
              </Button>
            )}
            <Box>
              <Button
                size='sm'
                variant='solid'
                type='submit'
                disabled={(!hasBeenEdited && variant === 'default') || !!loadingStatus}
              >
                {loadingStatus ? <LoadingIndicator size='sm' /> : buttonText || 'Save'}
              </Button>
            </Box>
          </CardActions>
        </CardOverflow>

        {loadingStatus && (
          <Typography
            level={'body-sm'}
            justifySelf={'end'}
            mt={0.5}
          >
            {loadingStatus === 'savingToFirestore' ? 'Saving your details...' : 'Uploading profile image...'}
          </Typography>
        )}
      </form>
    </>
  );
}
