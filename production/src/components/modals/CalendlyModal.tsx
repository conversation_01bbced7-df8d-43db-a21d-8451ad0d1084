import {ModalShell} from '@creator-campus/common-components';
import {InlineWidget} from 'react-calendly';

interface Props {
  onClose: () => void;
  calendlyUrl: string;
}

export default function CalendlyModal({onClose, calendlyUrl}: Props) {
  return (
    <ModalShell
      onClose={onClose}
      withPadding={false}
      closeOnBackgroundClick={true}
    >
      <InlineWidget url={calendlyUrl} />
    </ModalShell>
  );
}
