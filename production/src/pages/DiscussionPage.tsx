import {useRef} from 'react';
import {TabData, StaffRole} from '@creator-campus/common';
import {LoadingIndicator, PageHeader, PageShell, useUser} from '@creator-campus/common-components';
import {Public, School} from '@mui/icons-material';
import Stack from '@mui/joy/Stack';
import {Link as RouterLink, useParams} from 'react-router-dom';
import NotificationsIconButton from '../components/NotificationsIconButton.tsx';
import {ListItemDecorator, Tab, tabClasses, TabList, TabPanel, Tabs} from '@mui/joy';
import DiscussionContent from '../components/DiscussionContent.tsx';

interface Props {
  tab?: 'everyone' | 'my-university';
}

export default function DiscussionPage({tab}: Props) {
  const scrollableTarget = useRef<HTMLDivElement | null>(null);

  const {user, university} = useUser();
  const params = useParams();

  if (!user || !university) {
    return <LoadingIndicator size={'md'} />;
  }

  const tabs: TabData[] = [
    {
      displayName: 'My University',
      leadingIcon: <School />,
      path: 'my-university',
      content: (
        <DiscussionContent
          scrollableTarget={scrollableTarget?.current}
          universityId={user.universityId}
        />
      ),
    },
    {
      displayName: 'Global',
      leadingIcon: <Public />,
      path: 'everyone',
      content: <DiscussionContent scrollableTarget={scrollableTarget?.current} />,
    },
  ];

  return (
    <PageShell
      scrollableTarget={scrollableTarget}
      footer={false}
      banners={{profileIncomplete: {hideChildrenIfShown: true}, activateCommunity: {hideChildrenIfShown: false}}}
    >
      <Stack
        direction={'row'}
        alignItems={'start'}
        justifyContent={'space-between'}
      >
        <PageHeader
          title='Discussion'
          subtitle='Ask questions, share resources, and post company updates.'
        />
        {user && <NotificationsIconButton user={user} />}
      </Stack>
      {user.universityId === 'University of Oxford' ? (
        // For Oxford users, don't show the global discussion feed
        <DiscussionContent
          scrollableTarget={scrollableTarget?.current}
          universityId={user.universityId}
        />
      ) : university.partner || user.staffRole === StaffRole.OWNER ? (
        // If a partner, show the tabs
        <Tabs
          defaultValue={tab || (params.universityId === 'global' || !params.universityId ? tabs[0].path : tabs[1].path)}
          sx={{
            bgcolor: 'transparent',
          }}
          value={params.tab}
        >
          <TabList
            tabFlex={1}
            size='sm'
            sx={{
              pl: {xs: 0, md: 4},
              justifyContent: 'left',
              [`&& .${tabClasses.root}`]: {
                fontWeight: '600',
                flex: 'initial',
                color: 'text.tertiary',
                [`&.${tabClasses.selected}`]: {
                  bgcolor: 'transparent',
                  color: 'text.primary',
                  '&::after': {
                    height: '2px',
                    bgcolor: 'primary.500',
                  },
                },
              },
            }}
          >
            {...tabs.map((tab) => (
              <Tab
                sx={{borderRadius: '6px 6px 0 0'}}
                indicatorInset
                value={tab.path}
                component={RouterLink}
                to={`/discussion/${tab.path}`}
              >
                <ListItemDecorator>{tab.leadingIcon}</ListItemDecorator>
                {tab.displayName}
              </Tab>
            ))}
          </TabList>
          {...tabs.map((tab) => <TabPanel value={tab.path}>{tab.content}</TabPanel>)}
        </Tabs>
      ) : (
        // If not a partner, only show the global discussion feed
        <DiscussionContent scrollableTarget={scrollableTarget?.current} />
      )}
    </PageShell>
  );
}
