import FormLabel from '@mui/joy/FormLabel';
import {FormHelperText, Switch} from '@mui/joy';
import {firestore, functions, MaintenanceMode, MaintenanceModeConverter, User} from '@creator-campus/common';
import {LoadingIndicator, useSnackbar} from '@creator-campus/common-components';
import {ChangeEvent, useEffect, useState} from 'react';
import {httpsCallable} from 'firebase/functions';
import {doc, onSnapshot} from 'firebase/firestore';
import Box from '@mui/joy/Box';

interface Props {
  user: User | null;
}

export default function MaintenanceModeToggle({user}: Props) {
  const [maintenanceMode, setMaintenanceMode] = useState<MaintenanceMode | null>(null);

  const {showErrorSnackbar} = useSnackbar();

  useEffect(() => {
    const unsub = onSnapshot(doc(firestore(), 'config', 'maintenanceMode').withConverter(new MaintenanceModeConverter()), (snapshot) => {
      setMaintenanceMode(snapshot.data()!);
    });

    return () => unsub();
  }, []);

  return (
    <>
      <Box>
        <FormLabel>Maintenance mode</FormLabel>
        <FormHelperText>{maintenanceMode ? (maintenanceMode.enabled ? 'Enabled since:' : 'Disabled since:') : 'Loading...'}</FormHelperText>
        <FormHelperText sx={{mb: 1}}>{maintenanceMode ? maintenanceMode.lastToggled.toLocaleString() : '...'}</FormHelperText>
        {!maintenanceMode ? (
          <LoadingIndicator size='sm' />
        ) : (
          <Switch
            disabled={user === null}
            checked={maintenanceMode?.enabled || false}
            onChange={async (event: ChangeEvent<HTMLInputElement>) => {
              setMaintenanceMode(null);

              try {
                await httpsCallable(
                  functions(),
                  'toggleMaintenanceMode',
                )({
                  enabled: event.target.checked,
                  userEmail: user!.email,
                });
              } catch (e) {
                setMaintenanceMode(maintenanceMode);
                showErrorSnackbar(e, 'Error toggling maintenance mode.');
              }
            }}
            color={maintenanceMode?.enabled ? 'success' : 'neutral'}
            variant={maintenanceMode?.enabled ? 'solid' : 'outlined'}
            endDecorator={maintenanceMode?.enabled ? 'On' : 'Off'}
            slotProps={{
              endDecorator: {
                sx: {
                  minWidth: 24,
                },
              },
            }}
          />
        )}
      </Box>
    </>
  );
}
