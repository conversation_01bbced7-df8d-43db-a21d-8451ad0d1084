import {type EmailTemplate} from './emailTemplate.js';

interface Data {
  // Number of days the link is valid
  daysValid: number;
  // Link to sign up for alumnus account
  link: string;
  // Recipient name
  name: string;
}

export class AlumniApplicationAcceptedEmailTemplate implements EmailTemplate {
  readonly template: string = 'alumni_application_accepted';

  constructor(readonly data: Data) {}
}
