import {onDocumentCreated, onDocumentDeleted, onDocumentUpdated} from 'firebase-functions/v2/firestore';
import {FieldValue, Transaction} from 'firebase-admin/firestore';
import {getMetricsCollection} from './utils';
import * as admin from 'firebase-admin';
import {getInfluxWriter, newPoint} from './influxdb';
import {Opportunity, OpportunityApplication, OpportunityDbModelSafe, Project, User, UserDbModelSafe} from '@creator-campus/common';

export const onOpportunityCreated = onDocumentCreated(
  {
    document: 'opportunities/{oppId}',
    secrets: ['REACT_APP_INFLUX_DB_TOKEN'],
  },
  async (event) => {
    if (!event.data) {
      console.error('Event data is undefined!');
      return;
    }

    const data = event.data.data() as OpportunityDbModelSafe;
    const oppId = event.params.oppId;
    console.log(`Detected opportunity ${oppId} created (project ${data.projectId}).`);

    // Update project fields
    await syncOpportunitiesToParentProject(data.projectId);

    // Update metrics
    await getMetricsCollection(data.universityId).update({
      opportunitiesCreated: FieldValue.increment(1),
    });

    const influxToken = String(process.env.REACT_APP_INFLUX_DB_TOKEN);
    const influxWriter = getInfluxWriter(influxToken);

    const point = newPoint().intField('opportunitiesCreated', 1).tag('university', data.universityId);
    influxWriter.writePoint(point);
  },
);

async function syncOpportunitiesToParentProject(projectId: string) {
  const opportunitySnapshot = await admin.firestore().collection(Opportunity.collectionName).where('projectId', '==', projectId).get();
  const opportunities = opportunitySnapshot.docs.map((d) => d.data() as OpportunityDbModelSafe);
  const numOpenOpportunities = opportunities.filter((o) => o.fillDate === null).length;
  const opportunityTitles = Array.from(new Set(opportunities.map((o) => o.title)));
  const opportunityDescriptions = Array.from(new Set(opportunities.map((o) => o.description)));
  const compensations = Array.from(new Set(opportunities.map((o) => o.compensation).flat()));
  const locations = Array.from(new Set(opportunities.map((o) => o.location)));

  await admin.firestore().collection(Project.collectionName).doc(projectId).update({
    numOpenOpportunities,
    opportunityTitles,
    opportunityDescriptions,
    compensations,
    locations,
  });
}

export const onOpportunityUpdated = onDocumentUpdated(
  {
    document: 'opportunities/{oppId}',
    secrets: ['REACT_APP_INFLUX_DB_TOKEN'],
  },
  async (event) => {
    if (!event.data) {
      console.error('Event data is undefined!');
      return;
    }

    const beforeData = event.data.before.data() as OpportunityDbModelSafe;
    const afterData = event.data.after.data() as OpportunityDbModelSafe;
    const oppId = event.params.oppId;
    console.log(`Detected opportunity ${oppId} updated (project ${afterData.projectId}).`);

    // Check if the title or description changed (and we have to update them in the project doc)
    if (beforeData.title !== afterData.title || beforeData.description !== afterData.description) {
      await admin
        .firestore()
        .collection(Project.collectionName)
        .doc(afterData.projectId)
        .update({
          opportunityTitles: FieldValue.arrayRemove(beforeData.title),
          opportunityDescriptions: FieldValue.arrayRemove(beforeData.description),
        });

      await admin
        .firestore()
        .collection(Project.collectionName)
        .doc(afterData.projectId)
        .update({
          opportunityTitles: FieldValue.arrayUnion(afterData.title),
          opportunityDescriptions: FieldValue.arrayUnion(afterData.description),
        });
    }

    // Check if opportunity just got filled
    if (beforeData.fillDate === null && afterData.fillDate !== null) {
      console.log(`Opportunity just got filled. Updating related project: ${afterData.projectId}`);

      // Reduce project's numOpenOpportunities
      await admin
        .firestore()
        .collection(Project.collectionName)
        .doc(afterData.projectId)
        .update({
          numOpenOpportunities: FieldValue.increment(-1),
        });

      console.log('Updating metrics...');

      // Get applicant's university and update their university's metrics too
      const applicantSnapshot = await admin.firestore().collection(User.collectionName).doc(afterData.acceptedApplicantId!).get();
      const applicant = applicantSnapshot.data() as UserDbModelSafe;
      await getMetricsCollection(applicant.universityId).update({
        outgoingOppApplicationsAccepted: FieldValue.increment(1),
        unisWhoseOppsWeFill: {
          [afterData.universityId]: FieldValue.increment(1),
        },
      });

      const hoursSpentOpen = (new Date().getTime() - afterData.datePosted.toDate().getTime()) / 1000 / 60 / 60;
      await getMetricsCollection(afterData.universityId).update({
        hoursOppsSpentOpen: FieldValue.increment(hoursSpentOpen),
        unisFillingOurOpps: {
          [applicant.universityId]: FieldValue.increment(1),
        },
      });

      const influxToken = String(process.env.REACT_APP_INFLUX_DB_TOKEN);
      const influxWriter = getInfluxWriter(influxToken);

      let point = newPoint().intField('oppApplicationsAccepted', 1).tag('university', afterData.universityId).tag('applicantUniversity', applicant.universityId);
      influxWriter.writePoint(point);

      point = newPoint().floatField('hoursOppsSpentOpen', hoursSpentOpen).tag('university', afterData.universityId);
      influxWriter.writePoint(point);
    }
  },
);

export const onOpportunityDeleted = onDocumentDeleted('opportunities/{oppId}', async (event) => {
  if (!event.data) {
    console.error('Event data is undefined!');
    return;
  }

  const data = event.data.data();
  const oppId = event.params.oppId;
  console.log(`Detected opportunity ${oppId} deleted (project ${data.projectId}).`);

  const projectRef = admin.firestore().collection(Project.collectionName).doc(data.projectId);
  const project = await projectRef.get();

  if (project.exists) {
    console.log(`Syncing to parent project ${data.projectId}...`);
    await syncOpportunitiesToParentProject(data.projectId);
  } else {
    console.log('No project to update as it no longer exists (this is expected behaviour if this Function was triggered by a project deletion).');
  }

  // Delete related opportunity applications
  const applicationsSnapshot = await admin.firestore().collection(OpportunityApplication.collectionName).where('opportunityId', '==', oppId).get();
  console.log(`Deleting ${applicationsSnapshot.size} related opportunity applications...`);
  for (const application of applicationsSnapshot.docs) {
    await application.ref.delete();
  }
});
