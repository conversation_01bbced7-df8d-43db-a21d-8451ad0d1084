import normalizeUrl from 'normalize-url';
import {Link} from '@mui/joy';
import {ContentCopy, OpenInNew} from '@mui/icons-material';
import {isUrlValid} from '@creator-campus/common';

export function normaliseUrl(url: string): string | null {
  try {
    return normalizeUrl(url, {
      defaultProtocol: 'https',
      forceHttps: true,
      stripAuthentication: true,
      stripTextFragment: true,
    });
  } catch (_) {
    return null;
  }
}

export function isValidGithubUsername(username: string) {
  const regex = /^[a-z\d](?:[a-z\d]|-(?=[a-z\d])){0,38}$/i;
  return regex.test(username);
}

export function isValidLinkedinUsername(username: string) {
  const regex = /^[a-zA-Z0-9-]{3,100}$/;
  return regex.test(username) && !/^[-]|[-]$/.test(username);
}

export function enrichText(text: string, showSnackbar: (text: string) => void) {
  const isEmail = (str: string) => /\b[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}\b/i.test(str);

  return text.split(/([,\s]+)/).map((part) => {
    if (isEmail(part)) {
      return (
        <Link
          key={part}
          onClick={async () => {
            await navigator.clipboard.writeText(part);
            showSnackbar('Email copied to clipboard.');
          }}
        >
          {part}
          <ContentCopy sx={{fontSize: '16px', ml: 0.3}} />
        </Link>
      );
    }

    if (isUrlValid(part)) {
      return (
        <Link
          key={part}
          href={part}
          target='_blank'
          rel='noopener noreferrer'
        >
          {part}
          <OpenInNew sx={{fontSize: '16px', ml: 0.3}} />
        </Link>
      );
    }

    return part;
  });
}
