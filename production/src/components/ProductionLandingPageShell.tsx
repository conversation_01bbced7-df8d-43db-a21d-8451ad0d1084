import {LandingPageShell} from '@creator-campus/common-components';
import {ReactNode} from 'react';
import {CreatorCampusLogo} from './CreatorCampusLogo';

interface Props {
  lightImageUrl?: string;
  darkImageUrl?: string;
  children: ReactNode;
}

export function ProductionLandingPageShell({lightImageUrl, darkImageUrl, children}: Props) {
  return (
    <LandingPageShell
      lightImageUrl={lightImageUrl ?? 'url(https://firebasestorage.googleapis.com/v0/b/creator-campus-app.appspot.com/o/site-images%2Flogin-light.png?alt=media&token=d94bd7be-da3a-4bbe-a14c-88b64ed06ee2)'}
      darkImageUrl={darkImageUrl ?? 'url(https://firebasestorage.googleapis.com/v0/b/creator-campus-app.appspot.com/o/site-images%2Flogin-dark.png?alt=media&token=2ca307c8-e592-4551-8893-a3ec7d408197)'}
      creatorCampusLogo={<CreatorCampusLogo size={'40px'} />}
    >
      {children}
    </LandingPageShell>
  );
}
