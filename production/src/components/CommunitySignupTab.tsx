import Button from '@mui/joy/Button';
import Typography from '@mui/joy/Typography';
import Stack from '@mui/joy/Stack';
import {TermsAndConditionsCheckbox} from './TermsAndConditionsCheckbox.tsx';
import {useNavigate} from 'react-router-dom';

export function CommunitySignupTab() {
  const navigate = useNavigate();

  return (
    <Stack sx={{pl: {xs: 0, sm: 2}, pr: {xs: 0, sm: 2}}}>
      <Typography
        level={'body-md'}
        sx={{mt: 1, mb: 1}}
        fontWeight='bold'
      >
        We're building the complete ecosystem.
      </Typography>
      <Typography
        level={'body-md'}
        sx={{mb: 1}}
      >
        If you are an aspiring entrepreneur under 30 without a degree, a mentor, or an investor, you are welcome to request community access. We will review your request and email you with the outcome.
      </Typography>
      <form
        onSubmit={(event) => {
          event.preventDefault();
          window.open('https://forms.gle/dtRet1yKgmcr4Nd57', '_blank');
          navigate('/login');
        }}
      >
        <TermsAndConditionsCheckbox sx={{mt: 2, mb: 0.5}} />
        <Button
          fullWidth
          type='submit'
        >
          Join now
        </Button>
      </form>
    </Stack>
  );
}
