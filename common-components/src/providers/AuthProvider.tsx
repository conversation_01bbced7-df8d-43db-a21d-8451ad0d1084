import {createContext, useContext, useEffect, useState, type ReactNode} from 'react';
import {createUserWithEmailAndPassword, onAuthStateChanged, signInWithEmailAndPassword, signOut, type User} from 'firebase/auth';
import { auth } from '@creator-campus/common';

type withChildren = {
  children: ReactNode;
};

interface AuthContextType {
  currentUser: User | null;
  signUp: (email: string, password: string) => Promise<void>;
  login: (email: string, password: string) => Promise<unknown>;
  logout: () => Promise<void>;
  loadingAuth: boolean;
}

// Create AuthContext
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// AuthProvider component
function AuthProvider({children}: withChildren) {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const unsub = onAuthStateChanged(auth(), (user) => {
      setCurrentUser(user);
      setLoading(false);
    });

    return () => unsub();
  }, []);

  // Sign Up function
  const signUp = async (email: string, password: string) => {
    await createUserWithEmailAndPassword(auth(), email, password);
  };

  // Login function
  const login = async (email: string, password: string) => {
    try {
      await signInWithEmailAndPassword(auth(), email, password);
      return null; // Return null if login is successful
    } catch (error) {
      return error; // Return the error object if login fails
    }
  };

  // Logout function
  const logout = async () => {
    await signOut(auth());
  };

  const value: AuthContextType = {
    currentUser,
    signUp,
    login,
    logout,
    loadingAuth: loading,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

// Custom hook to access AuthContext
const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export {AuthProvider, useAuth};
