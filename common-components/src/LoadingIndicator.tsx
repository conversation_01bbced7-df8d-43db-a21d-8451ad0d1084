import {CircularProgress} from '@mui/joy';
import Box from '@mui/joy/Box';

interface Props {
  size?: 'sm' | 'md' | 'lg';
  sx?: object;
}

export function LoadingIndicator({size, sx}: Props) {
  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        ...sx,
      }}
    >
      <CircularProgress size={size ?? 'lg'} />
    </Box>
  );
}
