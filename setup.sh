#!/bin/bash

# Load nvm
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"

# Install Node v20 and set as default
nvm install 20
nvm use 20
nvm alias default 20

# Ensure the latest version of Firebase CLI is installed
echo "Installing the latest version of Firebase CLI (this may take a minute)..."
npm install -g firebase-tools

# Log into Firebase (user completes login in the browser)
echo ""
echo "Logging into Firebase (please complete login in your browser if needed)..."
firebase login

# Fetch secrets and write .env files
sh sync_secrets.sh

# Install dependencies
echo "Installing dependencies..."
yarn install

# Install dependencies
echo "Building project..."
yarn build

echo ""
echo "Setup complete!"
echo ""
echo "You can now run 'npm run emulators' to start the emulators."
echo "Then run 'npm run dev' in another terminal to run the app."
