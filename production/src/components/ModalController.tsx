import {useLocation, useSearchParams} from 'react-router-dom';
import {StaffRole} from '@creator-campus/common';
import {LoadingIndicator, ModalShell, useUser} from '@creator-campus/common-components';
import {ActivateYourCommunityModal} from './modals/ActivateYourCommunityModal.tsx';
import {NewPartnerModal} from './modals/NewPartnerModal.tsx';
import {useEffect, useState} from 'react';

export function ModalController() {
  const location = useLocation();
  const isOnboarding = location.pathname === '/onboarding';
  const {user, university} = useUser();
  const [searchParams, setSearchParams] = useSearchParams();

  const [showActivateCommunityModal, setShowActivateCommunityModal] = useState<boolean>(false);
  const [showPaymentPendingModal, setShowPaymentPendingModal] = useState<boolean>(false);
  const [showNewPartnerModal, setShowNewPartnerModal] = useState<boolean>(false);

  function canActivateCommunity() {
    return !isOnboarding && user?.staffRole === StaffRole.OWNER && !!university?.branding && university?.shouldActivateCommunity();
  }

  useEffect(() => {
    setShowActivateCommunityModal(canActivateCommunity());
  }, [user?.staffRole, university?.partner]);

  useEffect(() => {
    const checkoutStatus = searchParams.get('checkoutStatus');
    if (checkoutStatus === 'success') {
      setShowActivateCommunityModal(false);
      setShowPaymentPendingModal(true);
    } else if (checkoutStatus === 'cancelled') {
      setShowActivateCommunityModal(canActivateCommunity());
    }

    if (searchParams.get('showModal') === 'activateCommunity') {
      setShowActivateCommunityModal(canActivateCommunity());
    }
  }, [searchParams]);

  useEffect(() => {
    if (showPaymentPendingModal) {
      if (university?.paymentState === 'paying') {
        setShowPaymentPendingModal(false);
        setShowNewPartnerModal(true);
      } else if (university?.paymentState === 'notPaying') {
        setShowPaymentPendingModal(false);
        setShowActivateCommunityModal(true);
      }
    }
  }, [showPaymentPendingModal, university?.paymentState]);

  return (
    <>
      {showActivateCommunityModal && (
        <ActivateYourCommunityModal
          onClose={(becamePartner) => {
            setShowActivateCommunityModal(false);
            setShowNewPartnerModal(becamePartner);
            setSearchParams({});
          }}
        />
      )}
      {showPaymentPendingModal && (
        <ModalShell onClose={() => {}}>
          <LoadingIndicator size={'md'} />
        </ModalShell>
      )}
      {showNewPartnerModal && <NewPartnerModal onClose={() => setShowNewPartnerModal(false)} />}
    </>
  );
}
