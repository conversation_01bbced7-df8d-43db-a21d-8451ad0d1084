import {ReactNode, useEffect, useState} from 'react';
import {AlgoliaFilters} from './AlgoliaFiltersSheet';
import {Checkbox, checkboxClasses, List, ListItem, Stack} from '@mui/joy';
import {useLocation} from 'react-router-dom';
import {useUser} from '../providers/UserProvider';
import {influxWriter, newAppMetric} from '@creator-campus/common';

interface Props<T> {
  attribute: string;
  options: T[];
  getLabel: (option: T) => ReactNode;
  getValue: (option: T) => string;
  filters: AlgoliaFilters;
  onFilterChange: (key: string, filter: string | string[] | null) => void;
}

export function AlgoliaCheckboxes<T>({attribute, options, getLabel, getValue, filters, onFilterChange}: Props<T>) {
  const [selectedItems, setSelectedItems] = useState<T[]>([]);
  const {pathname} = useLocation();
  const {user} = useUser();

  useEffect(() => {
    const filterString = filters[attribute] || '';
    setSelectedItems(options.filter((option) => filterString.includes(getValue(option))));
  }, [filters, attribute, options]);

  return (
    <List
      sx={{
        p: 0,
        [`& .${checkboxClasses.root}`]: {
          mr: 'auto',
          flexGrow: 1,
          alignItems: 'center',
          flexDirection: 'row-reverse',
        },
      }}
    >
      {options.map((option) => {
        const checked = selectedItems.includes(option);

        return (
          <ListItem
            key={getValue(option)}
            variant='plain'
            sx={{borderRadius: 'sm'}}
            {...(checked && {variant: 'soft', color: 'neutral'})}
          >
            <Checkbox
              label={
                <Stack
                  alignItems={'center'}
                  direction={'row'}
                  spacing={0.5}
                >
                  {getLabel(option)}
                </Stack>
              }
              checked={checked}
              size={'sm'}
              overlay
              onChange={(e) => {
                const newSelectedItems = e.target.checked ? [...selectedItems, option] : selectedItems.filter((item) => item !== option);

                setSelectedItems(newSelectedItems);
                onFilterChange(attribute, newSelectedItems.length === 0 ? null : newSelectedItems.map((item) => getValue(item)));

                if (user) {
                  const metric = newAppMetric(user).intField('filterToggleCount', 1).tag('value', `${e.target.checked}`).tag('page', pathname).tag('attribute', attribute).tag('type', 'checkbox');
                  influxWriter.writePoint(metric);
                }
              }}
            />
          </ListItem>
        );
      })}
    </List>
  );
}
