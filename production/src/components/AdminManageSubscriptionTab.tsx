import {Link, Stack} from '@mui/joy';
import {PartnershipData, SubscriptionTier, University} from '@creator-campus/common';
import {LoadingIndicator, useSnackbar} from '@creator-campus/common-components';
import Typography from '@mui/joy/Typography';
import {useEffect, useState} from 'react';
import {ContentCopy} from '@mui/icons-material';
import {SubscriptionTierCard} from './SubscriptionTierCard';

interface Props {
  university: University;
}

export function AdminManageSubscriptionTab({university}: Props) {
  const [partnershipData, setPartnershipData] = useState<PartnershipData | null>(null);
  const {showSnackbar} = useSnackbar();

  useEffect(() => {
    university.fetchPartnershipData().then(setPartnershipData);
  }, [university]);

  function EmailLink() {
    return (
      <Link
        onClick={async () => {
          await navigator.clipboard.writeText('<EMAIL>');
          showSnackbar('Email copied to clipboard.');
        }}
      >
        <ContentCopy sx={{fontSize: '14px'}} />
        <EMAIL>
      </Link>
    );
  }

  return (
    <Stack>
      <Typography level={'title-sm'}>Manage subscription</Typography>
      <Typography
        level='body-sm'
        sx={{display: 'flex', flexWrap: 'wrap', alignItems: 'center', columnGap: 0.5}}
      >
        Please contact <EmailLink /> if you would like to make changes to your subscription.
      </Typography>

      <Stack mt={1}>
        {university.isPaying() && <Typography level={'body-sm'}>Next payment: {`£${partnershipData?.priceGbp} on ${partnershipData?.getNextPaymentDate()?.toDateString() || 'unknown date'}`}</Typography>}
        {university.partner && <Typography level={'body-sm'}>Partner since: {university.partnerSince?.toDateString()}</Typography>}
      </Stack>

      {partnershipData ? (
        <Stack
          direction={{xs: 'column', sm: 'row'}}
          spacing={2}
          mt={2}
          alignItems={'start'}
        >
          {!university.partner && !university.almostReachedUserLimit && (
            <SubscriptionTierCard
              tier={SubscriptionTier.INACTIVE}
              partnershipData={partnershipData}
            />
          )}
          <SubscriptionTierCard
            tier={SubscriptionTier.PARTNER}
            partnershipData={partnershipData}
          />
        </Stack>
      ) : (
        <LoadingIndicator size={'md'} />
      )}
    </Stack>
  );
}
