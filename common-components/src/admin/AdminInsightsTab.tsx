import {Box} from '@mui/joy';
import {useEffect, useState} from 'react';
import {average, collection, count, getAggregateFromServer, getDocs, query, sum, where} from 'firebase/firestore';
import FormLabel from '@mui/joy/FormLabel';
import Button from '@mui/joy/Button';
import Select from '@mui/joy/Select';
import Option from '@mui/joy/Option';
import Stack from '@mui/joy/Stack';
import {LoadingIndicator} from '../LoadingIndicator';
import {ProjectInsightsCard} from './ProjectInsightsCard';
import {PeopleInsightsCard} from './PeopleInsightsCard';
import {OpportunityInsightsCard} from './OpportunityInsightsCard';
import {firestore, PieChartData, University, UniversityMetrics, User, UniversityConverter, Project} from '@creator-campus/common';

interface Props {
  // Omitting a uniId provides the option to select one from a dropdown list
  uniId?: string;
}

export function AdminInsightsTab({uniId}: Props) {
  const [universityIds, setUniversityIds] = useState<string[] | null>(uniId ? [uniId] : null);
  const [universityId, setUniversityId] = useState<string>(uniId || 'All');

  const [metrics, setMetrics] = useState<UniversityMetrics | null>(null);
  const [numOpportunitiesOpen, setNumOpportunitiesOpen] = useState<number | null>(null);

  const [numProjectsData, setNumProjectsData] = useState<PieChartData[] | null>(null);
  const [numOpenOppsData, setNumOpenOppsData] = useState<PieChartData[] | null>(null);
  const [numUsersData, setNumUsersData] = useState<PieChartData[] | null>(null);

  useEffect(() => {
    if (!uniId) {
      getDocs(collection(firestore(), University.collectionName)).then((snapshot) => {
        setUniversityIds(snapshot.docs.map((docSnap) => docSnap.id).sort());
      });
    }
  }, []);

  useEffect(() => {
    if (!universityIds) {
      return;
    }

    if (universityId === 'All') {
      // Fetch metrics for each university and add together
      const metricsPromises = universityIds.map((uniId) => UniversityMetrics.fetchForUniversity(uniId)!);
      Promise.all(metricsPromises).then((allMetrics) => {
        setMetrics(allMetrics.reduce((a, b) => (a || UniversityMetrics.defaults()).add(b || UniversityMetrics.defaults()))!);
      });
    } else {
      UniversityMetrics.fetchForUniversity(universityId).then((m) => {
        setMetrics(m || UniversityMetrics.defaults());
      });
    }

    Promise.all(
      universityIds.map(async (uniId) => {
        const result = await getAggregateFromServer(query(collection(firestore(), Project.collectionName), where('universityId', '==', uniId), where('fake', '==', false), where('hidden', '==', false)), {
          numProjects: count(),
          openOpportunities: sum('numOpenOpportunities'),
        });

        const {numProjects, openOpportunities} = result.data();
        return {uniId, numProjects, openOpportunities};
      }),
    ).then((unisArray) => {
      setNumProjectsData(unisArray.map((e) => ({name: e.uniId, value: e.numProjects, colour: undefined})));
      setNumOpenOppsData(unisArray.map((e) => ({name: e.uniId, value: e.openOpportunities, colour: undefined})));
    });

    getAggregateFromServer(query(collection(firestore(), Project.collectionName), ...(universityId === 'All' ? [] : [where('universityId', '==', universityId)])).withConverter(new UniversityConverter()), {
      numProjects: count(),
      openOpportunities: sum('numOpenOpportunities'),
      avgOpenOpportunities: average('numOpenOpportunities'),
    }).then((snapshot) => {
      const data = snapshot.data();
      setNumOpportunitiesOpen(data.openOpportunities);
    });

    Promise.all(
      universityIds.map(async (uniId) => {
        const result = await getAggregateFromServer(query(collection(firestore(), User.collectionName), where('universityId', '==', uniId)), {
          numUsers: count(),
        });
        return {uniId, numUsers: result.data().numUsers};
      }),
    ).then((usersArray) => {
      setNumUsersData(usersArray.map((e) => ({name: e.uniId, value: e.numUsers, colour: undefined})));
    });
  }, [universityIds, universityId]);

  if (!metrics || !universityIds) {
    return <LoadingIndicator />;
  }

  return (
    <Stack spacing={3}>
      {!uniId && (
        <Box>
          <FormLabel>University</FormLabel>
          <Box sx={{display: 'flex', alignItems: 'center', gap: 1, mt: 0.5}}>
            <Button
              variant={universityId === 'All' ? 'solid' : 'outlined'}
              onClick={() => setUniversityId('All')}
            >
              All
            </Button>
            <Select
              sx={{width: '250px', zIndex: 100}}
              value={universityId}
              placeholder='Select university...'
              onChange={(_, value) => {
                if (value) {
                  setUniversityId(value);
                }
              }}
              size='sm'
            >
              {universityIds.map((uni) => (
                <Option
                  key={uni}
                  value={uni}
                >
                  {uni}
                </Option>
              ))}
            </Select>
          </Box>
        </Box>
      )}
      <Box sx={{display: 'flex', flexDirection: {xs: 'column', sm: 'row'}, gap: '20px'}}>
        <ProjectInsightsCard
          universityId={universityId}
          setUniversityId={setUniversityId}
          numProjectsData={numProjectsData}
        />
        <PeopleInsightsCard
          universityId={universityId}
          setUniversityId={setUniversityId}
          numUsersData={numUsersData}
        />
        <OpportunityInsightsCard
          universityId={universityId}
          setUniversityId={setUniversityId}
          metrics={metrics}
          numOpportunitiesOpen={numOpportunitiesOpen}
          numOpenOppsData={numOpenOppsData}
        />
      </Box>
    </Stack>
  );
}
