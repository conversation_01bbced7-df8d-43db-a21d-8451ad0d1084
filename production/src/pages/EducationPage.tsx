import {useParams} from 'react-router-dom';
import MarkdownBrowser from '../components/MarkdownBrowser';
import {PageHeader, PageShell} from '@creator-campus/common-components';

export default function EducationPage() {
  const params = useParams();

  return (
    <>
      <PageShell>
        <PageHeader
          title='Education'
          subtitle='Resources to help you get started.'
        />
        <MarkdownBrowser
          article={params.article!}
          path={params.path}
        />
      </PageShell>
    </>
  );
}
