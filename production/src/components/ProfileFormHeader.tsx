import Typography from '@mui/joy/Typography';
import Box from '@mui/joy/Box';

interface Props {
  variant: 'default' | 'onboarding';
}

export function ProfileFormHeader({variant}: Props) {
  return (
    <Box sx={{mb: 2}}>
      <Typography level='title-md'>About you</Typography>
      <Typography level='body-sm'>
        {variant === 'onboarding' ? 'A quality profile helps everyone get the most out of the community - take a couple minutes to finish yours so we can build something great together. Your profile will be submitted to the Creator Campus team for review.' : 'Edit your profile to let other members know who you are.'}
      </Typography>
    </Box>
  );
}
