{"firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "functions": {"source": "functions/webpack", "predeploy": ["yarn buildFunctions"], "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log"]}, "hosting": [{"site": "creator-campus-app", "public": "production/dist", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}]}, {"site": "creator-campus-demo", "public": "production/dist", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}]}, {"site": "creator-campus-admin", "public": "admin/dist", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}]}], "emulators": {"auth": {"port": 9099}, "functions": {"port": 5001}, "firestore": {"port": 8080}, "ui": {"enabled": true, "host": "127.0.0.1", "port": 4000}, "hosting": {"port": 5002}, "storage": {"port": 9199}, "pubsub": {"port": 8085}, "extensions": {"port": 5001}}, "storage": {"rules": "storage.rules"}, "extensions": {"delete-user-data": "firebase/delete-user-data@0.1.24", "firestore-algolia-search-people": "algolia/firestore-algolia-search@1.2.7", "firestore-algolia-search-projects": "algolia/firestore-algolia-search@1.2.7", "firestore-send-email": "firebase/firestore-send-email@0.1.37", "storage-resize-images": "firebase/storage-resize-images@0.2.8"}}