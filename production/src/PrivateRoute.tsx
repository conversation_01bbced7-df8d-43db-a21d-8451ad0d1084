import {Navigate, useLocation} from 'react-router-dom';

import {ReactNode} from 'react';
import {useAuth, useUser} from '@creator-campus/common-components';
import {onboardingStages} from './model/onboardingConfig.tsx';
import {LoadingPage} from './pages/LoadingPage.tsx';

interface Props {
  children: ReactNode;
}

export default function PrivateRoute({children}: Props) {
  const {loadingUser, user, university} = useUser();
  const {loadingAuth, currentUser} = useAuth();
  const {pathname} = useLocation();

  if (loadingAuth || loadingUser) {
    return <LoadingPage />;
  }

  if (!currentUser) {
    return (
      <Navigate
        to='/login'
        replace
      />
    );
  }

  if (!user || !university) {
    return <LoadingPage />;
  }

  const shouldGoToOnboarding = onboardingStages.some((stage) => stage.condition(user, university));

  if (pathname !== '/onboarding' && shouldGoToOnboarding) {
    return (
      <Navigate
        to='/onboarding'
        replace
      />
    );
  } else if (pathname === '/onboarding' && !shouldGoToOnboarding) {
    return (
      <Navigate
        to='/startups/explore'
        replace
      />
    );
  }

  return children;
}
