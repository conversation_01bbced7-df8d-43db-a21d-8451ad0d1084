import Card from '@mui/joy/Card';
import Box from '@mui/joy/Box';
import Typography from '@mui/joy/Typography';
import Divider from '@mui/joy/Divider';
import ResetPasswordModal from './modals/ResetPasswordModal.tsx';
import Button from '@mui/joy/Button';
import {Delete, RestartAlt} from '@mui/icons-material';
import {demoMode, User, UserNotifications} from '@creator-campus/common';
import {useSnackbar, useProfileString, useUser} from '@creator-campus/common-components';
import Stack from '@mui/joy/Stack';
import {useState} from 'react';
import DeleteProfileModal from './modals/DeleteProfileModal.tsx';
import Select from '@mui/joy/Select';
import {updateDoc} from 'firebase/firestore';
import {Option} from '@mui/joy';

export const notificationsSettings = [
  {
    title: 'New discussion activity',
    description: 'Receive regular summaries of recent discussion posts.',
    value: (user: User) => user.notifications.discussionNewPosts,
    getUpdateObject: (newValue: string) => ({
      'notifications.discussionNewPosts': newValue as UserNotifications['discussionNewPosts'],
    }),
    options: [
      {title: 'Daily', value: 'daily'},
      {title: 'Weekly', value: 'weekly'},
      {title: 'Off', value: 'off'},
    ],
  },
  {
    title: 'Discussion post replies',
    description: 'Get notified when someone replies to one of your discussion posts.',
    value: (user: User) => user.notifications.discussionPostReplies,
    getUpdateObject: (newValue: string) => ({
      'notifications.discussionPostReplies': newValue as UserNotifications['discussionPostReplies'],
    }),
    options: [
      {title: 'Real time', value: 'realTime'},
      {title: 'Off', value: 'off'},
    ],
  },
];

export default function ProfileAccountSettingsTab() {
  const [resetPasswordModalOpen, setResetPasswordModalOpen] = useState<boolean>(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState<boolean>(false);

  const {user} = useUser();
  const {showErrorSnackbar} = useSnackbar();

  return (
    <>
      <Stack
        spacing={2}
        sx={{
          display: 'flex',
          maxWidth: 800,
          width: {xs: '100%', sm: '100%', md: '100%', lg: '100%', xl: '800px'},
          mx: 'auto',
          px: {xs: 2, md: 6},
          pb: {xs: 2, md: 3},
        }}
      >
        <Card>
          <Stack>
            <Typography level='title-md'>Email notifications</Typography>
            <Typography level='body-sm'>Choose what you want to get updates about.</Typography>
          </Stack>
          <Divider />
          {user &&
            notificationsSettings.map((item) => (
              <Stack
                key={item.title}
                spacing={2}
                direction={'row'}
                alignItems={'center'}
                justifyContent={'space-between'}
                mt={0.3}
              >
                <Stack>
                  <Typography level={'title-sm'}>{item.title}</Typography>
                  <Typography level={'body-sm'}>{item.description}</Typography>
                </Stack>
                <Select
                  variant={'plain'}
                  sx={{fontSize: 14, minWidth: 110}}
                  value={item.value(user)}
                  onChange={async (_, newValue) => {
                    if (newValue) {
                      await updateDoc(
                        user._doc,
                        //@ts-ignore
                        item.getUpdateObject(newValue),
                      ).catch((e) => showErrorSnackbar(e, 'Error updating preferences.'));
                    }
                  }}
                >
                  {...item.options.map((o) => (
                    <Option value={o.value}>
                      <Typography level={'body-sm'}>{o.title}</Typography>
                    </Option>
                  ))}
                </Select>
              </Stack>
            ))}
        </Card>
        <Card>
          <Stack>
            <Typography level='title-md'>Account</Typography>
            <Typography level='body-sm'>Keep your account information up to date.</Typography>
          </Stack>
          <Divider />
          <Box sx={{minWidth: 30}}>
            <Button
              disabled={demoMode}
              size='sm'
              variant='outlined'
              color='neutral'
              endDecorator={<RestartAlt />}
              onClick={() => setResetPasswordModalOpen(true)}
            >
              Reset your password
            </Button>
            <ResetPasswordModal
              open={resetPasswordModalOpen}
              onClose={() => setResetPasswordModalOpen(false)}
            />
          </Box>
          <Box sx={{minWidth: 30}}>
            <Button
              disabled={demoMode}
              size='sm'
              variant='outlined'
              color='danger'
              endDecorator={<Delete />}
              onClick={() => setDeleteModalOpen(true)}
            >
              {useProfileString().delete_profile}
            </Button>
            {deleteModalOpen && <DeleteProfileModal onClose={() => setDeleteModalOpen(false)} />}
          </Box>
        </Card>
      </Stack>
    </>
  );
}
