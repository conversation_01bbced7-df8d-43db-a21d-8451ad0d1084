{"name": "cloud-functions", "version": "1.0.0", "private": true, "scripts": {"deploy": "firebase deploy --only functions --project creator-campus-app && firebase deploy --only functions --project creator-campus-demo"}, "engines": {"node": "20"}, "main": "dist/index.js", "dependencies": {"@dhaiwat10/react-link-preview": "^1.15.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fontsource/inter": "^5.1.1", "@genkit-ai/googleai": "^0.9.12", "@google-analytics/data": "^4.12.0", "@influxdata/influxdb-client": "^1.35.0", "@mui/base": "^5.0.0-beta.40-0", "@mui/icons-material": "^6.4.1", "@mui/joy": "^5.0.0-beta.51", "@mui/material": "^6.4.1", "@sentry/node": "^9.30.0", "algoliasearch": "^5.20.0", "axios": "^1.7.9", "common": "*", "date-fns": "^3.6.0", "deepmerge": "^4.3.1", "dotenv": "^16.4.7", "firebase": "^11.2.0", "firebase-admin": "^13.0.2", "firebase-functions": "^6.2.0", "genkit": "^0.9.12", "lodash": "^4.17.21", "marked": "^13.0.3", "node-fetch": "^3.3.2", "normalize-url": "^8.0.1", "npm-check": "^6.0.1", "react": "^18.3.1", "react-confetti-explosion": "^2.1.2", "react-dom": "^18.3.1", "react-icons": "^5.4.0", "react-infinite-scroll-component": "^6.1.0", "react-instantsearch": "^7.15.1", "react-markdown": "^9.0.3", "react-router-dom": "^6.28.2", "recharts": "^2.15.0", "rehype-raw": "^7.0.0", "stripe": "^18.2.1", "ts-node": "^10.9.2", "url-metadata": "^4.1.1", "uuid": "^11.0.4"}, "devDependencies": {"@rollup/plugin-json": "^6.1.0", "@types/node": "^22.10.7", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "prettier": "^3.4.2", "rollup": "^4.34.0", "typescript": "^5.7.3"}}