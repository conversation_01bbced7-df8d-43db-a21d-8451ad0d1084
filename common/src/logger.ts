interface LogFn {
  (message?: any, ...optionalParams: any[]): void;
}

export interface Logger {
  debug: LogFn;
  warn: LogFn;
  error: LogFn;
}

type LogLevel = 'debug' | 'warn' | 'error' | 'silent';

const NO_OP: LogFn = (_message?: any, ..._optionalParams: any[]) => {};

function getTimestamp() {
  return new Date().toISOString();
}

function getCallerInfo() {
  const stack = new Error().stack;
  if (!stack) {
    return 'unknown caller';
  }

  const stackLines = stack.split('\n');
  const callerLine = stackLines[3]?.trim();

  if (!callerLine) {
    return 'unknown caller';
  }

  // Extract file and line information
  const match = callerLine.match(/at (.+) \((.+):(\d+):(\d+)\)/) || callerLine.match(/at (.+):(\d+):(\d+)/);
  if (match) {
    if (match.length === 5) {
      // Matches "at functionName (file:line:column)"
      return `${match[1]}:${match[3]}`;
    } else if (match.length === 4) {
      // Matches "at file:line:column"
      return match[1];
    }
  }

  return callerLine;
}

class ConsoleLogger implements Logger {
  readonly debug: LogFn;
  readonly warn: LogFn;
  readonly error: LogFn;

  constructor(options?: {level?: LogLevel}) {
    const {level} = options || {};

    if (level === 'silent') {
      this.error = NO_OP;
      this.warn = NO_OP;
      this.debug = NO_OP;
      return;
    }

    this.error = this.createLogger(console.error, 'ERROR');

    if (level === 'error') {
      this.warn = NO_OP;
      this.debug = NO_OP;
      return;
    }

    this.warn = this.createLogger(console.warn, 'WARN');

    if (level === 'warn') {
      this.debug = NO_OP;
      return;
    }

    this.debug = this.createLogger(console.log, 'DEBUG');
  }

  private createLogger(consoleFn: LogFn, level: string): LogFn {
    return (message?: any, ...optionalParams: any[]) => {
      const timestamp = getTimestamp();
      const callerInfo = getCallerInfo();
      consoleFn(`[${timestamp}] [${level}] [${callerInfo}]`, message, ...optionalParams);
    };
  }
}

// Export a logger instance
export const logger = new ConsoleLogger({
  level: process.env.NODE_ENV === 'production' ? 'silent' : 'debug',
});
