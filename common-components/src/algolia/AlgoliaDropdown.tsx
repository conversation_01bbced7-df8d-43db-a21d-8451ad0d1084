import Autocomplete from '@mui/joy/Autocomplete';
import FormControl from '@mui/joy/FormControl';
import {useEffect, useState} from 'react';
import {AlgoliaFilters} from './AlgoliaFiltersSheet';
import {useLocation} from 'react-router-dom';
import {influxWriter, newAppMetric} from '@creator-campus/common';
import {useUser} from '../providers/UserProvider';

interface Props {
  attribute: string;
  options: string[];
  filters: AlgoliaFilters;
  onFilterChange: (key: string, filter: string | string[] | null) => void;
}

export function AlgoliaDropdown({attribute, options, filters, onFilterChange}: Props) {
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const {pathname} = useLocation();
  const {user} = useUser();

  function handleChange(_: any, selectedItems: string[]) {
    onFilterChange(attribute, selectedItems.length === 0 ? null : selectedItems);

    if (user) {
      const metric = newAppMetric(user).intField('filterToggleCount', 1).tag('value', selectedItems.join(',')).tag('page', pathname).tag('attribute', attribute).tag('type', 'dropdown');
      influxWriter.writePoint(metric);
    }
  }

  useEffect(() => {
    const filterString = filters[attribute] || '';
    setSelectedItems(options.filter((option) => filterString.includes(option)));
  }, [filters]);

  return (
    <FormControl>
      <Autocomplete
        multiple
        type='search'
        placeholder={selectedItems.length === 0 ? 'Select' : ''}
        options={options}
        value={selectedItems}
        onChange={handleChange}
        size='sm'
      />
    </FormControl>
  );
}
