import {AlgoliaFilters, UpdateAlgoliaFilters} from '@creator-campus/common-components';
import {UniversityFilterCard} from './UniversityFilterCard.tsx';
import {Stack} from '@mui/joy';

interface Props {
  universityId: string;
  filters: AlgoliaFilters;
  onFilterChange: UpdateAlgoliaFilters;
  sx?: object;
}

export function UniversityFilterCardsRow({universityId, filters, onFilterChange, sx}: Props) {
  return (
    <Stack
      direction={'row'}
      spacing={1}
      sx={{mb: 1, ...sx}}
    >
      <UniversityFilterCard
        variant={'myUniversity'}
        selected={filters['universityId'] === `:"${universityId}"`}
        onClick={() => {
          onFilterChange('universityId', `:"${universityId}"`);
        }}
      />
      <UniversityFilterCard
        variant={'global'}
        selected={!filters['universityId']}
        onClick={() => {
          onFilterChange('universityId', null);
        }}
      />
    </Stack>
  );
}
