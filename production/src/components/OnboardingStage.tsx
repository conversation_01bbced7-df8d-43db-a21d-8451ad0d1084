import {Box, Stack} from '@mui/joy';
import {ReactNode} from 'react';
import {OnboardingStageContentOptions} from '../model/onboardingConfig';

interface Props {
  contentOptions: OnboardingStageContentOptions;
  children: ReactNode;
}

export function OnboardingStage({contentOptions = {centred: false}, children}: Props) {
  if (contentOptions.centred) {
    return (
      <Stack
        height={'80%'}
        justifyContent={'center'}
      >
        {children}
      </Stack>
    );
  }

  return (
    <Box
      pt={2}
      pb={12}
    >
      {children}
    </Box>
  );
}
