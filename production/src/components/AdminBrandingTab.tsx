import FormLabel from '@mui/joy/FormLabel';
import Stack from '@mui/joy/Stack';
import FormControl from '@mui/joy/FormControl';
import {Box, Input, List, ListItem} from '@mui/joy';
import {useEffect, useState} from 'react';
import {logger, openLink, UniversityBranding, UniversityExternalLink} from '@creator-campus/common';
import {useSnackbar, useUser, useScreenWidth, LoadingIndicator} from '@creator-campus/common-components';
import Button from '@mui/joy/Button';
import {ColorLens, Delete, OpenInNew, School} from '@mui/icons-material';
import {ColorPickerBox} from './ColorPickerBox.tsx';
import {UniversityLogoWithUpload} from './UniversityLogoWithUpload.tsx';
import Typography from '@mui/joy/Typography';
import {Timestamp, updateDoc} from 'firebase/firestore';
import {normaliseUrl} from '../utils.tsx';
import _ from 'lodash';
import IconButton from '@mui/joy/IconButton';
import Tooltip from '@mui/joy/Tooltip';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';

interface Props {
  onNext?: () => void;
  buttonText?: string;
  buttonPosition?: 'start' | 'end';
}

export function AdminBrandingTab({onNext, buttonText, buttonPosition = 'start'}: Props) {
  const MIN_NAME_LEN = 3;
  const MAX_NAME_LEN = 50;
  const MAX_SIDEBAR_LINKS = 5;

  const {university} = useUser();
  const {showSnackbar, showErrorSnackbar} = useSnackbar();
  const screenWidth = useScreenWidth();

  if (!university) {
    return <></>;
  }

  const [loading, setLoading] = useState<boolean>(false);
  const [name, setName] = useState<string>(university.name);
  const [branding, setBranding] = useState<UniversityBranding>(university.branding || {logoUpdatedAt: null, primaryColor: '#000000'});
  const [sidebarLinks, setSidebarLinks] = useState<UniversityExternalLink[]>(university.sidebarLinks || []);
  const [chosenLogo, setChosenLogo] = useState<File | null>(null);

  const [errors, setErrors] = useState<{
    name?: string;
    logo?: string;
    sidebarLinks?: string;
  }>({});

  async function handleSubmit() {
    const normalisedName = name.trim();
    const normalisedBranding = chosenLogo ? {...branding, logoUpdatedAt: Timestamp.now()} : branding;
    const normalisedSidebarLinks = normaliseSidebarLinks();

    await updateDoc(university!.doc(), {
      branding: normalisedBranding,
      name: normalisedName,
      sidebarLinks: normalisedSidebarLinks,
    });

    if (chosenLogo) {
      await university!.updateLogo(chosenLogo);
      setChosenLogo(null);
    }
  }

  function isLinkFull(link: UniversityExternalLink) {
    return link.label !== '' && link.href !== '';
  }

  function isLinkEmpty(link: UniversityExternalLink) {
    return link.label === '' && link.href === '';
  }

  function normaliseSidebarLinks() {
    const fullLinks = sidebarLinks.filter(isLinkFull);
    return fullLinks.map((link) => ({label: link.label.trim(), href: normaliseUrl(link.href)!}));
  }

  function validateForm() {
    const newErrors: typeof errors = {};

    if (name.trim().length === 0) {
      newErrors.name = 'Name is required.';
    } else if (name.trim().length < MIN_NAME_LEN) {
      newErrors.name = `Name must be at least ${MIN_NAME_LEN} characters.`;
    } else if (name.trim().length > MAX_NAME_LEN) {
      newErrors.name = `Name must be less than ${MAX_NAME_LEN} characters.`;
    }

    if (!university?.branding?.logoUpdatedAt && !chosenLogo) {
      showSnackbar('Please upload a logo.', 'danger');
      return false;
    }

    if (!sidebarLinks.every((link) => isLinkFull(link) || isLinkEmpty(link))) {
      newErrors.sidebarLinks = 'All sidebar links must have a label and URL.';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }

  function addSidebarLink() {
    if (!canAddSidebarLink()) {
      return;
    }

    setSidebarLinks((prev) => [...prev, {label: '', href: ''}]);
  }

  function removeEmptyTrailingSidebarLinks() {
    setSidebarLinks((prev) => {
      let newLinks = [...prev];

      while (newLinks.length > 0 && isLinkEmpty(newLinks[newLinks.length - 1])) {
        newLinks.pop();
      }

      return newLinks;
    });
  }

  function canAddSidebarLink() {
    if (sidebarLinks.length >= MAX_SIDEBAR_LINKS) {
      return false;
    }

    if (sidebarLinks.length === 0) {
      return true;
    }

    const lastLink = sidebarLinks[sidebarLinks.length - 1];
    return lastLink.label !== '' || lastLink.href !== '';
  }

  useEffect(() => {
    if (sidebarLinks.length === 0) {
      return;
    }

    const lastLink = sidebarLinks[sidebarLinks.length - 1];
    if (isLinkEmpty(lastLink)) {
      removeEmptyTrailingSidebarLinks();
    }
  }, [sidebarLinks]);

  const hasBeenEdited = branding.primaryColor !== university.branding?.primaryColor || name !== university.name || !_.isEqual(sidebarLinks, university.sidebarLinks) || !!chosenLogo;

  return (
    <Stack
      spacing={4}
      sx={{maxWidth: 750}}
    >
      {/* University name and logo */}
      <Stack spacing={2}>
        <Stack>
          <Typography level={'title-md'}>University branding</Typography>
          <Typography level={'body-sm'}>
            Customise how your university looks on Creator Campus. Please use a logo following these guidelines:
            <List
              marker={'disc'}
              sx={{'--ListItem-paddingX': '0px'}}
            >
              <ListItem>
                <Typography level={'body-sm'}>Transparent background</Typography>
              </ListItem>
              <ListItem>
                <Typography level={'body-sm'}>Crop padding around logo</Typography>
              </ListItem>
            </List>
          </Typography>
        </Stack>

        <Stack
          direction={screenWidth < 500 ? 'column' : 'row'}
          spacing={2.5}
          alignItems={'center'}
        >
          <UniversityLogoWithUpload
            university={university}
            selectedImageUrl={chosenLogo ? URL.createObjectURL(chosenLogo) : undefined}
            onImageSelected={setChosenLogo}
          />

          <Stack spacing={1}>
            <Stack
              direction={'row'}
              spacing={1.5}
              alignItems={'center'}
            >
              <School sx={{fontSize: 20}} />
              <FormControl error={!!errors.name}>
                <Input
                  placeholder={university.name}
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  sx={{width: 300}}
                />
                {errors.name && (
                  <Typography
                    level='body-sm'
                    color='danger'
                  >
                    {errors.name}
                  </Typography>
                )}
              </FormControl>
            </Stack>
            <Stack
              direction={'row'}
              spacing={1.5}
              alignItems={'center'}
            >
              <ColorLens sx={{fontSize: 20}} />
              <ColorPickerBox
                color={branding.primaryColor}
                onChange={(color) => setBranding({...branding, primaryColor: color})}
              />
              <Tooltip
                sx={{maxWidth: 300}}
                enterTouchDelay={0}
                title='Pick your brand colour.'
                variant='solid'
              >
                <InfoOutlinedIcon fontSize='large' />
              </Tooltip>
            </Stack>
          </Stack>
        </Stack>
      </Stack>

      {/* Welcome email */}
      <Stack>
        <Typography level={'title-md'}>Customise welcome email</Typography>
        <Typography level={'body-sm'}>Fill out the form below if you want to customise the welcome email sent to new members of your community.</Typography>
        <Button
          onClick={() => openLink('https://forms.gle/i7xFDqfXMmSKqkxo8')}
          sx={{width: 150, mt: 1}}
        >
          Edit email
          <OpenInNew sx={{fontSize: '18px', ml: 0.5}} />
        </Button>
      </Stack>

      {/* Sidebar links */}
      <Stack
        spacing={1}
        sx={{maxWidth: 800}}
      >
        <Stack>
          <Typography level={'title-md'}>Sidebar links</Typography>
          <Typography level={'body-sm'}>You can add additional links to the platform sidebar - use this as an opportunity to signpost your enterprise website, events calendar, or anything else important for your members.</Typography>
        </Stack>

        {errors.sidebarLinks && (
          <Typography
            level='body-sm'
            color='danger'
            sx={{mb: 1}}
          >
            {errors.sidebarLinks}
          </Typography>
        )}

        {[...sidebarLinks, ...(canAddSidebarLink() ? [{label: '', href: ''}] : [])].map((link, i) => (
          <Stack
            key={i}
            direction={'row'}
            spacing={1}
          >
            {/* Label */}
            <FormControl sx={{width: {xs: screenWidth * 0.35, sm: '100%'}}}>
              <FormLabel>Label</FormLabel>
              <Input
                placeholder={`Entrepreneurship at ${university.name}`}
                value={link.label}
                size={'sm'}
                onChange={(e) => {
                  const newLabel = e.target.value;

                  if (i === sidebarLinks.length) {
                    addSidebarLink();
                  }

                  setSidebarLinks((prev) => prev.map((l, j) => (i === j ? {label: newLabel, href: l.href} : l)));
                }}
              />
            </FormControl>

            {/* Url */}
            <FormControl sx={{width: {xs: screenWidth * 0.4, sm: '100%'}}}>
              <FormLabel>Url</FormLabel>
              <Input
                placeholder={`https://entrepreneurship.${university.domain}/`}
                value={link.href}
                size={'sm'}
                onChange={(e) => {
                  const newHref = e.target.value;

                  if (i === sidebarLinks.length) {
                    addSidebarLink();
                  }

                  setSidebarLinks((prev) => prev.map((l, j) => (i === j ? {label: l.label, href: newHref} : l)));
                }}
              />
            </FormControl>

            {/* Delete button */}
            <IconButton
              size='sm'
              variant='soft'
              color='neutral'
              onClick={() => setSidebarLinks((prev) => prev.filter((_l, j) => i !== j))}
              sx={{alignSelf: 'end', visibility: isLinkEmpty(link) ? 'hidden' : 'visible'}}
            >
              <Delete />
            </IconButton>
          </Stack>
        ))}
      </Stack>

      <Box sx={{alignSelf: buttonPosition}}>
        <Button
          disabled={loading || !hasBeenEdited}
          onClick={async () => {
            setLoading(true);

            if (validateForm()) {
              await handleSubmit()
                .then(() => {
                  if (onNext) {
                    onNext();
                  } else {
                    showSnackbar('Changes saved.', 'success');
                  }
                })
                .catch((e) => {
                  logger.error(e);
                  showErrorSnackbar(e, 'Error saving changes.');
                });
            }

            setLoading(false);
          }}
        >
          {loading ? <LoadingIndicator size={'sm'} /> : buttonText ? buttonText : 'Save'}
        </Button>
      </Box>
    </Stack>
  );
}
