import Chip from '@mui/joy/Chip';
import {Persona} from '@creator-campus/common';

interface Props {
  persona: Persona;
  size?: 'sm' | 'md' | 'lg';
}

export default function PersonaChip({persona, size = 'md'}: Props) {
  return (
    <Chip
      size={size}
      variant='soft' // @ts-ignore
      color={persona.themedColour}
      startDecorator={<persona.icon />}
    >
      {persona.label}
    </Chip>
  );
}
