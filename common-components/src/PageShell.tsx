import Box from '@mui/joy/Box';
import {ReactNode, RefObject} from 'react';
import {AlertBanner} from './AlertBanner';
import {Footer} from './Footer';
import {useUser} from './providers/UserProvider';
import {LoadingIndicator} from './LoadingIndicator';
import {useNavigate} from 'react-router-dom';
import {Info, Warning} from '@mui/icons-material';
import {useActivateCommunityString} from './providers/TextProvider';
import {StaffRole} from '@creator-campus/common';

interface BannerSettings {
  hideChildrenIfShown: boolean;
}

interface Props {
  children: ReactNode;
  scrollableTarget?: RefObject<HTMLDivElement>;
  footer?: boolean;
  banners?: {profileIncomplete?: BannerSettings; activateCommunity?: BannerSettings};
}

export function PageShell({children, scrollableTarget, footer = true, banners = {}}: Props) {
  const {user, university} = useUser();
  const navigate = useNavigate();
  const strings = useActivateCommunityString();

  if (Object.keys(banners).length > 0) {
    if (!user || !university) {
      return <LoadingIndicator sx={{height: '90vh'}} />;
    }
  }

  const showActivateCommunityBanner = !!banners.activateCommunity && user?.staffRole === StaffRole.OWNER && university?.shouldActivateCommunity();
  const showProfileIncompleteBanner = !!banners.profileIncomplete && !user?.profileCompleted && !showActivateCommunityBanner;

  const hideChildren = (showProfileIncompleteBanner && !!banners.profileIncomplete?.hideChildrenIfShown) || (showActivateCommunityBanner && !!banners.activateCommunity?.hideChildrenIfShown);

  return (
    <Box
      id='pageShell'
      ref={scrollableTarget}
      sx={{
        width: '100%',
        minHeight: '100%',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'space-between',
        overflowY: 'auto',
        scrollbarGutter: 'stable',
        px: {xs: 2, md: 4},
        py: 2,
        pt: {xs: 8, md: 3},
      }}
    >
      <Box sx={{width: '100%', maxWidth: '1200px', flexGrow: 1}}>
        {showProfileIncompleteBanner && (
          <AlertBanner
            text={'Complete your profile to unlock full access to the platform.'}
            color={'warning'}
            actionButton={{text: 'Edit profile', action: () => navigate('/profile')}}
            sx={{mb: 2}}
          />
        )}
        {showActivateCommunityBanner && (
          <AlertBanner
            text={university!.reachedUserLimit ? strings.banner.activate : university!.almostReachedUserLimit ? strings.banner.retain : strings.banner.initial_activation}
            color={university!.reachedUserLimit ? 'danger' : university!.almostReachedUserLimit ? 'warning' : 'neutral'}
            startDecorator={university!.reachedUserLimit || university!.almostReachedUserLimit ? <Warning /> : <Info />}
            actionButton={{text: university!.almostReachedUserLimit || university!.reachedUserLimit ? 'View details' : 'Activate', action: () => navigate(`${location.pathname}?showModal=activateCommunity`)}}
            sx={{mb: 2}}
          />
        )}

        {hideChildren ? null : <>{children}</>}
      </Box>

      {footer && (
        <Box sx={{width: '100%', mt: 1}}>
          <Footer />
        </Box>
      )}
    </Box>
  );
}
