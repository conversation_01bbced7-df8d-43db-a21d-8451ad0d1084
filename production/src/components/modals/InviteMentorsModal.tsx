import {Typography} from '@mui/joy';
import {logger, MentorInviteEmailTemplate, sendEmail, University, usingEmulators} from '@creator-campus/common';
import {LoadingIndicator, ModalHeader, ModalShell, useProfileString, useSnackbar} from '@creator-campus/common-components';
import Button from '@mui/joy/Button';
import ButtonGroup from '@mui/joy/ButtonGroup';
import {useState} from 'react';
import {v4 as uuidv4} from 'uuid';
import Input from '@mui/joy/Input';
import {normaliseUrl} from '../../utils.tsx';

export const DAYS_MENTOR_INVITE_VALID = 14;

export function buildMentorInviteLink(mentorEmail: string, token: string) {
  return `${usingEmulators ? 'localhost:5173' : 'https://app.creatorcampus.io'}/create-mentor-account?mentorId=${mentorEmail}&token=${token}`;
}

interface MentorInvite {
  email: string;
  token: string;
  link: string;
  daysValid: number;
}

interface Props {
  onClose: () => void;
  afterInvitesSent: (emailTokenPairs: Record<string, string>) => Promise<void>;
  newMentorEmails: string[];
  myEmail: string;
  university: University;
}

export default function InviteMentorsModal({onClose, afterInvitesSent, newMentorEmails, myEmail, university}: Props) {
  const [loading, setLoading] = useState(false);
  const [calendlyUrl, setCalendlyUrl] = useState<string>('');

  const {showErrorSnackbar, showSnackbar} = useSnackbar();

  const newMentorsWithoutMe = newMentorEmails.filter((email) => email !== myEmail);

  function generateInvite(mentorEmail: string): MentorInvite {
    const token = uuidv4();
    const link = buildMentorInviteLink(mentorEmail, token);

    logger.debug(`Generated invite link for ${mentorEmail}: ${link}`);

    return {
      email: mentorEmail,
      token,
      link,
      daysValid: DAYS_MENTOR_INVITE_VALID,
    };
  }

  async function sendMentorEmailInvites() {
    const emailTokenPairs: Record<string, string> = {};
    const normalisedCalendlyUrl = normaliseUrl(calendlyUrl);

    if (newMentorEmails.includes(myEmail)) {
      if (normalisedCalendlyUrl) {
        emailTokenPairs[myEmail] = normalisedCalendlyUrl;
      } else {
        showSnackbar('Calendly URL is invalid.', 'danger');
        return null;
      }
    }

    const promises: Promise<any>[] = newMentorsWithoutMe.map((email) => {
      const invite = generateInvite(email);
      emailTokenPairs[email] = invite.token;

      return sendEmail(
        email,
        new MentorInviteEmailTemplate({
          daysValid: invite.daysValid,
          link: invite.link,
          universityName: university.name,
        }),
      );
    });

    await Promise.all(promises);

    return emailTokenPairs;
  }

  return (
    <ModalShell onClose={onClose}>
      <ModalHeader title={'Add mentors'} />

      {newMentorsWithoutMe.length > 0 &&
        newMentorsWithoutMe.map((email) => (
          <Typography
            key={email}
            sx={{fontStyle: 'italic'}}
          >
            {email}
          </Typography>
        ))}

      {newMentorEmails.includes(myEmail) && (
        <>
          <Typography
            level='body-sm'
            mt={1}
            mb={1}
          >
            You have added yourself as a mentor. Please enter your Calendly URL below.
          </Typography>
          <Input
            key='calendly-url'
            value={calendlyUrl}
            required
            size='sm'
            slotProps={{input: {maxLength: 500}}}
            placeholder='https://calendly.com/your_scheduling_page'
            onChange={(event) => {
              setCalendlyUrl(event.target.value);
            }}
          />
        </>
      )}

      <ButtonGroup
        spacing={1}
        sx={{mt: 2}}
      >
        <Button
          disabled={loading}
          variant='solid'
          color='primary'
          onClick={async () => {
            setLoading(true);

            await sendMentorEmailInvites()
              .then(async (emailTokenPairs) => {
                if (!emailTokenPairs) {
                  return;
                }

                await afterInvitesSent(emailTokenPairs);
                onClose();
              })
              .catch((e) => {
                showErrorSnackbar(e, 'Error sending email invites.');
              })
              .finally(() => {
                setLoading(false);
              });
          }}
        >
          {loading ? <LoadingIndicator size='sm' /> : 'Confirm'}
        </Button>
        <Button
          variant='outlined'
          color='neutral'
          onClick={() => onClose()}
        >
          {useProfileString().cancel}
        </Button>
      </ButtonGroup>
    </ModalShell>
  );
}
