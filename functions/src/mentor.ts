import {onCall} from 'firebase-functions/v2/https';
import * as admin from 'firebase-admin';
import {ApprovedMentor} from '@creator-campus/common';
import {onSchedule} from 'firebase-functions/v2/scheduler';
import {deleteExpiredDocs} from './utils';

export const validateMentorToken = onCall(async (request) => {
  if (!request.data.mentorId || !request.data.token) {
    console.error(`At least one required argument is undefined. mentorId=${request.data.mentorId}, token=${request.data.token}`);
    return {valid: false};
  }

  const approvedMentorDoc = admin.firestore().collection('approvedMentors').doc(request.data.mentorId);

  const snapshot = await approvedMentorDoc.get();
  if (!snapshot.exists) {
    return {valid: false};
  }

  const data = snapshot.data() as ApprovedMentor;

  if (data.token !== request.data.token) {
    console.error(`Tokens do not match. mentorId=${request.data.mentorId}, token=${request.data.token}`);
    return {valid: false};
  }

  await approvedMentorDoc.delete();

  return {
    valid: true,
    ...data,
  };
});

export const cleanUpExpiredMentorApprovals = onSchedule('every day 18:00', async () => {
  await deleteExpiredDocs('approvedMentors');
});
