import React, {useEffect, useState} from 'react';
import {useNavigate} from 'react-router-dom';
import Markdown from 'react-markdown';
import {Accordion, AccordionDetails, AccordionGroup, AccordionSummary, Box, Card, List, ListItem, ListItemButton, ListItemContent, Typography} from '@mui/joy';
import rehypeRaw from 'rehype-raw';
import {logger} from '@creator-campus/common';
import {useSnackbar} from '@creator-campus/common-components';

interface Props {
  article: string;
  path?: string;
}

type FileType = string | {[key: string]: string[]};

const MarkdownViewer: React.FC<{fileName: string}> = ({fileName}) => {
  const [content, setContent] = useState<string>('');

  const {showErrorSnackbar} = useSnackbar();

  useEffect(() => {
    if (fileName) {
      fetch(`/markdown/${fileName}.md`)
        .then((res) => res.text())
        .then((text) => setContent(text))
        .catch((e) => {
          logger.error(e);
          showErrorSnackbar(e, 'Error loading content.');
        });
    }
  }, [fileName]);

  return (
    <Box sx={{height: '100%', overflow: 'auto', padding: 2, ml: {xs: 0, md: 3}}}>
      <Markdown rehypePlugins={[rehypeRaw]}>{content}</Markdown>
      {/* {fileName.includes('/') ? <ArrowBlock isFirst={true} isLast={false} /> : <></>} */}
    </Box>
  );
};

const LocalSidebar: React.FC<{files: FileType[]; article: string}> = ({files, article}) => {
  const navigate = useNavigate();

  return (
    <List
      size='sm'
      sx={{
        gap: 1,
        '--List-nestedInsetStart': '30px',
        '--ListItem-radius': (theme) => theme.vars.radius.sm,
        height: '100%',
        overflow: 'auto',
        padding: 2,
      }}
    >
      <Typography level='h4'>Resources</Typography>
      {files.map((file, index) => {
        if (typeof file === 'string') {
          return (
            <ListItem key={index}>
              <ListItemButton
                selected={article === file.replace('.md', '').replace('?', '').split('/').pop()}
                role='menuitem'
                component='a'
                onClick={() => {
                  navigate(`/education/${file.replace('.md', '')}`);
                }}
              >
                <ListItemContent>
                  <Typography level='body-sm'>{file.replace('.md', '')}</Typography>
                </ListItemContent>
              </ListItemButton>
            </ListItem>
          );
        } else {
          return Object.keys(file).map((key) => (
            <React.Fragment key={key}>
              <AccordionGroup
                disableDivider
                sx={{maxWidth: 400}}
              >
                <Accordion defaultExpanded>
                  <AccordionSummary>
                    <Typography
                      level='body-sm'
                      sx={{ml: -0.5}}
                    >
                      {key}
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    {file[key].map((subFile) => (
                      <ListItem key={subFile}>
                        <ListItemButton
                          selected={article === subFile.replace('.md', '').replace('?', '')}
                          role='menuitem'
                          component='a'
                          onClick={() => {
                            navigate(`/education/${key}/${subFile.replace('.md', '')}`);
                          }}
                        >
                          <ListItemContent>
                            <Typography level='body-sm'>{subFile.replace('.md', '')}</Typography>
                          </ListItemContent>
                        </ListItemButton>
                      </ListItem>
                    ))}
                  </AccordionDetails>
                </Accordion>
              </AccordionGroup>
            </React.Fragment>
          ));
        }
      })}
    </List>
  );
};

const TopNav: React.FC<{files: FileType[]; article: string}> = ({files, article}) => {
  const navigate = useNavigate();

  return (
    <List
      size='sm'
      sx={{
        gap: 1,
        '--List-nestedInsetStart': '30px',
        '--ListItem-radius': (theme) => theme.vars.radius.sm,
        height: '100%',
        overflow: 'auto',
        padding: 2,
        display: {xs: 'block', md: 'none'},
      }}
    >
      <Typography level='h4'>Resources</Typography>
      {files.map((file, index) => {
        if (typeof file === 'string') {
          return (
            <ListItem key={index}>
              <ListItemButton
                selected={article === file.replace('.md', '').replace('?', '').split('/').pop()}
                role='menuitem'
                component='a'
                onClick={() => {
                  navigate(`/education/${file.replace('.md', '')}`);
                }}
              >
                <ListItemContent>
                  <Typography level='body-sm'>{file.replace('.md', '')}</Typography>
                </ListItemContent>
              </ListItemButton>
            </ListItem>
          );
        } else {
          return Object.keys(file).map((key) => (
            <React.Fragment key={key}>
              <AccordionGroup disableDivider>
                <Accordion>
                  <AccordionSummary>
                    <Typography
                      level='body-sm'
                      sx={{ml: -0.5}}
                    >
                      {key}
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    {file[key].map((subFile) => (
                      <ListItem key={subFile}>
                        <ListItemButton
                          selected={article === subFile.replace('.md', '').replace('?', '')}
                          role='menuitem'
                          component='a'
                          onClick={() => {
                            navigate(`/education/${key}/${subFile.replace('.md', '')}`);
                          }}
                        >
                          <ListItemContent>
                            <Typography level='body-sm'>{subFile.replace('.md', '')}</Typography>
                          </ListItemContent>
                        </ListItemButton>
                      </ListItem>
                    ))}
                  </AccordionDetails>
                </Accordion>
              </AccordionGroup>
            </React.Fragment>
          ));
        }
      })}
    </List>
  );
};

function MarkdownBrowser(props: Props) {
  const [files, setFiles] = useState<FileType[]>([]);

  const {showErrorSnackbar} = useSnackbar();

  useEffect(() => {
    // Fetch the list of markdown files
    fetch('/markdown/files.json')
      .then((res) => res.json())
      .then((data) => {
        logger.debug(data.files);
        setFiles(data.files);
      })
      .catch((e) => {
        logger.error(e);
        showErrorSnackbar(e, 'Error loading content.');
      });
  }, []);

  return (
    <Box sx={{display: 'flex'}}>
      <Box
        minWidth='250px'
        maxWidth='250px'
        display={{xs: 'none', md: 'block'}}
      >
        <Card sx={{position: 'sticky', top: '0', padding: 0}}>
          <LocalSidebar
            files={files}
            article={props.article}
          />
        </Card>
      </Box>
      <Box sx={{height: '100%', overflow: 'auto', marginTop: -5}}>
        <Box display={{xs: 'block', md: 'none'}}>
          <Card sx={{mt: 5, padding: 0, maxWidth: '500px'}}>
            <TopNav
              files={files}
              article={props.article}
            />
          </Card>
        </Box>
        <MarkdownViewer fileName={props.path === undefined ? props.article : props.path + '/' + props.article} />
      </Box>
    </Box>
  );
}

export default MarkdownBrowser;
