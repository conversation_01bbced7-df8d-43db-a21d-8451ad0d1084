import Box from '@mui/joy/Box';
import {ListItemDecorator, Tab, tabClasses, TabList, TabPanel, Tabs} from '@mui/joy';
import {ChildFriendly, FireTruck, QueryStats, ShowChart} from '@mui/icons-material';
import {Link, useParams} from 'react-router-dom';
import {TabData} from '@creator-campus/common';
import {PageHeader, PageShell, AdminInsightsTab} from '@creator-campus/common-components';
import {OnboardingStatsTab} from '../components/OnboardingStatsTab.tsx';

export default function DashboardPage() {
  const params = useParams();

  const tabs: TabData[] = [
    {
      displayName: 'Overview',
      leadingIcon: <ShowChart />,
      path: 'overview',
      content: <AdminInsightsTab />,
    },
    {
      displayName: 'Onboarding',
      leadingIcon: <ChildFriendly />,
      path: 'onboarding',
      content: <OnboardingStatsTab />,
    },
    {
      displayName: 'Firestore Usage',
      leadingIcon: <FireTruck />,
      path: 'firestoreUsage',
      content: <>Under construction</>,
    },
    {
      displayName: 'InfluxDB Usage',
      leadingIcon: <QueryStats />,
      path: 'influxUsage',
      content: <>Under construction</>,
    },
  ];

  return (
    <PageShell>
      <PageHeader
        title='Dashboard'
        sx={{mt: 1, px: {xs: 2, md: 6}}}
      />
      <Tabs
        defaultValue={tabs[0].path}
        sx={{
          bgcolor: 'transparent',
        }}
        value={params.tab}
      >
        <TabList
          tabFlex={1}
          size='sm'
          sx={{
            pl: {xs: 0, md: 4},
            justifyContent: 'left',
            [`&& .${tabClasses.root}`]: {
              fontWeight: '600',
              flex: 'initial',
              color: 'text.tertiary',
              [`&.${tabClasses.selected}`]: {
                bgcolor: 'transparent',
                color: 'text.primary',
                '&::after': {
                  height: '2px',
                  bgcolor: 'primary.500',
                },
              },
            },
          }}
        >
          {tabs.map((tab) => (
            <Tab
              key={tab.path}
              sx={{borderRadius: '6px 6px 0 0'}}
              indicatorInset
              value={tab.path}
              component={Link}
              to={`/dashboard/${tab.path}`}
            >
              <ListItemDecorator>{tab.leadingIcon}</ListItemDecorator>
              {tab.displayName}
            </Tab>
          ))}
        </TabList>
        {tabs.map((tab) => (
          <TabPanel
            key={tab.path}
            value={tab.path}
          >
            <Box sx={{ml: {xs: 0, md: 4}, mr: {xs: 0, md: 4}}}>{tab.content}</Box>
          </TabPanel>
        ))}
      </Tabs>
    </PageShell>
  );
}
