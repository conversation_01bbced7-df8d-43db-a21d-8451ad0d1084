import {ReactNode} from 'react';
import Modal from '@mui/joy/Modal';
import {ModalOverflow} from '@mui/joy';
import Sheet from '@mui/joy/Sheet';

interface Props {
  open: boolean;
  handleBackdropClick?: () => void;
  children: ReactNode;
}

export default function ModalShell({children, open, handleBackdropClick}: Props) {
  function handleClose(_: string, reason: string) {
    if (handleBackdropClick && reason === 'backdropClick') {
      handleBackdropClick();
    }
  }

  return (
    <Modal
      disableEscapeKeyDown={true}
      aria-labelledby='close-modal-title'
      open={open}
      onClose={handleClose}
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 1000,
      }}
    >
      <ModalOverflow
        sx={{
          display: 'flex',
          alignItems: 'center',
          flexDirection: 'column',
          justifyContent: 'flex-start',
          zIndex: 1000,
        }}
      >
        <Sheet
          variant='outlined'
          sx={{
            minWidth: 300,
            borderRadius: 'md',
            p: 3,
            maxWidth: {lg: 700, md: '70%', sm: '80%', xs: '90%'},
            margin: 'auto',
          }}
        >
          {children}
        </Sheet>
      </ModalOverflow>
    </Modal>
  );
}
