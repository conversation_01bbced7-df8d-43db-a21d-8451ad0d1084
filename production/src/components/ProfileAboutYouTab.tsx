import Card from '@mui/joy/Card';
import EditProfileForm from './EditProfileForm.tsx';
import {Stack} from '@mui/joy';
import {User} from '@creator-campus/common';
import {MembershipApplicationStatus} from './MembershipApplicationStatus.tsx';

interface Props {
  user: User;
}

export default function ProfileAboutYouTab({user}: Props) {
  return (
    <Stack
      spacing={2}
      sx={{
        display: 'flex',
        maxWidth: '800px',
        width: {xs: '100%', sm: '100%', md: '100%', lg: '100%', xl: '800px'},
        mx: 'auto',
        pb: 1,
      }}
    >
      {user.applicationStatus === 'submitted' || user.applicationStatus === 'rejected' ? (
        <MembershipApplicationStatus user={user} />
      ) : (
        <Card>
          <EditProfileForm variant={user.applicationStatus === 'accepted' ? 'default' : 'onboarding'} />
        </Card>
      )}
    </Stack>
  );
}
