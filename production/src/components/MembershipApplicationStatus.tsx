import {Stack} from '@mui/joy';
import Typography from '@mui/joy/Typography';
import {REAPPLICATION_COOLDOWN_DAYS, User, UserApplicationStatus} from '@creator-campus/common';
import {useSnackbar, useScreenWidth} from '@creator-campus/common-components';
import Input from '@mui/joy/Input';
import Button from '@mui/joy/Button';
import {useState} from 'react';
import {Check} from '@mui/icons-material';

interface Content {
  title: string;
  subtitle: string;
  displayStatus: string;
  color: 'success' | 'warning' | 'danger';
}

interface Props {
  user: User;
}

export function MembershipApplicationStatus({user}: Props) {
  const [linkCopied, setLinkCopied] = useState<boolean>(false);

  const {showSnackbar} = useSnackbar();
  const screenWidth = useScreenWidth();

  const referralLink = `https://app.creatorcampus.io/signup/students/referral-${user.id}`;
  const earliestReapplicationDate = new Date(user.dateJoined.getTime() + 1000 * 60 * 60 * 24 * REAPPLICATION_COOLDOWN_DAYS);

  //@ts-ignore
  const content: Record<UserApplicationStatus, Content> = {
    submitted: {
      title: "We're reviewing your profile!",
      subtitle: "We'll email you within 24 hours to let you know if you've been accepted to join Creator Campus.",
      displayStatus: 'Submitted',
      color: 'warning',
    },
    rejected: {
      title: 'Your application was unsuccessful',
      subtitle: `Thanks for applying to join Creator Campus. You weren't successful this time, but if you're still keen, you'll be able to submit a new application on ${earliestReapplicationDate.toDateString()}.`,
      displayStatus: 'Unsuccessful',
      color: 'danger',
    },
  };

  return (
    <Stack
      alignSelf={'center'}
      sx={{maxWidth: Math.min(screenWidth - 32, 500), pt: {sm: 0, md: 1, lg: 2}}}
    >
      <Typography level='title-lg'>{content[user.applicationStatus].title}</Typography>
      <Stack
        direction={'row'}
        spacing={0.5}
        sx={{mt: 0.5}}
      >
        <Typography level='title-sm'>Status:</Typography>
        <Typography
          level='title-sm'
          color={content[user.applicationStatus].color}
        >
          {content[user.applicationStatus].displayStatus}
        </Typography>
      </Stack>
      <Stack
        direction={screenWidth < 400 ? 'column-reverse' : 'row'}
        spacing={screenWidth < 400 ? 4 : 2}
        my={screenWidth < 400 ? 3 : 4}
        alignItems={'center'}
      >
        <img
          src='/undraw_connecting_teams.svg'
          alt={'Onboarding complete'}
          className='responsive-image'
          style={{
            width: '100%',
            height: 130,
          }}
        />
        <Stack
          spacing={2}
          alignItems={'left'}
        >
          <Typography level='body-md'>{content[user.applicationStatus].subtitle}</Typography>
        </Stack>
      </Stack>
      {user.applicationStatus === 'submitted' && (
        <Stack spacing={1}>
          <Stack
            direction={'row'}
            spacing={1}
            alignItems={'center'}
          >
            <Typography
              level='title-md'
              sx={{mt: 2}}
            >
              Fast-track your application ⏩
            </Typography>
          </Stack>
          <Typography level='body-md'>Invite your friends and business partners to join you on Creator Campus! If you share your referral link, all of your applications will get fast-tracked.</Typography>
          <Stack
            direction={'row'}
            spacing={1}
            justifyContent={'center'}
            pt={2}
          >
            <Input
              value={referralLink}
              disabled={true}
            />
            <Button
              onClick={async () => {
                await navigator.clipboard.writeText(referralLink);
                setLinkCopied(true);
                showSnackbar('Link copied to clipboard.');
              }}
              onMouseLeave={() => setLinkCopied(false)}
              sx={{width: 120}}
            >
              {linkCopied ? <Check /> : <>{'Copy link'}</>}
            </Button>
          </Stack>
        </Stack>
      )}
    </Stack>
  );
}
