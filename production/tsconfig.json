{
  "compilerOptions": {
    "target": "ES2022",
    "useDefineForClassFields": true,
    "lib": ["ES2022", "DOM", "DOM.Iterable"],
    "module": "ES2022",
    "skipLibCheck": true,
    "esModuleInterop": true,

    /* Sentry source-mapping */
    "sourceMap": true,
    "inlineSources": true,
    "sourceRoot": "/",

    "types": ["node"],

    /* Bundler mode */
    "moduleResolution": "Bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,

    /* Additional options for Jest compatibility */
    "allowJs": true,
    "forceConsistentCasingInFileNames": true
  },

  "include": [
    "src",
  ],

  "exclude": ["cypress.config.ts", "cypress", "node_modules"],
}