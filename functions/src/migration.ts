import {onRequest} from 'firebase-functions/v2/https';
import * as admin from 'firebase-admin';
import {config} from 'dotenv';
import {CollectionReference, FieldValue, Query, QueryDocumentSnapshot} from 'firebase-admin/firestore';
import {Opportunity, OpportunityDbModelSafe, Project, ProjectDbModelSafe, Role, University, UniversityMetrics, UniversityMetricsDbModel, User, UserDbModelSafe} from '@creator-campus/common';
import {calculateProfileScore, calculateProjectScore} from './utils';

const usingEmulators = !!process.env.FIRESTORE_EMULATOR_HOST;
if (usingEmulators) {
  config();
}

async function addFirestoreFields(col: CollectionReference | Query | string, fields: Record<string, any>) {
  if (typeof col === 'string') {
    col = admin.firestore().collection(col);
  }

  const promises: Promise<any>[] = [];

  const snapshot = await col.get();
  for (const document of snapshot.docs) {
    promises.push(
      (async () => {
        await document.ref.set(fields, {merge: true});
      })(),
    );
  }

  await Promise.all(promises);
}

async function removeFirestoreFields(col: CollectionReference | Query | string, fields: string[]) {
  const mapping = fields.reduce((acc, field) => ({...acc, [field]: FieldValue.delete()}), {});
  await setFirestoreValues(col, mapping);
}

async function renameFirestoreFields(col: CollectionReference | Query | string, nameMapping: Record<string, string>) {
  if (typeof col === 'string') {
    col = admin.firestore().collection(col);
  }

  const promises: Promise<any>[] = [];

  const snapshot = await col.get();
  for (const document of snapshot.docs) {
    const docData = document.data();
    const updateData: Record<string, any> = {};

    for (const [from, to] of Object.entries(nameMapping)) {
      if (docData.hasOwnProperty(from)) {
        updateData[to] = docData[from];
        updateData[from] = FieldValue.delete();
      }
    }

    promises.push(document.ref.update(updateData));
  }

  await Promise.all(promises);
}

async function renameFirestoreValues(col: CollectionReference | Query | string, field: string, valueMapping: Record<any, any>) {
  if (typeof col === 'string') {
    col = admin.firestore().collection(col);
  }

  const promises: Promise<any>[] = [];

  for (const [oldValue, newValue] of Object.entries(valueMapping)) {
    // Fetch all documents where the field == oldValue
    const snapshot = await col.where(field, '==', oldValue).get();
    for (const document of snapshot.docs) {
      promises.push(
        document.ref.update({
          [field]: newValue,
        }),
      );
    }
  }

  await Promise.all(promises);
}

async function mapFirestoreValues(col: CollectionReference | Query | string, field: string, valueMapping: (prev: any) => any) {
  if (typeof col === 'string') {
    col = admin.firestore().collection(col);
  }

  const promises: Promise<any>[] = [];

  const snapshot = await col.get();
  for (const document of snapshot.docs) {
    const docData = document.data();
    const newValue = valueMapping(docData[field]);
    promises.push(
      document.ref.update({
        [field]: newValue,
      }),
    );
  }

  await Promise.all(promises);
}

async function setFirestoreValues(col: CollectionReference | Query | string, fieldValues: Record<string, any> | ((doc: QueryDocumentSnapshot) => Record<string, any> | Promise<Record<string, any>>)) {
  if (typeof col === 'string') {
    col = admin.firestore().collection(col);
  }

  const promises: Promise<any>[] = [];

  const snapshot = await col.get();
  for (const document of snapshot.docs) {
    promises.push(
      (async () => {
        const data = typeof fieldValues === 'function' ? await fieldValues(document) : fieldValues;
        await document.ref.set(data, {merge: true});
      })(),
    );
  }

  await Promise.all(promises);
}

async function checkUniHasMentors(universityId: string) {
  console.log(`Checking if university ${universityId} has any mentors...`);
  const mentorsSnapshot = await admin.firestore().collection(User.collectionName).where('universityId', '==', universityId).where('role', '==', Role.MENTOR.id).where('profileCompleted', '==', true).count().get();
  const numMentors = mentorsSnapshot.data().count;

  console.log(`University ${universityId} has ${numMentors} mentors. Updating Firestore doc...`);
  await admin
    .firestore()
    .collection(University.collectionName)
    .doc(universityId)
    .set(
      {
        hasMentors: numMentors > 0,
      },
      {merge: true},
    );
}

export const migration = onRequest(async (_request, response) => {
  await addFirestoreFields('people', {
    services: [],
  });

  await addFirestoreFields('opportunityApplications', {
    cvDownloadUrl: null,
  });

  const universitiesSnapshot = await admin.firestore().collection(University.collectionName).get();
  for (const university of universitiesSnapshot.docs) {
    const metricsSnapshot = await admin.firestore().collection(University.collectionName).doc(university.id).collection('metrics').doc('metrics').get();
    const metrics = metricsSnapshot.data() as any;

    await admin
      .firestore()
      .collection(University.collectionName)
      .doc(university.id)
      .collection('metrics')
      .doc('metrics')
      .update({
        oppApplicationsAccepted: FieldValue.delete(),
        unisFillingOurOpps: {
          ...(metrics.oppApplicationsAccepted > 0 ? {unknown: metrics.oppApplicationsAccepted} : {}),
        },
        unisWhoseOppsWeFill: {},
      });
  }

  response.status(200).send('success');
});
