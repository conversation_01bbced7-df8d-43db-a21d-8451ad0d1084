import {algolia} from '../src/algolia';
import {algoliasearch} from 'algoliasearch';
import {initializeApp} from 'firebase-admin/app';
import {getFirestore} from 'firebase-admin/firestore';

// This script runs after emulators have finished spinning up
console.log('Initialising app...');
initializeApp({projectId: 'creator-campus-app'});

async function syncFirestoreToAlgolia() {
  const algoliaClient = algoliasearch(
    String(process.env.REACT_APP_ALGOLIA_APP_ID),
    String(process.env.REACT_APP_ALGOLIA_WRITE_API_KEY),
  );

  for (const [col, index] of Object.entries(algolia.indexConfigs)) {
    console.log(`Syncing ${col}...`);
    await algoliaClient.clearObjects({indexName: index.name});

    // Fetch all docs from collection
    const snapshot = await getFirestore().collection(col).get();
    const objects = snapshot.docs.map((doc) => ({
      objectID: doc.id,
      ...doc.data(),
    }));

    // Save the docs to Algolia
    await algoliaClient.saveObjects({indexName: index.name, objects});
    console.log(`Synced ${snapshot.size} ${col} documents to ${index.name}`);
  }
}

async function notifyEmulatorsStatusChanged(ready: boolean) {
  await getFirestore().collection('config').doc('emulators').update({
    ready,
  });
}

console.log('Creating Algolia indexes...');
algolia.syncIndexConfigsToAlgolia().then(async () => {
  console.log('Syncing Firestore and Algolia...');
  await syncFirestoreToAlgolia();

  console.log('Notifying front-end that emulators are ready...');
  await notifyEmulatorsStatusChanged(true);

  console.log('\nEmulators are ready! Press Ctrl+C to shut \'em down.');
});

// Listen for shutdown
let shuttingDown = false;
process.on('SIGINT', async () => {
  if (shuttingDown) {
    return;
  }

  shuttingDown = true;
});

// Keep the script running until manually terminated
process.stdin.resume();
