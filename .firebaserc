{"projects": {"default": "creator-campus-app", "demo": "creator-campus-demo", "production": "creator-campus-app", "admin": "creator-campus-app"}, "targets": {"creator-campus-app": {"hosting": {"creator-campus-app": ["creator-campus-app"], "creator-campus-admin": ["creator-campus-admin"]}}, "creator-campus-demo": {"hosting": {"creator-campus-demo": ["default"]}}}, "etags": {"creator-campus-app": {"extensionInstances": {"delete-user-data": "43cc846adebfde4abc39941af93de0b9204ee6f3f00703b5a5675a19bc1e2e98", "firestore-algolia-search-people": "3562d2efa396044077a3b9859d4cd15e2507dea1170aaeb01dc76ab9b8042a7e", "firestore-algolia-search-projects": "2741ad78ba478b71739d4b6e5d9f1e0e43c487ab313a3c05527bf8e0ce988aaa", "firestore-send-email": "b82df21c6bf65a5e10ccedd0902ada78be72ea4332bb27e8f24775b4ee8effc5", "storage-resize-images": "59dd317b3d567bf7f49c22c9622c91eaed48661e5bca45c7725a1e3cffbdf606"}}, "creator-campus-demo": {"extensionInstances": {"delete-user-data": "e7b2137e4dfcb46719fc88bc879e5264740e5cacb7dc1f12f47d0dbf72ec051d", "firestore-algolia-search-people": "29f3973fab5717956e6eb12ae3938898c4b342f7c5f55510559fef3d9de8a884", "firestore-algolia-search-projects": "e42c80fd8076e4a774c0b49dc6585ba3a483873dc48b986f8cbbf654ac64b8cb", "firestore-send-email": "19dffce4c4b0e164749c109aa5d350565447c1a1ab29722ab13fc4ddad14a30d", "storage-resize-images": "daff800b25083d046bcc9c6b50571c01adf3c90d6a296685ffa923429d642d0e"}}}, "dataconnectEmulatorConfig": {}}