import {doc, FieldValue, type FirestoreDataConverter, getDoc, QueryDocumentSnapshot, type SnapshotOptions, Timestamp, type WithFieldValue} from 'firebase/firestore';
import {firestore} from '../../firebase';
import {PaymentInterval} from '../paymentInterval';
import {University} from './university';

export class PartnershipData {
  static readonly collectionName = 'partnership';
  readonly priceGbp: number | null;
  readonly yearlyPriceGbp: number;

  constructor(
    readonly universityId: string,
    readonly lastPaymentDate: Date | null,
    readonly monthlyPriceGbp: number,
    readonly paymentInterval: PaymentInterval | null,
    readonly userLimit: number,
  ) {
    this.priceGbp = paymentInterval === null ? null : paymentInterval === 'monthly' ? monthlyPriceGbp : PartnershipData.convertMonthlyPriceToYearly(monthlyPriceGbp);
    this.yearlyPriceGbp = PartnershipData.convertMonthlyPriceToYearly(monthlyPriceGbp);
  }

  public static convertMonthlyPriceToYearly(monthlyPriceGbp: number) {
    return monthlyPriceGbp * 10;
  }

  public static doc(universityId: string) {
    return doc(firestore(), University.collectionName, universityId, 'private', PartnershipData.collectionName).withConverter(new PartnershipDataConverter());
  }

  public static async fetchForUniversity(universityId: string) {
    return getDoc(PartnershipData.doc(universityId)).then((snapshot) => snapshot.data() || null);
  }

  public getNextPaymentDate() {
    if (!this.lastPaymentDate) {
      return null;
    }

    const lastPaymentDate = new Date(this.lastPaymentDate);

    if (this.paymentInterval === 'monthly') {
      return new Date(lastPaymentDate.setMonth(lastPaymentDate.getMonth() + 1));
    } else {
      return new Date(lastPaymentDate.setFullYear(lastPaymentDate.getFullYear() + 1));
    }
  }
}

export interface UniversityPartnershipDbModelNoDates {
  monthlyPriceGbp: number;
  userLimit: number;
  paymentInterval: PaymentInterval | null; // null if not paying
}

export interface UniversityPartnershipDbModel extends UniversityPartnershipDbModelNoDates {
  lastPaymentDate: Timestamp | null;
}

export interface UniversityPartnershipDbModelSafe extends UniversityPartnershipDbModelNoDates {
  lastPaymentDate: any | null;
}

export class PartnershipDataConverter implements FirestoreDataConverter<PartnershipData, UniversityPartnershipDbModel> {
  toFirestore(partnershipData: WithFieldValue<PartnershipData>): WithFieldValue<UniversityPartnershipDbModel> {
    return {
      lastPaymentDate: partnershipData.lastPaymentDate instanceof FieldValue ? partnershipData.lastPaymentDate : partnershipData.lastPaymentDate === null ? null : Timestamp.fromDate(partnershipData.lastPaymentDate as Date),
      monthlyPriceGbp: partnershipData.monthlyPriceGbp,
      paymentInterval: partnershipData.paymentInterval,
      userLimit: partnershipData.userLimit,
    };
  }

  fromFirestore(snapshot: QueryDocumentSnapshot, options: SnapshotOptions): PartnershipData {
    const data = snapshot.data(options) as UniversityPartnershipDbModel;
    return new PartnershipData(snapshot.ref.parent.parent!.id, data.lastPaymentDate?.toDate() || null, data.monthlyPriceGbp, data.paymentInterval, data.userLimit);
  }
}
