import {type EmailTemplate} from './emailTemplate.js';

interface Data {
  // The recipient's name
  displayName: string;
  // The type of reminder
  reminderType: '7-day' | '24-hour';
  // The recipient's uid
  uid: string;
}

export class CompleteProfileReminderEmailTemplate implements EmailTemplate {
  readonly template: string = 'complete_profile_reminder';

  constructor(readonly data: Data) {}
}
