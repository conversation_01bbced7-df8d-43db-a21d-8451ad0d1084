import {Box, Typography} from '@mui/joy';
import Button from '@mui/joy/Button';
import {ModalHeader, ModalShell, useUser} from '@creator-campus/common-components';
import ConfettiExplosion from 'react-confetti-explosion';

interface Props {
  onClose: () => void;
}

export function NewPartnerModal({onClose}: Props) {
  const title = 'Welcome to Creator Campus';
  const subtitle = "It's great to have you onboard as a partner! You can now track metrics about your community, update your branding, manage staff and mentors, and more. Check out the Dashboard page on the sidebar to get started.";

  const {university} = useUser();

  function Image() {
    return (
      <img
        src='/new-partner.png'
        alt={'Onboarding complete'}
        className='responsive-image'
        style={{
          objectFit: 'cover',
          width: '100%',
          height: '90%',
        }}
      />
    );
  }

  function ActionButtons() {
    return (
      <Button
        sx={{maxWidth: 150, mt: 2}}
        onClick={onClose}
      >
        Let's go!
      </Button>
    );
  }

  if (!university) {
    return <></>;
  }

  return (
    <>
      <Box sx={{width: '100%', justifyItems: 'center', position: 'absolute', top: 0, left: 0, right: 0}}>
        <ConfettiExplosion
          width={window.innerWidth}
          zIndex={995}
          force={0.4}
        />
      </Box>
      <ModalShell
        onClose={onClose}
        withCloseButton={true}
        closeOnBackgroundClick={true}
      >
        <Box sx={{display: {xs: 'none', sm: 'flex'}, justifyContent: 'center', alignItems: 'center'}}>
          <Box
            sx={{
              maxWidth: 500,
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              marginRight: 2,
            }}
          >
            <Image />
          </Box>
          <Box sx={{display: 'flex', flexDirection: 'column'}}>
            <ModalHeader
              title={title}
              subtitle={subtitle}
            />
            <ActionButtons />
          </Box>
        </Box>
        <Box sx={{display: {xs: 'flex', sm: 'none'}, flexDirection: 'column', justifyContent: 'center', alignItems: 'center'}}>
          <ModalHeader title={title} />
          <Box
            sx={{
              mt: 1.5,
              maxWidth: 200,
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <Image />
          </Box>
          <Typography
            id='modal-desc'
            textColor='text.tertiary'
            sx={{mt: 1}}
          >
            {subtitle}
          </Typography>
          <ActionButtons />
        </Box>
      </ModalShell>
    </>
  );
}
