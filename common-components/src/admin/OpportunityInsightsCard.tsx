import {Box, CircularProgress, Typography} from '@mui/joy';
import {Work} from '@mui/icons-material';
import Card from '@mui/joy/Card';
import {AdminPieChart} from './AdminPieChart';
import {AdminLineChart} from './AdminLineChart';
import {NumericMetric} from './NumericMetric';
import Stack from '@mui/joy/Stack';
import {PieChartData, UniversityMetrics, formatHours} from '@creator-campus/common';
import Tooltip from '@mui/joy/Tooltip';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';

interface Props {
  universityId: string;
  setUniversityId: (uni: string) => void;
  metrics: UniversityMetrics;
  numOpportunitiesOpen: number | null;
  numOpenOppsData: PieChartData[] | null;
}

export function OpportunityInsightsCard({universityId, setUniversityId, metrics, numOpportunitiesOpen, numOpenOppsData}: Props) {
  return (
    <>
      <Card sx={{flex: 1}}>
        <Box sx={{display: 'flex', alignItems: 'center', mb: 2}}>
          <Typography
            startDecorator={<Work />}
            level="h4"
          >
            Opportunities
          </Typography>
        </Box>
        <Box sx={{display: 'flex', flexDirection: 'row', alignItems: 'center', alignSelf: 'center', mb: 2}}>
          <Typography
            level="h1"
            sx={{fontWeight: 'bold', mr: 2}}
          >
            {numOpportunitiesOpen === null ? <CircularProgress size="sm" /> : numOpportunitiesOpen}
          </Typography>
          <Typography level="body-md">Open Opportunit{numOpportunitiesOpen === 1 ? 'y' : 'ies'}</Typography>
        </Box>

        {universityId === 'All' && (
          <Box sx={{alignSelf: 'center'}}>
            <AdminPieChart
              data={numOpenOppsData}
              showEmptyLegendItems={false}
              onSegmentClick={(data: any) => {
                setUniversityId(data.name);
              }}
            />
          </Box>
        )}

        <Box sx={{alignSelf: 'center'}}>
          <AdminLineChart
            influxField={'oppApplicationsAccepted'}
            label={'Successful Matches'}
            universityId={universityId === 'All' ? undefined : universityId}
          />
        </Box>

        <Stack
          ml={1.5}
          spacing={2.5}
        >
          <NumericMetric
            label={'Opportunities Filled'}
            value={metrics.getOppApplicationsAccepted()}
            tooltip={`The total number of opportunities posted by ${universityId} users, which have been successfully filled.`}
          />
          <NumericMetric
            label={'Average Time to Fill'}
            value={metrics.getAvgTimeToMatch()}
            tooltip={`The average time it takes for an opportunity from ${universityId} to go from posted to filled.`}
          />
          <NumericMetric
            label={'Applications Sent'}
            value={metrics.outgoingOppApplications}
            tooltip={`The total number of opportunity applications sent by users from ${universityId}.`}
          />
          <NumericMetric
            label={'Connection Requests Sent'}
            value={metrics.outgoingConnectionRequests}
            tooltip={`The total number of connection requests sent by users from ${universityId}.`}
          />
        </Stack>

        {Object.keys(metrics.unisWhoseOppsWeFill).length > 0 && (
          <>
            <Stack
              direction={'row'}
              spacing={0.5}
              alignItems={'center'}
              justifyContent={'center'}
              mt={3}
            >
              <Typography
                level="title-lg"
                sx={{textAlign: 'center'}}
              >
                External Opportunities Filled
              </Typography>
              <Tooltip
                sx={{maxWidth: 300}}
                title={`Where are the opportunities that ${universityId} users are filling?.`}
                variant="solid"
              >
                <InfoOutlinedIcon fontSize="large" />
              </Tooltip>
            </Stack>
            <Box sx={{alignSelf: 'center'}}>
              <AdminPieChart data={Object.entries(metrics.unisWhoseOppsWeFill).map(([name, value]) => ({name, value}))} />
            </Box>
          </>
        )}

        {Object.keys(metrics.unisFillingOurOpps).length > 0 && (
          <>
            <Stack
              direction={'row'}
              spacing={0.5}
              alignItems={'center'}
              justifyContent={'center'}
            >
              <Typography
                level="title-lg"
                sx={{textAlign: 'center'}}
              >
                Internal Opportunities Filled
              </Typography>
              <Tooltip
                sx={{maxWidth: 300}}
                title={`Where are users from who are filling opportunities at ${universityId}?.`}
                variant="solid"
              >
                <InfoOutlinedIcon fontSize="large" />
              </Tooltip>
            </Stack>
            <Box sx={{alignSelf: 'center'}}>
              <AdminPieChart data={Object.entries(metrics.unisFillingOurOpps).map(([name, value]) => ({name, value}))} />
            </Box>
          </>
        )}
      </Card>
    </>
  );
}
