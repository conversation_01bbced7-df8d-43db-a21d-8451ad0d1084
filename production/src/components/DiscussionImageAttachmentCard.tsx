import Box from '@mui/joy/Box';
import {LoadingIndicator} from '@creator-campus/common-components';
import {LinearProgress, ModalOverflow, Skeleton} from '@mui/joy';
import IconButton from '@mui/joy/IconButton';
import {Clear, Download} from '@mui/icons-material';
import Modal from '@mui/joy/Modal';
import {useState} from 'react';
import {DownloadableSettings, UploadableSettings} from './DiscussionAttachmentCard.tsx';
import Typography from '@mui/joy/Typography';
import Stack from '@mui/joy/Stack';

interface Props {
  uploadable?: UploadableSettings;
  downloadable?: DownloadableSettings;
  downloadUrl: string | null;
  downloadInProgress: boolean;
  uploadComplete: boolean;
  uploadProgress: number | null;
  downloadAttachment: () => void;
}

export default function DiscussionImageAttachmentCard({downloadable, uploadable, downloadUrl, downloadInProgress, uploadComplete, uploadProgress, downloadAttachment}: Props) {
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const [hovering, setHovering] = useState<boolean>(false);

  const src = downloadable ? downloadUrl : URL.createObjectURL(uploadable!.file);

  return (
    <>
      <Box
        position='relative'
        sx={{
          minWidth: 100,
          minHeight: 100,
          maxWidth: downloadable ? 287 : 200,
          maxHeight: downloadable ? 287 : 200,
          borderRadius: 4,
          overflow: 'hidden',
        }}
        onClick={() => {
          if (uploadable) {
            uploadable.removeFile();
          } else {
            setModalOpen(true);
          }
        }}
        onMouseEnter={() => setHovering(true)}
        onMouseLeave={() => setHovering(false)}
      >
        {/* Image */}
        {src ? (
          <img
            src={src}
            alt={downloadable ? downloadable.metadata.name : uploadable!.file.name}
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover',
            }}
          />
        ) : (
          <Skeleton
            variant={'rectangular'}
            width={1000}
            height={1000}
          />
        )}

        {/* Hover overlay */}
        <Box
          position='absolute'
          top={0}
          left={0}
          width='100%'
          height='100%'
          sx={{
            cursor: 'pointer',
            transition: 'background-color 0.3s',
            backgroundColor: hovering && uploadable ? 'rgba(0,0,0,0.4)' : 'rgba(0,0,0,0)',
          }}
        >
          {hovering && uploadable && (
            <Stack
              direction={'row'}
              height={'100%'}
              spacing={0.5}
              alignItems={'center'}
              justifyContent={'center'}
            >
              <Clear
                sx={{fontSize: 'xl'}}
                style={{color: 'white'}}
              />
              <Typography
                level='body-sm'
                noWrap
                style={{color: 'white'}}
              >
                Remove
              </Typography>
            </Stack>
          )}

          {/* Upload progress */}
          {uploadable && (
            <Box sx={{width: '100%', position: 'absolute', bottom: 0}}>
              <LinearProgress
                determinate
                color={uploadProgress === null ? 'danger' : uploadComplete ? 'success' : 'primary'}
                value={uploadProgress === null ? 100 : uploadProgress * 100}
                sx={{
                  '--LinearProgress-radius': '0px',
                }}
              />
            </Box>
          )}
        </Box>

        {/* Download button at the bottom right */}
        {downloadable && hovering && (
          <IconButton
            onClick={(e) => {
              e.stopPropagation();
              downloadAttachment();
            }}
            size={'sm'}
            sx={{
              position: 'absolute',
              bottom: 8,
              right: 8,
              bgcolor: 'rgba(255, 255, 255, 0.3)',
            }}
          >
            {downloadInProgress ? <LoadingIndicator size={'sm'} /> : <Download sx={{color: '#fff'}} />}
          </IconButton>
        )}
      </Box>
      {/* Modal to show the enlarged image */}
      {src && (
        <Modal
          open={modalOpen}
          onClose={() => setModalOpen(false)}
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backdropFilter: 'blur(6px)',
            zIndex: 990,
          }}
        >
          <ModalOverflow
            sx={{
              position: 'relative',
              maxHeight: '90vh',
              maxWidth: '90vw',
            }}
          >
            {/* Image and IconButton container */}
            <Box sx={{position: 'relative', width: '100%', height: '100%'}}>
              <IconButton
                size={'sm'}
                sx={{
                  position: 'absolute',
                  top: 12,
                  right: 12,
                  backgroundColor: 'rgba(255, 255, 255, 0.3)',
                  zIndex: 1,
                }}
                onClick={() => setModalOpen(false)}
              >
                <Clear sx={{color: '#fff'}} />
              </IconButton>
              <img
                src={src}
                alt={downloadable ? downloadable.metadata.name : uploadable!.file.name}
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'contain',
                }}
              />
            </Box>
          </ModalOverflow>
        </Modal>
      )}
    </>
  );
}
