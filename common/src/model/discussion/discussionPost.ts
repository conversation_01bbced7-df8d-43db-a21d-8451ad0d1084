import {addDoc, collection, doc, FieldValue, type FirestoreDataConverter, getDoc, QueryDocumentSnapshot, setDoc, type SnapshotOptions, Timestamp, type WithFieldValue} from 'firebase/firestore';
import {firestore} from '../../firebase.js';
import {DiscussionComment} from './discussionComment.js';
import {User} from '../user/user.js';
import {DiscussionPostBase, DiscussionPostBaseDbModel, DiscussionPostBaseDbModelSafe} from './discussionPostBase.js';
import {generateRandomId} from '../../utils/misc.js';
import {DiscussionAttachment} from './discussionAttachment.js';
import {University} from '../university/university';

export class DiscussionPost extends DiscussionPostBase {
  constructor(
    override readonly id: string,
    override readonly attachments: DiscussionAttachment[],
    override readonly authorId: string,
    override readonly datePosted: Date,
    readonly numComments: number,
    readonly pinned: boolean,
    override readonly text: string,
    override readonly universityId: string | null,
    override readonly upVotes: string[],
  ) {
    super(id, attachments, authorId, datePosted, text, universityId, upVotes);
  }

  public static create(user: User, text: string, attachments: DiscussionAttachment[], universityId?: string) {
    return new DiscussionPost(generateRandomId(), attachments, user.id, new Date(), 0, false, text, universityId || null, []);
  }

  public static async addNew(post: DiscussionPost) {
    await setDoc(DiscussionPost.doc(post.id, post.universityId || undefined), post);
  }

  public static globalDoc() {
    return doc(firestore(), 'global', 'discussion');
  }

  public static async fetchGlobalPinnedPostIds() {
    let docSnap = await getDoc(DiscussionPost.globalDoc());
    return docSnap.data()?.pinnedPosts || [];
  }

  public static doc(id: string, universityId?: string) {
    if (universityId) {
      return doc(firestore(), University.collectionName, universityId, 'discussion', id).withConverter<DiscussionPost, DiscussionPostDbModel>(new DiscussionPostConverter());
    }

    return doc(firestore(), 'global', 'discussion', 'discussion', id).withConverter<DiscussionPost, DiscussionPostDbModel>(new DiscussionPostConverter());
  }

  public doc() {
    return DiscussionPost.doc(this.id, this.universityId || undefined);
  }

  public collection() {
    if (this.universityId) {
      return collection(firestore(), University.collectionName, this.universityId, 'discussion').withConverter<DiscussionPost, DiscussionPostDbModel>(new DiscussionPostConverter());
    }

    return collection(firestore(), 'global', 'discussion', 'discussion').withConverter<DiscussionPost, DiscussionPostDbModel>(new DiscussionPostConverter());
  }

  public async refresh() {
    const post = await getDoc(this.doc());
    return post.data()!;
  }

  public async addComment(comment: DiscussionComment) {
    await addDoc(comment.collection(), comment);
  }

  public static getUploadsFolder(postId: string, universityId?: string) {
    if (universityId) {
      return `universities/${universityId}/discussion/posts/${postId}`;
    }

    return `global/discussion/posts/${postId}`;
  }

  public getUploadsFolder() {
    return DiscussionPost.getUploadsFolder(this.id, this.universityId || undefined);
  }
}

export interface DiscussionPostDbModelFields {
  numComments: number;
  pinned: boolean;
}

export interface DiscussionPostDbModel extends DiscussionPostBaseDbModel, DiscussionPostDbModelFields {}

export interface DiscussionPostDbModelSafe extends DiscussionPostBaseDbModelSafe, DiscussionPostDbModelFields {}

export class DiscussionPostConverter implements FirestoreDataConverter<DiscussionPost, DiscussionPostDbModel> {
  toFirestore(post: WithFieldValue<DiscussionPost>): WithFieldValue<DiscussionPostDbModel> {
    return {
      attachments: post.attachments,
      authorId: post.authorId,
      datePosted: post.datePosted instanceof FieldValue ? post.datePosted : Timestamp.fromDate(post.datePosted as Date),
      numComments: post.numComments,
      numUpVotes: (post.upVotes as string[]).length,
      pinned: post.pinned,
      text: post.text,
      upVotes: post.upVotes,
    };
  }

  fromFirestore(snapshot: QueryDocumentSnapshot, options: SnapshotOptions): DiscussionPost {
    const data = snapshot.data(options) as DiscussionPostDbModel;
    const parentCollection = snapshot.ref.parent.parent!.id;

    return new DiscussionPost(snapshot.id, data.attachments, data.authorId, data.datePosted.toDate(), data.numComments, data.pinned, data.text, parentCollection === 'discussion' ? null : parentCollection, data.upVotes);
  }
}
