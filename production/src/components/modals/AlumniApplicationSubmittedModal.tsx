import {University} from '@creator-campus/common';
import {ModalShell, ModalHeader} from '@creator-campus/common-components';
import Button from '@mui/joy/Button';

interface Props {
  university: University;
  onClose: () => void;
}

export function AlumniApplicationSubmittedModal({university, onClose}: Props) {
  return (
    <ModalShell
      onClose={onClose}
      withCloseButton={true}
      closeOnBackgroundClick={true}
    >
      <ModalHeader
        title='Alumnus Application'
        subtitle={`Your alumnus application will be reviewed by an admin at ${university.name} and you'll receive an email once a decision has been made. If approved, you'll receive a link valid for 14 days allowing you to sign up.`}
      />
      <Button
        onClick={onClose}
        sx={{mt: 2}}
      >
        Okay
      </Button>
    </ModalShell>
  );
}
