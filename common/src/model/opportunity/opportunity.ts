import {Compensation} from './compensation';
import {Location} from './location';
import {collection, deleteDoc, doc, DocumentReference, FieldValue, type FirestoreDataConverter, getDoc, getDocs, onSnapshot, orderBy, query, QueryDocumentSnapshot, setDoc, type SnapshotOptions, Timestamp, updateDoc, where, type WithFieldValue} from 'firebase/firestore';
import {User} from '../user/user';
import {OpportunityApplication, OpportunityApplicationConverter} from './opportunityApplication';
import {generateRandomId} from '../../utils/misc';
import {sendEmail} from '../../utils/email';
import {firestore} from '../../firebase';
import {OppApplicationEmailTemplate} from '../emailTemplate/oppApplicationEmailTemplate';
import {OppApplicationReceiptEmailTemplate} from '../emailTemplate/oppApplicationReceiptEmailTemplate';
import {Project} from '../project/project';

export class Opportunity {
  static readonly collectionName = 'opportunities';

  static get col() {
    return collection(firestore(), Opportunity.collectionName);
  }

  static readonly MAX_TITLE_LEN = 100;
  static readonly MAX_DESC_LEN = 1000;
  static readonly MAX_DESC_LINES = 3;

  readonly _doc: DocumentReference<Opportunity, OpportunityDbModel>;

  constructor(
    readonly id: string,
    readonly acceptedApplicantId: string | null,
    readonly applicantIds: string[],
    readonly compensation: Compensation[],
    readonly datePosted: Date,
    readonly description: string,
    readonly fillDate: Date | null,
    readonly location: Location,
    readonly ownerId: string,
    readonly projectId: string,
    readonly title: string,
    readonly universityId: string,
  ) {
    this._doc = Opportunity.doc(id).withConverter<Opportunity, OpportunityDbModel>(new OpportunityConverter());
  }

  public static doc(id: string) {
    return doc(firestore(), Opportunity.collectionName, id).withConverter(new OpportunityConverter());
  }

  public static async create(project: Project, compensation: Compensation[], description: string, location: Location, title: string) {
    const id = generateRandomId();
    const opp = new Opportunity(id, null, [], compensation, new Date(), description, null, location, project.ownerId, project.id, title, project.universityId);

    await setDoc(Opportunity.doc(id), opp);

    return opp;
  }

  public static async fetch(id: string) {
    const snapshot = await getDoc(doc(firestore(), Opportunity.collectionName, id).withConverter(new OpportunityConverter()));
    return snapshot.data() || null;
  }

  public static queryForProject(projectId: string) {
    return query(this.col, where('projectId', '==', projectId)).withConverter(new OpportunityConverter());
  }

  public static async fetchForProject(projectId: string) {
    const snapshot = await getDocs(Opportunity.queryForProject(projectId));

    return snapshot.docs.map((d) => d.data());
  }

  private getApplicationsQuery() {
    return query(
      collection(firestore(), OpportunityApplication.collectionName),
      where('opportunityId', '==', this.id),
      where('projectOwnerId', '==', this.ownerId), // Needed for Firestore rules
      orderBy('dateAccepted', 'desc'),
      orderBy('dateApplied'),
    ).withConverter(new OpportunityApplicationConverter());
  }

  public async fetchApplications() {
    const snapshot = await getDocs(this.getApplicationsQuery());
    return snapshot.docs.map((d) => d.data());
  }

  public listenToApplications(onNext: (apps: OpportunityApplication[]) => void) {
    return onSnapshot(this.getApplicationsQuery(), (docSnap) => {
      onNext(docSnap.docs.map((d) => d.data()));
    });
  }

  public async update(compensation: Compensation[], description: string, location: Location, title: string) {
    await updateDoc(this._doc, {
      compensation: compensation.map((c) => c.id),
      description: description,
      location: location.id,
      title: title,
    });
  }

  public async apply(applicant: User, message: string, project: Project, withCv: boolean) {
    const attachments: string[] = [];
    let cvDownloadUrl: string | null = null;
    if (withCv) {
      cvDownloadUrl = await applicant.getCvDownloadUrl();
      attachments.push(cvDownloadUrl);
    }

    await OpportunityApplication.create(applicant, message, this.id, project.id, project.ownerId, this.universityId, cvDownloadUrl);

    // Send application receipts
    await sendEmail(
      project.ownerId,
      new OppApplicationEmailTemplate(
        {
          message: message,
          title: this.title,
          projName: project.name,
          theirName: applicant.name,
          address: applicant.email,
          link: applicant.getLink(),
        },
        attachments,
      ),
    );

    await sendEmail(
      applicant.email,
      new OppApplicationReceiptEmailTemplate(
        {
          message: message,
          title: this.title,
          projName: project.name,
          link: project.getLink(),
        },
        attachments,
      ),
    );
  }

  public async delete() {
    await deleteDoc(this._doc);
  }
}

export interface OpportunityDbModelNoDates {
  acceptedApplicantId: string | null;
  applicantIds: string[];
  compensation: string[];
  description: string;
  location: string;
  ownerId: string;
  projectId: string;
  title: string;
  universityId: string;
}

export interface OpportunityDbModelSafe extends OpportunityDbModelNoDates {
  datePosted: any;
  fillDate: any | null;
}

export interface OpportunityDbModel extends OpportunityDbModelNoDates {
  datePosted: Timestamp;
  fillDate: Timestamp | null;
}

export class OpportunityConverter implements FirestoreDataConverter<Opportunity, OpportunityDbModel> {
  toFirestore(opp: WithFieldValue<Opportunity>): WithFieldValue<OpportunityDbModel> {
    return {
      acceptedApplicantId: opp.acceptedApplicantId,
      applicantIds: opp.applicantIds,
      compensation: opp.compensation instanceof FieldValue ? opp.compensation : opp.compensation.map((c) => (c instanceof FieldValue ? c : c.id)),
      datePosted: opp.datePosted instanceof FieldValue ? opp.datePosted : Timestamp.fromDate(opp.datePosted as Date),
      description: opp.description,
      fillDate: opp.fillDate === null ? null : opp.fillDate instanceof FieldValue ? opp.fillDate : Timestamp.fromDate(opp.fillDate as Date),
      location: opp.location instanceof FieldValue ? opp.location : opp.location.id,
      ownerId: opp.ownerId,
      projectId: opp.projectId,
      title: opp.title,
      universityId: opp.universityId,
    };
  }

  fromFirestore(snapshot: QueryDocumentSnapshot, options: SnapshotOptions): Opportunity {
    const data = snapshot.data(options) as OpportunityDbModel;
    return new Opportunity(
      snapshot.id,
      data.acceptedApplicantId,
      data.applicantIds,
      data.compensation.map((c) => Compensation.fromId(c)!),
      data.datePosted.toDate(),
      data.description,
      data.fillDate?.toDate() || null,
      Location.fromId(data.location)!,
      data.ownerId,
      data.projectId,
      data.title,
      data.universityId,
    );
  }
}
