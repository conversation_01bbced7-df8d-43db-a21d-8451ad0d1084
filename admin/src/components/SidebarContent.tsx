import Box from '@mui/joy/Box';
import Typography from '@mui/joy/Typography';
import {Avatar, Divider, Link, List, listItemButtonClasses} from '@mui/joy';
import {ColorSchemeToggle, LoadingIndicator, SidebarItem, useAuth, useSidebarString, useUser} from '@creator-campus/common-components';
import MaintenanceModeToggle from './MaintenanceModeToggle.tsx';
import IconButton from '@mui/joy/IconButton';
import LogoutRoundedIcon from '@mui/icons-material/LogoutRounded';
import {Contacts, People, School, SpaceDashboard} from '@mui/icons-material';
import {useLocation} from 'react-router-dom';
import {CreatorCampusLogo} from './CreatorCampusLogo.tsx';

export function SidebarContent() {
  const location = useLocation();
  const {user, profilePicUrl} = useUser();
  const {logout, currentUser} = useAuth();
  const strings = useSidebarString();

  function getFirstSegment(url: string): string {
    return url.split('/')[1];
  }

  const selectedPage = getFirstSegment(location.pathname);

  if (!user) {
    return <></>;
  }

  return (
    <>
      <Box sx={{gap: 1, mt: 1}}>
        <Box sx={{mr: 'auto', ml: 'auto', display: 'flex', gap: 1, alignItems: 'center'}}>
          <CreatorCampusLogo />
        </Box>
      </Box>

      <Box
        sx={{
          minHeight: 0,
          overflow: 'hidden auto',
          flexGrow: 1,
          display: 'flex',
          flexDirection: 'column',
          [`& .${listItemButtonClasses.root}`]: {
            gap: 1.5,
          },
        }}
      >
        <List
          size='sm'
          sx={{
            gap: 1,
            '--List-nestedInsetStart': '30px',
            '--ListItem-radius': (theme) => theme.vars.radius.sm,
          }}
        >
          <SidebarItem
            text={'Dashboard'}
            leadingIcon={<SpaceDashboard />}
            selected={selectedPage === 'dashboard'}
            href={'/dashboard'}
          />
          <SidebarItem
            text={'Applications'}
            leadingIcon={<Contacts />}
            selected={selectedPage === 'applications'}
            href={'/applications'}
          />
          <SidebarItem
            text={'Users'}
            leadingIcon={<People />}
            selected={selectedPage === 'users'}
            href={'/users'}
          />
          <SidebarItem
            text={'Universities'}
            leadingIcon={<School />}
            selected={selectedPage === 'universities'}
            href={'/universities'}
          />
        </List>
      </Box>
      <Divider />
      <MaintenanceModeToggle user={user} />
      <Divider />
      <Box
        display='flex'
        justifyContent='space-between'
        alignItems='flex-end'
        sx={{mt: -1}}
      >
        <Box>
          <Link
            href='https://app.termly.io/policy-viewer/policy.html?policyUUID=f9dc8f36-24ac-4e70-a62a-eb1f1f0c640a'
            target='_blank'
            underline='hover'
            level='body-xs'
          >
            {strings.tos}
          </Link>
          <Link
            href='https://app.termly.io/policy-viewer/policy.html?policyUUID=a070d366-16e2-461f-8e88-c47ad9309334'
            target='_blank'
            underline='hover'
            level='body-xs'
          >
            {strings.privacy}
          </Link>
        </Box>
        <ColorSchemeToggle />
      </Box>
      <Divider />
      <Box sx={{display: 'flex', gap: 1, alignItems: 'center'}}>
        {profilePicUrl ? (
          <Avatar
            variant='outlined'
            size='sm'
            src={profilePicUrl}
          />
        ) : (
          <LoadingIndicator size='sm' />
        )}
        <Box sx={{minWidth: 0, flex: 1}}>
          <Typography
            level='title-sm'
            noWrap
          >
            {user?.getFirstNameLastInitial() || '...'}
          </Typography>
          <Typography
            level='body-xs'
            noWrap
          >
            {currentUser!.email}
          </Typography>
        </Box>
        <IconButton
          size='sm'
          variant='plain'
          color='neutral'
          onClick={logout}
        >
          <LogoutRoundedIcon />
        </IconButton>
      </Box>
    </>
  );
}
