import {Box} from '@mui/joy';
import Typography from '@mui/joy/Typography';
import {useCreateOpportunityString, ModalShell} from '@creator-campus/common-components';
import ButtonGroup from '@mui/joy/ButtonGroup';
import Button from '@mui/joy/Button';

interface Props {
  onNext: () => void;
  onSkip: () => void;
}

export function CreateOpportunityIntroModal({onNext, onSkip}: Props) {
  function IntroImage({size}: {size: 'small' | 'large'}) {
    return (
      <Box
        sx={{
          maxWidth: size === 'large' ? 500 : 200,
          minWidth: size === 'large' ? 300 : undefined,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          marginRight: size === 'large' ? 2 : 0,
        }}
      >
        <img
          src='/undraw_teamwork.svg'
          alt={'Opportunity created'}
          className='responsive-image'
          style={{
            objectFit: 'cover',
            width: '100%',
            height: '90%',
          }}
        />
      </Box>
    );
  }

  function IntroText() {
    const strings = useCreateOpportunityString();
    return (
      <>
        <Typography
          component='h2'
          id='modal-title'
          level='h4'
          textColor='inherit'
          fontWeight='lg'
          mb={1}
        >
          {strings.guidance_title}
        </Typography>
        <Typography
          id='modal-desc'
          textColor='text.tertiary'
          sx={{mb: 1}}
        >
          {strings.guidance_1}
        </Typography>
        <Typography
          id='modal-desc'
          textColor='text.tertiary'
          sx={{mb: 1}}
        >
          {strings.guidance_2}
        </Typography>
      </>
    );
  }

  function IntroButtons() {
    return (
      <ButtonGroup
        spacing='0.5rem'
        aria-label='spacing button group'
        sx={{mt: 2}}
      >
        <Button
          onClick={onNext}
          color='primary'
          variant='solid'
        >
          Next
        </Button>
        <Button onClick={onSkip}>Skip</Button>
      </ButtonGroup>
    );
  }

  return (
    <>
      <ModalShell
        onClose={onSkip}
        withCloseButton={true}
        closeOnBackgroundClick={true}
      >
        {/* Desktop View */}
        <Box
          sx={{
            display: {xs: 'none', md: 'flex'},
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <IntroImage size='large' />
          <Box sx={{display: 'flex', flexDirection: 'column'}}>
            <IntroText />
            <IntroButtons />
          </Box>
        </Box>

        {/* Mobile View */}
        <Box
          sx={{
            display: {xs: 'flex', md: 'none'},
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <IntroText />
          <IntroImage size='small' />
          <IntroButtons />
        </Box>
      </ModalShell>
    </>
  );
}
