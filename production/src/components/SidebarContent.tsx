import Avatar from '@mui/joy/Avatar';
import Box from '@mui/joy/Box';
import Divider from '@mui/joy/Divider';
import IconButton from '@mui/joy/IconButton';
import List from '@mui/joy/List';
import ListItem from '@mui/joy/ListItem';
import {listItemButtonClasses} from '@mui/joy/ListItemButton';
import ListItemContent from '@mui/joy/ListItemContent';
import Typography from '@mui/joy/Typography';
import DashboardRoundedIcon from '@mui/icons-material/DashboardRounded';
import Person2Icon from '@mui/icons-material/Person2';
import PeopleAltIcon from '@mui/icons-material/PeopleAlt';
import LogoutRoundedIcon from '@mui/icons-material/LogoutRounded';
import Link from '@mui/joy/Link';
import YourUniversityPlaceholder from '../assets/your-university.png';

import {demoMode, UniversityExternalLink, usingEmulators} from '@creator-campus/common';
import {ColorSchemeToggle, SidebarItem, useAuth, useSidebarString, useUser} from '@creator-campus/common-components';
import {Checklist, Flag, ForumRounded, Gamepad, GroupAdd, Lightbulb, Lock, MenuBook, OpenInNew, SpaceDashboard} from '@mui/icons-material';
import {useLocation} from 'react-router-dom';
import {AlumniCountChip} from './AlumniCountChip.tsx';
import Chip from '@mui/joy/Chip';
import KarmaChip from './KarmaChip.tsx';
import Stack from '@mui/joy/Stack';
import StaffChip from './StaffChip.tsx';
import {CreatorCampusLogo} from './CreatorCampusLogo.tsx';

export default function SidebarContent() {
  const UNI_LOGO_SIZE = '45px';

  const {logout, currentUser} = useAuth();
  const {user, profilePicUrl, numUnreadDiscussionPosts, university, universityLogoUrl} = useUser();
  const location = useLocation();
  const strings = useSidebarString();

  const isCreatorCampusAdmin = !!currentUser && usingEmulators;

  const externalLinks: UniversityExternalLink[] = demoMode ? [{label: 'Entrepreneurship Website', href: 'https://creatorcampus.io/'}] : university?.sidebarLinks || [];

  function getFirstSegment(url: string): string {
    return url.split('/')[1];
  }

  const selectedPage = getFirstSegment(location.pathname);

  if (!user || !university || location.pathname === '/onboarding' || (university.branding?.logoUpdatedAt && !universityLogoUrl)) {
    return <></>;
  }

  return (
    <>
      {demoMode ? (
        <img
          src={YourUniversityPlaceholder}
          alt='Your university placeholder'
          style={{marginTop: -15, marginBottom: -10}}
        />
      ) : (
        <>
          <Box sx={{gap: 1, mt: 1}}>
            <Box sx={{mr: 'auto', ml: 'auto', display: 'flex', gap: 1, alignItems: 'center'}}>
              {universityLogoUrl ? (
                <img
                  src={universityLogoUrl}
                  alt={`${university.name} logo`}
                  style={{minHeight: UNI_LOGO_SIZE, maxHeight: 80, minWidth: 100, maxWidth: 180, objectFit: 'contain'}}
                />
              ) : (
                <CreatorCampusLogo size={UNI_LOGO_SIZE} />
              )}
            </Box>
          </Box>
          {/*<Card size="sm" sx={{ textAlign: "center" }}>*/}
          {/*  <Typography level="title-sm" fontWeight="700">*/}
          {/*    {university?.name || "..."}*/}
          {/*  </Typography>*/}
          {/*</Card>*/}
        </>
      )}

      <Box
        sx={{
          minHeight: 0,
          overflow: 'hidden auto',
          flexGrow: 1,
          display: 'flex',
          flexDirection: 'column',
          [`& .${listItemButtonClasses.root}`]: {
            gap: 1.5,
          },
          // Hidden scrollbar
          scrollbarWidth: 'none', // Firefox
          '&::-webkit-scrollbar': {
            display: 'none', // Chrome, Safari, Edge
          },
        }}
      >
        <List
          size='sm'
          sx={{
            gap: 1,
            '--List-nestedInsetStart': '30px',
            '--ListItem-radius': (theme) => theme.vars.radius.sm,
          }}
        >
          <SidebarItem
            text={strings.projects}
            leadingIcon={<DashboardRoundedIcon />}
            selected={selectedPage === 'startups'}
            href={'/startups'}
          />
          <SidebarItem
            text={'People'}
            leadingIcon={<PeopleAltIcon />}
            selected={selectedPage === 'people'}
            href={'/people'}
          />
          {university.partner && university.hasMentors && (
            <SidebarItem
              text={'Mentors'}
              leadingIcon={<Lightbulb />}
              selected={selectedPage === 'mentors'}
              href={'/mentors'}
            />
          )}
          <SidebarItem
            text={'Discussion'}
            leadingIcon={<ForumRounded />}
            trailingIcon={!user.hasSomeAccessToApp() ? <Lock /> : numUnreadDiscussionPosts === null || numUnreadDiscussionPosts === 0 || selectedPage === 'discussion' ? null : <Chip color='warning'>{numUnreadDiscussionPosts}</Chip>}
            selected={selectedPage === 'discussion'}
            href={'/discussion'}
            enabled={user.hasSomeAccessToApp()}
            tooltip={!user.hasSomeAccessToApp() ? 'Complete your profile to unlock discussion.' : undefined}
          />
          <SidebarItem
            text={'Education'}
            leadingIcon={<MenuBook />}
            trailingIcon={!user.hasSomeAccessToApp() ? <Lock /> : null}
            selected={selectedPage === 'education'}
            href={'/education'}
            enabled={user.hasSomeAccessToApp()}
            tooltip={!user.hasSomeAccessToApp() ? 'Complete your profile to unlock education.' : undefined}
          />

          <Divider />

          {user?.staffRole?.canViewInsights() && (
            <SidebarItem
              text={'Dashboard'}
              leadingIcon={<SpaceDashboard />}
              trailingIcon={<AlumniCountChip universityId={user.universityId} />}
              selected={selectedPage === 'admin'}
              href={'/admin'}
            />
          )}

          {usingEmulators && isCreatorCampusAdmin ? (
            <SidebarItem
              text='God Mode'
              leadingIcon={<Gamepad />}
              selected={selectedPage === 'godMode'}
              href={'/godMode'}
            />
          ) : null}

          <SidebarItem
            text='Profile'
            leadingIcon={<Person2Icon />}
            selected={selectedPage === 'profile'}
            href={'/profile'}
          />

          <Divider />

          <ListItem>
            <ListItemContent>
              <Typography level='title-sm'>Helpful Links</Typography>
            </ListItemContent>
          </ListItem>

          {externalLinks.map((link, i) => (
            <SidebarItem
              key={i}
              text={link.label}
              leadingIcon={<Lightbulb />}
              trailingIcon={<OpenInNew />}
              selected={false}
              href={link.href}
              openInNewTab={true}
            />
          ))}

          <SidebarItem
            text='Report something'
            leadingIcon={<Flag />}
            trailingIcon={<OpenInNew />}
            selected={false}
            href={'https://forms.gle/RpTUeypyuBg3M9Ds8'}
            openInNewTab={true}
          />
          <SidebarItem
            text='Make a suggestion'
            leadingIcon={<Checklist />}
            trailingIcon={<OpenInNew />}
            selected={false}
            href={'https://forms.gle/T3DGzLLx9LoC2szF7'}
            openInNewTab={true}
          />
          <SidebarItem
            text='I matched with someone'
            leadingIcon={<GroupAdd />}
            trailingIcon={<OpenInNew />}
            selected={false}
            href={'https://forms.gle/HAYRPBbZg9zq6J8o8'}
            openInNewTab={true}
          />
        </List>
      </Box>
      <Box
        display='flex'
        justifyContent='space-between'
        alignItems='flex-end'
        sx={{mt: -1}}
      >
        <Box>
          <Link
            href='https://app.termly.io/policy-viewer/policy.html?policyUUID=f9dc8f36-24ac-4e70-a62a-eb1f1f0c640a'
            target='_blank'
            underline='hover'
            level='body-xs'
          >
            {strings.tos}
          </Link>
          <Link
            href='https://app.termly.io/policy-viewer/policy.html?policyUUID=a070d366-16e2-461f-8e88-c47ad9309334'
            target='_blank'
            underline='hover'
            level='body-xs'
          >
            {strings.privacy}
          </Link>
        </Box>
        <ColorSchemeToggle />
      </Box>
      <Divider />
      <Box sx={{display: 'flex', gap: 1, alignItems: 'center'}}>
        <Link
          href={'/profile'}
          pb={3}
        >
          <Avatar
            variant='outlined'
            size='sm'
            src={profilePicUrl || ''}
          />
        </Link>
        <Box sx={{minWidth: 0, flex: 1}}>
          <Typography
            level='title-sm'
            noWrap
          >
            {user.getFirstNameLastInitial()}
          </Typography>
          <Typography
            level='body-xs'
            noWrap
          >
            {user.email}
          </Typography>
          <Stack
            direction={'row'}
            spacing={0.5}
            mt={0.4}
          >
            <KarmaChip karma={user.karma} />
            {user.staffRole && <StaffChip />}
          </Stack>
        </Box>
        <IconButton
          size='sm'
          variant='plain'
          color='neutral'
          onClick={logout}
          sx={{mb: 3}}
        >
          <LogoutRoundedIcon />
        </IconButton>
      </Box>
    </>
  );
}
