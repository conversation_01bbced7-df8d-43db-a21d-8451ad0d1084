import {Box, CircularProgress, Tab, tabClasses, Tab<PERSON>ist, TabPanel, Tabs, Typography} from '@mui/joy';
import {PeopleAlt} from '@mui/icons-material';
import Card from '@mui/joy/Card';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import Tooltip from '@mui/joy/Tooltip';
import {collection, getCountFromServer, query, where} from 'firebase/firestore';
import {useEffect, useState} from 'react';
import {AdminPieChart} from './AdminPieChart';
import {AdminLineChart} from './AdminLineChart';
import {firestore, Persona, PieChartData, Role, UniversityMetrics, UserConverter, User} from '@creator-campus/common';

interface Props {
  universityId: string;
  setUniversityId: (uni: string) => void;
  numUsersData: PieChartData[] | null;
}

export function PeopleInsightsCard({universityId, setUniversityId, numUsersData}: Props) {
  const [activeUsers, setActiveUsers] = useState<number | null>(null);
  const [fracFounders, setFracFounders] = useState<number | null>(null);
  const [fracOpenToWork, setFracOpenToWork] = useState<number | null>(null);
  const [rolesData, setRolesData] = useState<PieChartData[] | null>(null);
  const [personasData, setPersonasData] = useState<PieChartData[] | null>(null);
  const [numActiveLastMonth, setNumActiveLastMonth] = useState<number | null>(null);

  useEffect(() => {
    getCountFromServer(query(collection(firestore(), User.collectionName), ...(universityId === 'All' ? [] : [where('universityId', '==', universityId)]), where('profileCompleted', '==', true), where('fake', '==', false)).withConverter(new UserConverter())).then((snapshot) => {
      const activeUsers = snapshot.data().count;
      setActiveUsers(activeUsers);

      getCountFromServer(query(collection(firestore(), User.collectionName), ...(universityId === 'All' ? [] : [where('universityId', '==', universityId)]), where('founder', '==', true), where('fake', '==', false))).then((snapshot) => {
        setFracFounders(activeUsers > 0 ? snapshot.data().count / activeUsers : 0);
      });

      getCountFromServer(query(collection(firestore(), User.collectionName), ...(universityId === 'All' ? [] : [where('universityId', '==', universityId)]), where('openToWork', '==', true), where('fake', '==', false))).then((snapshot) => {
        setFracOpenToWork(activeUsers > 0 ? snapshot.data().count / activeUsers : 0);
      });
    });

    getCountFromServer(
      query(collection(firestore(), User.collectionName), ...(universityId === 'All' ? [] : [where('universityId', '==', universityId)]), where('profileCompleted', '==', true), where('fake', '==', false), where('lastOnline', '>', new Date(Date.now() - 1000 * 60 * 60 * 24 * 30))).withConverter(new UserConverter()),
    ).then((snapshot) => {
      setNumActiveLastMonth(snapshot.data().count);
    });

    Promise.all(
      Role.values().map(async (role) => {
        const count = await getCountFromServer(query(collection(firestore(), User.collectionName), ...(universityId === 'All' ? [] : [where('universityId', '==', universityId)]), where('role', '==', role.id)));
        return {name: role.labelPlural, value: count.data().count, colour: undefined};
      }),
    ).then(setRolesData);

    Promise.all(
      Persona.values().map(async (persona) => {
        const count = await getCountFromServer(query(collection(firestore(), User.collectionName), ...(universityId === 'All' ? [] : [where('universityId', '==', universityId)]), where('persona', '==', persona.id)));
        return {name: persona.label, value: count.data().count, colour: persona.color};
      }),
    ).then(setPersonasData);
  }, [universityId]);

  return (
    <>
      <Card sx={{flex: 1}}>
        <Box sx={{display: 'flex', alignItems: 'center', mb: 2}}>
          <Typography
            startDecorator={<PeopleAlt />}
            level='h4'
          >
            People
          </Typography>
        </Box>
        <Box sx={{display: 'flex', flexDirection: 'row', alignItems: 'center', alignSelf: 'center', mb: 2, gap: 2}}>
          <Typography
            level='h1'
            sx={{fontWeight: 'bold'}}
          >
            {activeUsers}
          </Typography>
          <Typography level='body-md'>Total User{activeUsers === 1 ? '' : 's'}</Typography>
          <Tooltip
            sx={{maxWidth: 300}}
            enterTouchDelay={0}
            title={`The number of users from ${universityId} who have signed up and completed their profile.`}
            variant='solid'
          >
            <InfoOutlinedIcon fontSize='large' />
          </Tooltip>
        </Box>

        {universityId === 'All' && (
          <Box sx={{alignSelf: 'center'}}>
            <AdminPieChart
              data={numUsersData}
              showEmptyLegendItems={false}
              onSegmentClick={(data: any) => {
                setUniversityId(data.name);
              }}
            />
          </Box>
        )}

        <Box sx={{alignSelf: 'center'}}>
          <AdminLineChart
            influxField={'numUsers'}
            label={'Registrations'}
            universityId={universityId === 'All' ? undefined : universityId}
          />
        </Box>

        <Box sx={{display: 'grid', gridTemplateColumns: '29% 71%', gap: 1, alignItems: 'center'}}>
          <Typography
            level='h3'
            sx={{fontWeight: 'bold', textAlign: 'right'}}
          >
            {numActiveLastMonth === null ? <CircularProgress size='sm' /> : numActiveLastMonth}
          </Typography>
          <Box sx={{display: 'flex', gap: 0.5, alignItems: 'center'}}>
            <Typography level='body-md'>Online last 4 weeks</Typography>
          </Box>

          <Typography
            level='h3'
            sx={{fontWeight: 'bold', textAlign: 'right'}}
          >
            {fracFounders === null ? <CircularProgress size='sm' /> : Math.round(fracFounders * 100)}%
          </Typography>
          <Box sx={{display: 'flex', gap: 0.5, alignItems: 'center'}}>
            <Typography level='body-md'>Founders</Typography>
            <Tooltip
              sx={{maxWidth: 300}}
              enterTouchDelay={0}
              title={`The percentage of users from ${universityId} who currently have at least one project.`}
              variant='solid'
            >
              <InfoOutlinedIcon fontSize='large' />
            </Tooltip>
          </Box>

          <Typography
            level='h3'
            sx={{fontWeight: 'bold', textAlign: 'right'}}
          >
            {fracOpenToWork === null ? <CircularProgress size='sm' /> : Math.round(fracOpenToWork * 100)}%
          </Typography>
          <Box sx={{display: 'flex', gap: 0.5, alignItems: 'center'}}>
            <Typography level='body-md'>Startup Talent</Typography>
            <Tooltip
              sx={{maxWidth: 300}}
              enterTouchDelay={0}
              title={`The percentage of users from ${universityId} actively looking to join a project.`}
              variant='solid'
            >
              <InfoOutlinedIcon fontSize='large' />
            </Tooltip>
          </Box>
        </Box>

        <Tabs
          aria-label='tabs'
          defaultValue={0}
          sx={{bgcolor: 'transparent', alignItems: 'center', mt: 2}}
        >
          <TabList
            disableUnderline
            sx={{
              p: 0.5,
              gap: 0.5,
              borderRadius: 'xl',
              bgcolor: 'background.level1',
              [`& .${tabClasses.root}[aria-selected="true"]`]: {
                boxShadow: 'sm',
                bgcolor: 'background.surface',
              },
              maxWidth: 220,
            }}
          >
            <Tab disableIndicator>Backgrounds</Tab>
            <Tab disableIndicator>Roles</Tab>
          </TabList>
          <TabPanel value={0}>
            <AdminPieChart
              data={personasData}
              showEmptyLegendItems={true}
            />
          </TabPanel>
          <TabPanel value={1}>
            <AdminPieChart
              data={rolesData}
              showEmptyLegendItems={true}
              dynamicShadesHue={1}
            />
          </TabPanel>
        </Tabs>

        {/*<Box sx={{display: 'flex', alignItems: 'center', mb: 1}}>*/}
        {/*  <Typography*/}
        {/*    startDecorator={<School />}*/}
        {/*    level='h4'*/}
        {/*  >*/}
        {/*    Alumni*/}
        {/*  </Typography>*/}
        {/*</Box>*/}

        {/*<Stack*/}
        {/*  ml={1.5}*/}
        {/*  spacing={2.5}*/}
        {/*>*/}
        {/*  <NumericMetric*/}
        {/*    label={'Total applications'}*/}
        {/*    value={metrics.alumni.totalAlumniApplications}*/}
        {/*  />*/}
        {/*  <NumericMetric*/}
        {/*    label={'Applications approved'}*/}
        {/*    value={metrics.alumni.numAlumniApproved}*/}
        {/*  />*/}
        {/*  <NumericMetric*/}
        {/*    label={'Applications rejected'}*/}
        {/*    value={metrics.alumni.numAlumniRejected}*/}
        {/*  />*/}
        {/*  <NumericMetric*/}
        {/*    label={'Avg response time'}*/}
        {/*    value={formatHours(metrics.alumni.totalAlumniApplications === 0 ? 0 : metrics.alumni.hoursAlumniApplicationsSpentUndecided / metrics.alumni.totalAlumniApplications)}*/}
        {/*    tooltip={`The average time it takes someone at ${universityId} to respond to an alumnus application.`}*/}
        {/*  />*/}
        {/*  <NumericMetric*/}
        {/*    label={'Expired approvals'}*/}
        {/*    value={metrics.alumni.numApprovedAlumniExpired}*/}
        {/*    tooltip={'The number of approved alumni who did not claim their account before their approval expired.'}*/}
        {/*  />*/}
        {/*</Stack>*/}
      </Card>
    </>
  );
}
