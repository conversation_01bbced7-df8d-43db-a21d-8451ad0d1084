import {Button, ButtonGroup, Checkbox, Divider, FormControl, Stack, Textarea, Typography} from '@mui/joy';
import {logger, Opportunity, Project, User} from '@creator-campus/common';
import {LoadingIndicator, ModalHeader, ModalShell, useProjectCardString, useSnackbar, useUser} from '@creator-campus/common-components';
import {FormEvent, useState} from 'react';
import {OppCompensationChip} from '../OppCompensationChip.tsx';
import {OppLocationChip} from '../OppLocationChip.tsx';

interface ApplyFormElements extends HTMLFormControlsCollection {
  application: HTMLInputElement;
  checkbox: HTMLInputElement;
}

interface ApplyFormElement extends HTMLFormElement {
  readonly elements: ApplyFormElements;
}

interface Props {
  opp: Opportunity;
  project: Project;
  applicant: User;
  onCancel: () => void;
  onApply: () => void;
}

export function OpportunityApplicationModal({opp, project, applicant, onCancel, onApply}: Props) {
  const [loading, setLoading] = useState<boolean>(false);
  const [attachCv, setAttachCv] = useState<boolean>(true);

  const {showErrorSnackbar} = useSnackbar();
  const {user} = useUser();

  return (
    <ModalShell onClose={onCancel}>
      <ModalHeader title={`${opp.title} at ${project.name}`} />
      <Stack
        direction={'row'}
        spacing={0.5}
        mb={1}
      >
        {opp.compensation.map((comp) => (
          <OppCompensationChip
            key={comp.id}
            compensation={comp}
          />
        ))}
        <OppLocationChip location={opp.location} />
      </Stack>
      <Typography
        textColor='text.tertiary'
        level='body-md'
        mb={2}
      >
        {opp.description}
      </Typography>
      <Divider />
      <Typography
        textColor='text.secondary'
        level='title-md'
        mt={2}
      >
        {useProjectCardString().apply}
      </Typography>
      <Typography
        textColor='text.tertiary'
        level='body-sm'
      >
        {useProjectCardString().apply_guidance}
      </Typography>
      <form
        onSubmit={async (event: FormEvent<ApplyFormElement>) => {
          event.preventDefault();
          const formElements = event.currentTarget.elements;
          const data = {
            message: formElements.application.value,
            attachCv: !!user?.cvUploaded && formElements.checkbox.checked,
          };

          setLoading(true);

          await opp
            .apply(applicant, data.message, project, data.attachCv)
            .then(onApply)
            .catch((e) => {
              logger.error(e);
              showErrorSnackbar(e, 'Error applying to opportunity.');
            });

          setLoading(false);
        }}
      >
        <FormControl sx={{mt: 1}}>
          <Textarea
            required
            name='application'
            minRows={3}
            maxRows={10}
            placeholder='Why are you interested in this opportunity?'
            variant='soft'
            slotProps={{textarea: {maxLength: 5000}}}
          />
        </FormControl>
        {user?.cvUploaded && (
          <Checkbox
            size='sm'
            name='checkbox'
            checked={attachCv}
            sx={{mt: 1, alignItems: 'center'}}
            onChange={(event) => {
              setAttachCv(event.target.checked);
            }}
            label={<Typography level='body-sm'>Include my CV with this application</Typography>}
          />
        )}
        <ButtonGroup
          spacing='0.5rem'
          aria-label='spacing button group'
          sx={{mt: 2}}
        >
          <Button
            disabled={loading}
            color='primary'
            variant='solid'
            type='submit'
          >
            {loading ? <LoadingIndicator size='sm' /> : useProjectCardString().apply}
          </Button>
          <Button onClick={onCancel}>{useProjectCardString().cancel}</Button>
        </ButtonGroup>
      </form>
    </ModalShell>
  );
}
