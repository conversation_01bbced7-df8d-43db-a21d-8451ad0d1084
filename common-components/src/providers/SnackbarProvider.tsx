import {createContext, ReactNode, useContext, useState} from 'react';
import {logger} from '@creator-campus/common';
import {SnackbarWithDecorators} from '../SnackbarWithDecorators';

interface SnackbarContextProps {
  showSnackbar: (message: string, color?: 'success' | 'warning' | 'danger' | 'neutral', autoHideMs?: number) => void;
  showErrorSnackbar: (error: any, message?: string) => void;
}

const SnackbarContext = createContext<SnackbarContextProps | undefined>(undefined);

export function useSnackbar() {
  const context = useContext(SnackbarContext);
  if (!context) {
    throw new Error('useSnackbar must be used within an SnackbarProvider');
  }
  return context;
}

/**
 * Context to access a global snackbar.
 */
export function SnackbarProvider({children}: {children: ReactNode}) {
  const [open, setOpen] = useState<boolean>(false);
  const [text, setText] = useState<string>('');
  const [color, setColor] = useState<'success' | 'warning' | 'danger' | 'neutral'>('neutral');
  const [autoHideMs, setAutoHideMs] = useState<number>(2500);

  const showSnackbar = (message: string, color?: 'success' | 'warning' | 'danger' | 'neutral', autoHideMs?: number) => {
    setText(message);
    setColor(color || 'neutral');
    setAutoHideMs(autoHideMs || 2500);
    setOpen(true);
  };

  const showErrorSnackbar = (error: any, message?: string) => {
    logger.error(error);
    showSnackbar(message || 'Sorry, something went wrong.', 'danger');
  };

  return (
    <SnackbarContext.Provider value={{showSnackbar, showErrorSnackbar}}>
      {children}
      <SnackbarWithDecorators
        text={text}
        color={color}
        isOpen={open}
        onClose={() => setOpen(false)}
        autoHideMs={autoHideMs}
      />
    </SnackbarContext.Provider>
  );
}
