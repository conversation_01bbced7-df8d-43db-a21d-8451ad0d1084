import {useState} from 'react';
import Box from '@mui/joy/Box';
import Button from '@mui/joy/Button';
import FormControl from '@mui/joy/FormControl';
import FormLabel from '@mui/joy/FormLabel';
import Link from '@mui/joy/Link';
import Input from '@mui/joy/Input';
import Typography from '@mui/joy/Typography';
import Stack from '@mui/joy/Stack';
import {signInWithEmailAndPassword} from 'firebase/auth';
import {auth, demoMode, logger} from '@creator-campus/common';
import {LandingPageShell, LoadingIndicator, useLoginString} from '@creator-campus/common-components';
import {Alert} from '@mui/joy';
import {useNavigate} from 'react-router-dom';
import ResetPasswordModal from '../components/modals/ResetPasswordModal.tsx';
import {EMAIL_INPUT_SLOT_PROPS, PASSWORD_INPUT_SLOT_PROPS} from '../constants/styles.ts';
import {CreatorCampusLogo} from '../components/CreatorCampusLogo.tsx';

enum SignInError {
  userNotFound,
  usingUniEmail,
  wrongPassword,
}

export default function LoginPage() {
  const [email, setEmail] = useState<string>('');
  const [password, setPassword] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);

  const [signInError, setSignInError] = useState<SignInError | null>(null);
  const [resetPasswordModalOpen, setResetPasswordModalOpen] = useState<boolean>(false);

  const loginStrings = useLoginString();
  const navigate = useNavigate();

  async function signInUser() {
    setLoading(true);
    setSignInError(null);

    await signInWithEmailAndPassword(auth(), email.trim(), password)
      .then(() => {
        navigate('/startups/explore');
      })
      .catch((e) => {
        logger.error(e);

        if (e.code === 'auth/wrong-password' || e.code === 'auth/invalid-credential') {
          setSignInError(SignInError.wrongPassword);
        } else if (e.code === 'auth/user-not-found') {
          if (email.endsWith('ac.uk')) {
            setSignInError(SignInError.usingUniEmail);
          } else {
            setSignInError(SignInError.userNotFound);
          }
        }
      });

    setLoading(false);
  }

  return (
    <LandingPageShell
      lightImageUrl={'url(https://firebasestorage.googleapis.com/v0/b/creator-campus-app.appspot.com/o/site-images%2Flogin-light.png?alt=media&token=d94bd7be-da3a-4bbe-a14c-88b64ed06ee2)'}
      darkImageUrl={'url(https://firebasestorage.googleapis.com/v0/b/creator-campus-app.appspot.com/o/site-images%2Flogin-dark.png?alt=media&token=2ca307c8-e592-4551-8893-a3ec7d408197)'}
      creatorCampusLogo={<CreatorCampusLogo size='40px' />}
    >
      <Stack
        gap={1}
        sx={{pl: {xs: 1, sm: 2}, pr: {xs: 1, sm: 2}}}
      >
        <Typography level='h3'>{loginStrings.sign_in}</Typography>
        <Typography level='body-sm'>
          {demoMode ? (
            'Welcome to demo mode! Please sign in using the credentials you received.'
          ) : (
            <>
              New to Creator Campus?{' '}
              <Link
                data-cy='signup-link'
                href={`/signup/students${email.trim().length > 0 ? `?email=${email.trim()}` : ''}`}
                level='title-sm'
              >
                Join the club!
              </Link>
            </>
          )}
        </Typography>
      </Stack>
      <Stack
        gap={4}
        sx={{mt: demoMode ? 0 : 2, pl: {xs: 1, sm: 2}, pr: {xs: 1, sm: 2}}}
      >
        {signInError !== null && (
          <Alert color='danger'>
            {signInError === SignInError.wrongPassword && (
              <Typography
                level='title-sm'
                // @ts-ignore
                color='danger-500'
              >
                {loginStrings.email_or_pwd_wrong}
              </Typography>
            )}
            {signInError === SignInError.usingUniEmail && (
              <Typography
                level='title-sm'
                // @ts-ignore
                color='danger-500'
              >
                We couldn't find that account. Did you mean to use your personal email? Otherwise <Link href={'/signup'}>sign up</Link> to get started!
              </Typography>
            )}
            {signInError === SignInError.userNotFound && (
              <Typography
                level='title-sm'
                // @ts-ignore
                color='danger-500'
              >
                Looks like you don't have an account yet. Please <Link href={'/signup'}>sign up</Link> to get started!
              </Typography>
            )}
          </Alert>
        )}
        <form
          onSubmit={async (event) => {
            event.preventDefault();
            await signInUser();
          }}
        >
          <FormControl required>
            <FormLabel>{loginStrings.email}</FormLabel>
            <Input
              type='email'
              onChange={(e) => setEmail(e.target.value)}
              slotProps={EMAIL_INPUT_SLOT_PROPS}
            />
          </FormControl>
          <FormControl required>
            <FormLabel>{loginStrings.password}</FormLabel>
            <Input
              type='password'
              onChange={(e) => setPassword(e.target.value)}
              slotProps={PASSWORD_INPUT_SLOT_PROPS}
            />
          </FormControl>
          <Stack gap={3}>
            {!demoMode && (
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}
              >
                <Link
                  level='title-sm'
                  onClick={() => setResetPasswordModalOpen(true)}
                >
                  Forgot password?
                </Link>
                <ResetPasswordModal
                  open={resetPasswordModalOpen}
                  onClose={() => setResetPasswordModalOpen(false)}
                />
              </Box>
            )}
            <Button
              disabled={loading}
              type='submit'
              fullWidth
            >
              {loading ? <LoadingIndicator size='sm' /> : loginStrings.sign_in}
            </Button>
          </Stack>
        </form>
      </Stack>
    </LandingPageShell>
  );
}
