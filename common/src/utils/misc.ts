export function generateRandomId(len: number = 28): string {
  const AUTO_ID_CHARS = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';

  let autoId = '';

  for (let i = 0; i < len; i++) {
    autoId += AUTO_ID_CHARS.charAt(Math.floor(Math.random() * AUTO_ID_CHARS.length));
  }

  return autoId;
}

export function trimChar(str: string, char: string): string {
  const regex = new RegExp(`^${char}+|${char}+$`, 'g');
  return str.replace(regex, '');
}

export function openLink(link: string) {
  window.open(link, '_blank');
}
