import {useState} from 'react';
import {collection, where} from 'firebase/firestore';
import {firestore, User, UserConverter} from '@creator-campus/common';
import {FirestoreInfiniteScroll, LoadingIndicator, PageHeader, PageShell} from '@creator-campus/common-components';
import AddUserModal from '../components/AddUserModal.tsx';
import Typography from '@mui/joy/Typography';
import ApplicationCard from '../components/ApplicationCard.tsx';
import Stack from '@mui/joy/Stack';
import {Contacts} from '@mui/icons-material';
import UserApplicationModal from '../components/UserApplicationModal.tsx';

export default function ApplicationsPage() {
  const [addUserModalOpen, setAddUserModalOpen] = useState<boolean>(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);

  return (
    <>
      {selectedUser && (
        <UserApplicationModal
          user={selectedUser}
          onClose={() => setSelectedUser(null)}
        />
      )}
      <PageShell footer={false}>
        <PageHeader
          title='Applications'
          subtitle='Review new Creator Campus membership applications.'
        />
        <AddUserModal
          open={addUserModalOpen}
          setOpen={setAddUserModalOpen}
        />
        <FirestoreInfiniteScroll
          col={collection(firestore(), User.collectionName)}
          filters={[where('applicationStatus', '==', 'submitted'), where('profileCompleted', '==', true)]}
          order={{sortField: 'dateJoined', direction: 'asc'}}
          converter={new UserConverter()}
          liveUpdates={'fullCollection'}
          itemBuilder={(u) => (
            <ApplicationCard
              key={u.id}
              user={u}
              showModal={setSelectedUser}
            />
          )}
          noItemsPlaceholder={
            <Stack
              spacing={1}
              justifyContent={'center'}
              alignItems={'center'}
              sx={{height: '75vh'}}
            >
              <Contacts style={{fontSize: '24px'}} />
              <Typography>No applications to review.</Typography>
            </Stack>
          }
          loadingIndicator={
            <Stack
              justifyContent={'center'}
              alignItems={'center'}
              sx={{height: '75vh'}}
            >
              <LoadingIndicator size={'md'} />
            </Stack>
          }
          scrollFullPage={true}
          itemHeight={200}
          sx={{display: 'flex', flexDirection: 'column', scrollbarWidth: 'none', gap: 10}}
        />
      </PageShell>
    </>
  );
}
