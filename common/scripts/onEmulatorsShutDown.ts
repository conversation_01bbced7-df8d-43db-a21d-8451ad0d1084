import {exec} from 'child_process';
import {algolia} from '../src/algolia';

async function main() {
  // Make sure everything in the seed folder is tracked.
  // Files in Firebase Storage can stop being tracked if
  // they are changed and then emulators data is exported.
  console.log('Adding seed data to git...');
  exec('git add seed');


  console.log('Deleting Algolia indexes...');
  await algolia.deleteIndexes();

  console.log('Done!');
}

main();
