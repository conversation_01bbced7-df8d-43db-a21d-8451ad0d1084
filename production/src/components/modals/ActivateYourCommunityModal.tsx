import {Box, Checkbox, Stack, Typography} from '@mui/joy';
import Button from '@mui/joy/Button';
import {useEffect, useState} from 'react';
import {functions, logger, PartnershipData, PaymentInterval, University} from '@creator-campus/common';
import {LoadingIndicator, ModalHeader, ModalShell, useActivateCommunityString, useSnackbar, useUser} from '@creator-campus/common-components';
import {updateDoc} from 'firebase/firestore';
import {httpsCallable} from 'firebase/functions';
import {stripePromise} from '../../App.tsx';
import Link from '@mui/joy/Link';
import {useNavigate} from 'react-router-dom';

interface Props {
  onClose: (becamePartner: boolean) => void;
}

export function ActivateYourCommunityModal({onClose}: Props) {
  const [loading, setLoading] = useState<boolean>(false);
  const [partnershipData, setPartnershipData] = useState<PartnershipData | null>(null);
  const [checkboxChecked, setCheckboxChecked] = useState<boolean>(false);

  const {showErrorSnackbar} = useSnackbar();
  const {university} = useUser();
  const navigate = useNavigate();
  const strings = useActivateCommunityString();

  function usePaidVariant(university: University) {
    return university.almostReachedUserLimit || university.reachedUserLimit;
  }

  useEffect(() => {
    if (!university) {
      return;
    }

    university.fetchPartnershipData().then(async (pData) => {
      setPartnershipData(pData);

      if (usePaidVariant(university)) {
        // Warm up the createCheckoutSession function so it doesn't take
        // forever when they try and check out
        await warmUpFirebaseFunction(university.id);
      }
    });
  }, []);

  if (!university || !partnershipData) {
    return <></>;
  }

  const paidVariant = usePaidVariant(university);
  const titleText = university.almostReachedUserLimit || university.reachedUserLimit ? strings.modal.retain_title : strings.modal.activate_title;

  function Subtitle() {
    return (
      <Typography
        id='modal-desc'
        textColor='text.tertiary'
        sx={{mt: 1}}
      >
        {university!.reachedUserLimit ? (
          strings.modal.activate_subtitle
        ) : university!.almostReachedUserLimit ? (
          strings.modal.retain_subtitle
        ) : (
          <>
            By going live, you’ll unlock full access to all{' '}
            <Link
              onClick={() => {
                navigate('/admin/manage-subscription');
                onClose(false);
              }}
              sx={{zIndex: 1000}}
            >
              Partnership Perks
            </Link>
            . Staff, students, and recent graduates from your university will now be switched to the partnership view, and be able to network privately within your entrepreneurship community. Let’s get started!
          </>
        )}
      </Typography>
    );
  }

  async function warmUpFirebaseFunction(universityId: string) {
    const createCheckout = httpsCallable(functions(), 'createCheckoutSession');
    await createCheckout({
      universityId,
      interval: 'monthly',
      priceGbp: 0,
      baseUrl: window.location.origin,
    });
  }

  async function handleCheckout(interval: PaymentInterval) {
    const createCheckout = httpsCallable(functions(), 'createCheckoutSession');

    const result = await createCheckout({
      universityId: university!.id,
      interval,
      priceGbp: interval === 'monthly' ? partnershipData!.monthlyPriceGbp : partnershipData!.yearlyPriceGbp,
      baseUrl: window.location.origin,
    }).catch((e) => {
      showErrorSnackbar(e, 'Sorry, something went wrong.');
      logger.error(e);
      return null;
    });

    if (!result) {
      return false;
    }

    const {sessionId} = result.data as {sessionId: string};

    const stripe = await stripePromise;
    await stripe!.redirectToCheckout({sessionId});
    return true;
  }

  function Image() {
    return (
      <img
        src='/activate.png'
        alt={'Onboarding complete'}
        className='responsive-image'
        style={{
          objectFit: 'cover',
          width: '100%',
          height: '90%',
          padding: '5%',
        }}
      />
    );
  }

  function ConfirmationCheckbox() {
    if (university!.almostReachedUserLimit) {
      return null;
    }

    return (
      <Checkbox
        size='sm'
        checked={checkboxChecked}
        onChange={(event) => {
          setCheckboxChecked(event.target.checked);
        }}
        label={
          <Typography level='body-sm'>
            I've read and agree to the terms outlined in the{' '}
            <Link
              href='https://drive.google.com/file/d/1RZYL1FiFKjqxfyXtRdRnvUFGM3vuvTsz/view?usp=sharing'
              target='_blank'
              sx={{zIndex: 1000}}
            >
              partnership agreement
            </Link>
            .
          </Typography>
        }
        sx={{mt: 2, mb: 1, alignItems: 'center'}}
      />
    );
  }

  function ActionButtons() {
    return (
      <Stack
        direction={paidVariant ? 'column' : 'row'}
        spacing={1}
        mt={2}
      >
        <Button
          disabled={loading}
          variant='outlined'
          color='neutral'
          sx={{width: '100%'}}
          onClick={() => onClose(false)}
        >
          Later
        </Button>

        {paidVariant ? (
          // Paid variant
          <Stack
            direction={'row'}
            spacing={1}
          >
            <Button
              disabled={loading}
              sx={{width: '100%'}}
              onClick={async () => {
                setLoading(true);

                const success = await handleCheckout('monthly');
                if (!success) {
                  setLoading(false);
                  return;
                }
              }}
            >
              £100 / month
            </Button>
            <Button
              disabled={loading}
              sx={{width: '100%'}}
              onClick={async () => {
                setLoading(true);

                const success = await handleCheckout('yearly');
                if (!success) {
                  setLoading(false);
                  return;
                }
              }}
            >
              £1000 / year
            </Button>
          </Stack>
        ) : (
          // Free version
          <Button
            disabled={loading || !checkboxChecked}
            sx={{width: '100%'}}
            onClick={async () => {
              setLoading(true);

              // Free until user limit reached
              await updateDoc(university!.doc(), {partner: true});

              onClose(true);
              setLoading(false);
            }}
          >
            {loading ? <LoadingIndicator size={'sm'} /> : 'Activate'}
          </Button>
        )}
      </Stack>
    );
  }

  return (
    <ModalShell
      onClose={() => onClose(false)}
      withCloseButton={true}
      closeOnBackgroundClick={true}
    >
      <Box sx={{display: {xs: 'none', sm: 'flex'}, justifyContent: 'center', alignItems: 'center'}}>
        <Box
          sx={{
            maxWidth: 250,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            mr: 4,
          }}
        >
          <Image />
        </Box>
        <Box sx={{display: 'flex', flexDirection: 'column'}}>
          <ModalHeader title={titleText} />
          <Subtitle />
          <ConfirmationCheckbox />
          <ActionButtons />
        </Box>
      </Box>
      <Stack
        spacing={1}
        alignItems={'center'}
        sx={{display: {xs: 'flex', sm: 'none'}}}
      >
        <ModalHeader title={titleText} />
        <Box
          sx={{
            maxWidth: 150,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <Image />
        </Box>
        <Subtitle />
        <ConfirmationCheckbox />
        <ActionButtons />
      </Stack>
    </ModalShell>
  );
}
