import {FormEvent, useEffect, useState} from 'react';
import Button from '@mui/joy/Button';
import Typography from '@mui/joy/Typography';
import FormControl from '@mui/joy/FormControl';
import FormLabel from '@mui/joy/FormLabel';
import ButtonGroup from '@mui/joy/ButtonGroup';
import {Input} from '@mui/joy';
import {PartnershipData, University, UniversityMetrics} from '@creator-campus/common';
import {LoadingIndicator, useSnackbar} from '@creator-campus/common-components';
import {getDoc, setDoc, updateDoc} from 'firebase/firestore';
import Stack from '@mui/joy/Stack';
import ModalShell from './ModalShell.tsx';

interface Props {
  initialUniId?: string;
  onSave: () => void;
  onCancel: () => void;
}

export default function UniversityFormModal({initialUniId, onSave, onCancel}: Props) {
  const [name, setName] = useState<string>('');
  const [domain, setDomain] = useState<string>('');
  const [monthlyPriceGbp, setMonthlyPriceGbp] = useState<string>('');
  const [userLimit, setUserLimit] = useState<string>('');

  const [loadingSubmit, setLoadingSubmit] = useState<boolean>(false);
  const [initialUni, setInitialUni] = useState<University | null>(null);
  const [initialPartnershipData, setInitialPartnershipData] = useState<PartnershipData | null>(null);
  const [loadingUni, setLoadingUni] = useState<boolean>(!!initialUniId);

  const {showErrorSnackbar, showSnackbar} = useSnackbar();

  useEffect(() => {
    if (initialUniId) {
      University.fetchFromFirestore(initialUniId).then((uni) => {
        if (!uni) {
          showSnackbar('University not found.', 'danger');
          return;
        }

        setInitialUni(uni);
        setName(uni.name);
        setDomain(uni.domain);

        uni
          .fetchPartnershipData()
          .then((data) => {
            if (!data) {
              showSnackbar('Partnership data not found.', 'danger');
              return;
            }

            console.log('Found partnership data:', data);

            setInitialPartnershipData(data);
            setMonthlyPriceGbp(data.monthlyPriceGbp.toString());
            setUserLimit(data.userLimit.toString());
          })
          .finally(() => {
            setLoadingUni(false);
          });
      });
    }
  }, [initialUniId]);

  function validateForm() {
    if (name.length === 0) {
      return 'Name is required.';
    }

    if (domain.length === 0) {
      return 'Domain is required.';
    }

    if (!domain.includes('.')) {
      return 'Domain is invalid.';
    }

    if (monthlyPriceGbp.length === 0) {
      return 'Monthly price is required.';
    }

    if (!/^\d+$/.test(monthlyPriceGbp)) {
      return 'Monthly price must be a number.';
    }

    if (userLimit.length === 0) {
      return 'User limit is required.';
    }

    if (!/^\d+$/.test(userLimit)) {
      return 'User limit must be a number.';
    }

    return null;
  }

  async function handleSubmit() {
    const partnershipData = new PartnershipData(initialUniId || name, null, parseInt(monthlyPriceGbp), null, parseInt(userLimit));

    let errorMsg = null;

    if (initialUniId) {
      await updateDoc(University.doc(initialUniId), {
        name,
        domain,
      });

      await updateDoc(PartnershipData.doc(initialUniId), partnershipData);
    } else {
      const snapshot = await getDoc(University.doc(name));
      if (snapshot.exists()) {
        errorMsg = `University with ID '${name}' already exists.`;
      }

      const uniId = name;

      await setDoc(University.doc(uniId), University.defaults(uniId, domain));
      await setDoc(UniversityMetrics.doc(uniId), UniversityMetrics.defaults());
      await setDoc(PartnershipData.doc(uniId), partnershipData);
    }

    if (errorMsg) {
      showSnackbar(errorMsg, 'danger');
    } else {
      onSave();
    }
  }

  const hasBeenEdited = !initialUni ? true : name !== initialUni.name || domain !== initialUni.domain || monthlyPriceGbp !== initialPartnershipData?.monthlyPriceGbp.toString() || userLimit !== initialPartnershipData!.userLimit.toString();

  return (
    <ModalShell open={true}>
      <Typography
        component='h2'
        id='close-modal-title'
        level='h4'
        textColor='inherit'
        fontWeight='lg'
      >
        {initialUniId ? 'Update university details' : 'Add new university'}
      </Typography>
      <Typography
        id='modal-desc'
        textColor='text.tertiary'
        level='body-sm'
      >
        {initialUniId ? 'Change the details of an existing university.' : 'Enter the details for the new university.'}
      </Typography>
      {loadingUni ? (
        <LoadingIndicator size={'sm'} />
      ) : (
        <form
          onSubmit={async (e: FormEvent) => {
            e.preventDefault();

            setLoadingSubmit(true);
            const errorMsg = validateForm();

            if (errorMsg) {
              showSnackbar(errorMsg, 'danger');
            } else {
              try {
                await handleSubmit();
              } catch (e) {
                showErrorSnackbar(e, 'Error saving university.');
              }
            }

            setLoadingSubmit(false);
          }}
        >
          <Stack
            spacing={2}
            mt={2}
            mb={3}
            alignItems={'start'}
            sx={{minWidth: 500}}
          >
            {/*University name*/}
            <FormControl sx={{width: '100%'}}>
              <FormLabel>University name</FormLabel>
              <Input
                placeholder='University of Edinburgh'
                variant='outlined'
                defaultValue={name}
                onChange={(e) => setName(e.target.value)}
                required
              />
            </FormControl>

            {/*Email domain*/}
            <FormControl sx={{width: '100%'}}>
              <FormLabel>Email domain</FormLabel>
              <Input
                placeholder='ed.ac.uk'
                variant='outlined'
                defaultValue={domain}
                onChange={(e) => setDomain(e.target.value)}
                required
              />
            </FormControl>

            {/*Monthly price*/}
            <FormControl sx={{width: '100%'}}>
              <FormLabel>Monthly price (£)</FormLabel>
              <Input
                placeholder='100'
                variant='outlined'
                defaultValue={monthlyPriceGbp}
                onChange={(e) => setMonthlyPriceGbp(e.target.value)}
                required
              />
            </FormControl>

            {/*User limit*/}
            <FormControl sx={{width: '100%'}}>
              <FormLabel>Free user limit</FormLabel>
              <Input
                placeholder='300'
                variant='outlined'
                defaultValue={userLimit}
                onChange={(e) => setUserLimit(e.target.value)}
                required
              />
            </FormControl>
          </Stack>

          <ButtonGroup
            spacing='0.5rem'
            aria-label='spacing button group'
          >
            <Button
              disabled={loadingSubmit || !hasBeenEdited}
              type='submit'
              color='primary'
              variant='solid'
            >
              {loadingSubmit ? <LoadingIndicator size='sm' /> : initialUniId ? 'Save' : 'Confirm'}
            </Button>
            <Button onClick={onCancel}>Cancel</Button>
          </ButtonGroup>
        </form>
      )}
    </ModalShell>
  );
}
