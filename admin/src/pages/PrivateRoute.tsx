import PropTypes from 'prop-types';
import {Navigate} from 'react-router-dom';

import {ReactNode} from 'react';
import {useAuth, useUser} from '@creator-campus/common-components';

interface Props {
  children: ReactNode;
}

export default function PrivateRoute({children}: Props) {
  const {loadingUser, user} = useUser();
  const {loadingAuth, currentUser, logout} = useAuth();

  if (loadingAuth || loadingUser) {
    return <></>;
  }

  if (!currentUser) {
    return <Navigate to='/login' />;
  }

  if (!user) {
    return <></>;
  }

  if (!user.isCreatorCampusAdmin()) {
    logout();
    return <></>;
  }

  return children;
}

PrivateRoute.propTypes = {
  children: PropTypes.node,
};
