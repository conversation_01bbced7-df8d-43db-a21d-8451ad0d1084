import {Box} from '@mui/joy';
import Typography from '@mui/joy/Typography';
import {useConnectModalString, useNewProjectModalString, ModalShell} from '@creator-campus/common-components';
import ButtonGroup from '@mui/joy/ButtonGroup';
import Button from '@mui/joy/Button';

interface Props {
  onNext: () => void;
  onCancel: () => void;
}

export function CreateProjectIntroModal({onNext, onCancel}: Props) {
  function ModalHeader() {
    const {guidance_title} = useNewProjectModalString();

    return (
      <Typography
        component='h2'
        id='modal-title'
        level='h4'
        textColor='inherit'
        fontWeight='lg'
        mb={1}
      >
        {guidance_title}
      </Typography>
    );
  }

  function ModalImage({size}: {size: 'small' | 'large'}) {
    const style = size === 'large' ? {maxWidth: 500, minWidth: 300} : {maxWidth: 200};

    return (
      <Box sx={{...style, display: 'flex', justifyContent: 'center', alignItems: 'center'}}>
        <img
          src='/new-startup.png'
          alt={'Project created'}
          className='responsive-image'
          style={{
            objectFit: 'cover',
            width: '100%',
            height: '90%',
            padding: '5%',
          }}
        />
      </Box>
    );
  }

  function ModalDescription() {
    const {guidance_1, guidance_2, guidance_3} = useNewProjectModalString();
    return (
      <>
        <Typography
          id='modal-desc'
          textColor='text.tertiary'
          sx={{mb: 1}}
        >
          {guidance_1}
        </Typography>
        <Typography
          id='modal-desc'
          textColor='text.tertiary'
          sx={{mb: 1}}
        >
          {guidance_2}
        </Typography>
        <Typography
          id='modal-desc'
          textColor='text.tertiary'
          sx={{mb: 1}}
        >
          {guidance_3}
        </Typography>
      </>
    );
  }

  function ModalButtons({onNext, onCancel}: Props) {
    const {cancel} = useConnectModalString();
    return (
      <ButtonGroup
        spacing='0.5rem'
        aria-label='spacing button group'
        sx={{mt: 2}}
      >
        <Button
          onClick={onNext}
          color='primary'
          variant='solid'
        >
          Next
        </Button>
        <Button onClick={onCancel}>{cancel}</Button>
      </ButtonGroup>
    );
  }

  return (
    <>
      <ModalShell onClose={onCancel}>
        {/* Desktop View */}
        <Box
          sx={{
            display: {xs: 'none', md: 'flex'},
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <ModalImage size='large' />
          <Box sx={{display: 'flex', flexDirection: 'column'}}>
            <ModalHeader />
            <ModalDescription />
            <ModalButtons
              onNext={onNext}
              onCancel={onCancel}
            />
          </Box>
        </Box>

        {/* Mobile View */}
        <Box
          sx={{
            display: {xs: 'flex', md: 'none'},
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <ModalHeader />
          <ModalImage size='small' />
          <ModalDescription />
          <ModalButtons
            onNext={onNext}
            onCancel={onCancel}
          />
        </Box>
      </ModalShell>
    </>
  );
}
