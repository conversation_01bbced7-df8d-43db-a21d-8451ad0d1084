import {Opportunity, Project} from '@creator-campus/common';
import {useCreateOpportunityString, ModalShell, ModalHeader} from '@creator-campus/common-components';
import {useState} from 'react';
import ShareOpportunityModal from './ShareOpportunityModal.tsx';
import {CreateEditOpportunityForm} from '../CreateEditOpportunityForm.tsx';

interface Props {
  project: Project;
  initialOpp?: Opportunity;
  onClose: (success: boolean, createdOpp: Opportunity | null) => void;
}

export function CreateEditOpportunityModal({project, initialOpp, onClose}: Props) {
  const [createdOpp, setCreatedOpp] = useState<Opportunity | null>(null);

  return (
    <>
      {createdOpp && (
        <ShareOpportunityModal
          onClose={() => onClose(true, createdOpp)}
          project={project}
        />
      )}
      {!createdOpp && (
        <ModalShell onClose={() => onClose(false, createdOpp)}>
          <ModalHeader
            title={initialOpp ? 'Edit opportunity' : useCreateOpportunityString().heading}
            subtitle={useCreateOpportunityString().subheading}
          />
          <CreateEditOpportunityForm
            project={project}
            initialOpp={initialOpp}
            onClose={(success, createdOpp) => {
              if (createdOpp) {
                setCreatedOpp(createdOpp);
              } else {
                onClose(success, createdOpp);
              }
            }}
          />
        </ModalShell>
      )}
    </>
  );
}
