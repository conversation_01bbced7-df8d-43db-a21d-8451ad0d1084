import Apartment from '@mui/icons-material/Apartment';
import LocationOn from '@mui/icons-material/LocationOn';
import JoinFull from '@mui/icons-material/JoinFull';
import {ElementType} from 'react';

export class Location {
  static readonly REMOTE = new Location('Remote', 'Remote', LocationOn);
  static readonly HYBRID = new Location('Hybrid', 'Hybrid', JoinFull);
  static readonly IN_PERSON = new Location('In-person', 'In-person', Apartment);

  private constructor(
    readonly id: string,
    readonly label: string,
    readonly icon: ElementType,
  ) {}

  static fromId(id: string): Location | undefined {
    return this.values().find((l) => l.id === id);
  }

  static values(): Location[] {
    return Object.values(Location);
  }

  static getRandom() {
    return this.values()[Math.floor(Math.random() * this.values().length)]!;
  }
}
