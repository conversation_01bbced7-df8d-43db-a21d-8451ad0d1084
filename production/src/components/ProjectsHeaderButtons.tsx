import {Badge, Button, Stack} from '@mui/joy';
import {BorderColor, Explore} from '@mui/icons-material';
import {useUser, useScreenWidth} from '@creator-campus/common-components';
import Tooltip from '@mui/joy/Tooltip';
import Box from '@mui/joy/Box';

interface Props {
  selectedItem: 'explore' | 'my-startups';
}

export default function ProjectsHeaderButtons({selectedItem}: Props) {
  const {user} = useUser();
  const screenWidth = useScreenWidth();
  const iconOnly = screenWidth < 600;

  return (
    <Stack
      direction='row'
      spacing={1}
      alignItems='center'
      justifyContent='flex-end'
    >
      <Button
        variant={selectedItem === 'explore' ? 'soft' : 'plain'}
        color='neutral'
        component='a'
        href='/startups/explore'
        size='sm'
      >
        <Stack mr={{xs: 0, sm: 0.5}}>
          <Explore />
        </Stack>
        {!iconOnly && 'Explore'}
      </Button>

      <Tooltip title={user?.hasFullAccessToApp() ? '' : 'Complete your profile to add your own startup.'}>
        <Box>
          <Button
            variant={selectedItem === 'my-startups' ? 'soft' : 'plain'}
            color='neutral'
            component='a'
            href='/startups/my-startups'
            size='sm'
            disabled={!user?.hasFullAccessToApp()}
            sx={{position: 'relative', whiteSpace: 'nowrap'}}
          >
            {user?.hasUnreadOppApplications && (
              <Badge
                badgeContent=''
                color='primary'
                size='md'
                sx={{position: 'absolute', top: 2, right: 2}}
              />
            )}
            <Stack mr={{xs: 0, sm: 0.5}}>
              <BorderColor />
            </Stack>
            {!iconOnly && 'My Startups'}
          </Button>
        </Box>
      </Tooltip>
    </Stack>
  );
}
