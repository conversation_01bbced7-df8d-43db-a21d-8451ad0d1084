import './App.css';
import Login from './pages/LoginPage.tsx';
import {BrowserRouter, Navigate, Route, Routes} from 'react-router-dom';
import theme from './theme.tsx';

import {CssVarsProvider} from '@mui/joy/styles';
import GlobalStyles from '@mui/joy/GlobalStyles';
import CssBaseline from '@mui/joy/CssBaseline';
import {AuthProvider, NotFoundPage, PageLayout, Sidebar, SnackbarProvider, TextProvider, UserProvider} from '@creator-campus/common-components';
import DashboardPage from './pages/DashboardPage.tsx';
import PrivateRoute from './pages/PrivateRoute.tsx';
import Box from '@mui/joy/Box';
import {SidebarContent} from './components/SidebarContent.tsx';
import ManageUniversitiesPage from './pages/ManageUniversitiesPage.tsx';
import ManageUsersPage from './pages/ManageUsersPage.tsx';
import ApplicationsPage from './pages/ApplicationsPage.tsx';
import {EmulatorStatusWrapper} from 'production/src/providers/EmulatorStatusWrapper.tsx';

export default function App() {
  return (
    <CssVarsProvider
      defaultMode='light'
      disableTransitionOnChange
      theme={theme}
    >
      <CssBaseline />
      <GlobalStyles
        styles={{
          ':root': {
            '--Collapsed-breakpoint': '769px', // form will stretch when viewport is below `769px`
            '--Cover-width': '50vw', // must be `vw` only
            '--Form-maxWidth': '800px',
            '--Transition-duration': '0.4s', // set to `none` to disable transition
          },
        }}
      />
      <EmulatorStatusWrapper>
        <AuthProvider>
          <SnackbarProvider>
            <UserProvider>
              <TextProvider>
                <BrowserRouter future={{v7_startTransition: true, v7_relativeSplatPath: true}}>
                  <Box sx={{display: 'flex', minHeight: '100dvh'}}>
                    <Routes>
                      <Route
                        path='/'
                        element={
                          <PrivateRoute>
                            <Sidebar>
                              <SidebarContent />
                            </Sidebar>
                            <PageLayout />
                          </PrivateRoute>
                        }
                      >
                        <Route
                          index
                          element={
                            <Navigate
                              replace
                              to={'/dashboard/overview'}
                            />
                          }
                        />
                        <Route
                          path='/home'
                          element={
                            <Navigate
                              replace
                              to={'/dashboard/overview'}
                            />
                          }
                        />
                        <Route
                          path={'/dashboard'}
                          element={
                            <Navigate
                              replace
                              to={'/dashboard/overview'}
                            />
                          }
                        />
                        <Route
                          path={'/dashboard/:tab'}
                          element={<DashboardPage />}
                        />
                        <Route
                          path={'/applications'}
                          element={<ApplicationsPage />}
                        />
                        <Route
                          path={'/users'}
                          element={<ManageUsersPage />}
                        />
                        <Route
                          path={'/universities'}
                          element={<ManageUniversitiesPage />}
                        />
                      </Route>
                      <Route
                        path='/login'
                        element={<Login />}
                      />
                      <Route
                        path='*'
                        element={<NotFoundPage />}
                      />
                    </Routes>
                  </Box>
                </BrowserRouter>
              </TextProvider>
            </UserProvider>
          </SnackbarProvider>
        </AuthProvider>
      </EmulatorStatusWrapper>
    </CssVarsProvider>
  );
}
