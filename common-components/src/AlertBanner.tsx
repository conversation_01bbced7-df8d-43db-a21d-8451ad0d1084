import {Warning} from '@mui/icons-material';
import {<PERSON><PERSON>, But<PERSON>} from '@mui/joy';
import Stack from '@mui/joy/Stack';
import {ReactNode} from 'react';

interface Props {
  text: string;
  color?: 'primary' | 'neutral' | 'success' | 'warning' | 'danger';
  startDecorator?: ReactNode;
  actionButton?: {text: string; action: () => void};
  sx?: object;
}

export function AlertBanner({text, color, startDecorator = <Warning />, sx, actionButton}: Props) {
  return (
    <>
      <Alert
        startDecorator={startDecorator}
        variant='soft'
        color={color}
        sx={sx}
      >
        <Stack
          alignItems={'center'}
          justifyContent={'space-between'}
          width={'100%'}
          direction={'row'}
          spacing={1}
        >
          {text}
          {actionButton && (
            <Button
              variant='soft'
              color={color}
              sx={{mr: 1, maxWidth: '30%'}}
              onClick={actionButton.action}
            >
              {actionButton.text}
            </Button>
          )}
        </Stack>
      </Alert>
    </>
  );
}
