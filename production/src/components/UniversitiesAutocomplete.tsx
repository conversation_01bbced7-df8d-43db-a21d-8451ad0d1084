import {useEffect, useState} from 'react';
import {University} from '@creator-campus/common';
import {Autocomplete} from '@mui/joy';
import {matchSorter} from 'match-sorter';

interface Props {
  onSelect: (universityId: string) => void;
}

export function UniversitiesAutocomplete({onSelect}: Props) {
  const [options, setOptions] = useState<{id: string; name: string}[]>([]);
  console.log(options);

  // Fetch universities list on mount
  useEffect(() => {
    University.fetchFullList().then(setOptions);
  }, []);

  return (
    <Autocomplete
      placeholder='Select university...'
      options={options}
      getOptionLabel={(o) => o.name}
      required
      autoHighlight
      disableListWrap
      onInputChange={(_, value) => value && onSelect(value)}
      filterOptions={(options, {inputValue}) => matchSorter(options, inputValue.trim(), {keys: ['name']}).slice(0, 200)}
    />
  );
}
