import {Box, FormControl, FormHelperText, FormLabel, Input, Typography} from '@mui/joy';
import {ChangeEvent, useState} from 'react';
import {useSignupString} from '@creator-campus/common-components';
import {PASSWORD_INPUT_SLOT_PROPS} from '../constants/styles.ts';

interface Props {
  longPassword: boolean;
  setLongPassword: (long: boolean) => void;
  onChange?: (value: string) => void;
}

export default function CreatePasswordInput({longPassword, setLongPassword, onChange}: Props) {
  const [password, setPassword] = useState('');
  const [score, setScore] = useState(0);

  function evaluatePasswordStrength(password: string) {
    let s = 0;
    if (!password) return 0;
    if (password.length >= 8) s++;
    if (/[a-z]/.test(password)) s++;
    if (/[A-Z]/.test(password)) s++;
    if (/\d/.test(password)) s++;
    if (/[^A-Za-z0-9]/.test(password)) s++;
    return s;
  }

  function handleChange(event: ChangeEvent<HTMLInputElement>) {
    const value = event.target.value;
    setPassword(value);

    const strengthScore = evaluatePasswordStrength(value);
    setScore(strengthScore);

    setLongPassword(true);
    onChange?.(value);
  }

  const getStrengthColor = (score: number): string => {
    switch (score) {
      case 0:
        return '#ccc';
      case 1:
        return '#f44336';
      case 2:
        return '#ff7043';
      case 3:
        return '#ffb300';
      case 4:
        return '#66bb6a';
      case 5:
        return '#2e7d32';
      default:
        return '#ccc';
    }
  };

  const getStrengthLabel = (score: number): string => {
    switch (score) {
      case 0:
        return '';
      case 1:
        return 'Fragile';
      case 2:
        return 'Delicate';
      case 3:
        return 'Sturdy';
      case 4:
        return 'Jacked';
      case 5:
        return 'Indestructible';
      default:
        return '';
    }
  };

  const strengthColor = getStrengthColor(score);
  const strengthLabel = getStrengthLabel(score);

  return (
    <FormControl
      required
      error={!longPassword}
    >
      <FormLabel>{useSignupString().password}</FormLabel>
      <Input
        data-cy={'password-input'}
        type='password'
        name='password'
        value={password}
        onChange={handleChange}
        slotProps={PASSWORD_INPUT_SLOT_PROPS}
      />

      {!longPassword ? (
        <Typography
          level='body-sm'
          color='danger'
        >
          Password must be at least 8 characters long
        </Typography>
      ) : (
        <>
          <Box
            sx={{
              height: 8,
              width: '100%',
              backgroundColor: '#e0e0e0',
              borderRadius: 4,
              overflow: 'hidden',
              mt: 1,
            }}
          >
            <Box
              sx={{
                height: '100%',
                width: `${(score / 5) * 100}%`,
                backgroundColor: strengthColor,
                transition: 'width 0.3s ease, background-color 0.3s ease',
              }}
            />
          </Box>
          <FormHelperText>
            Strength:{' '}
            <Typography
              level='title-sm'
              sx={{color: strengthColor}}
            >
              {strengthLabel}
            </Typography>
          </FormHelperText>
        </>
      )}
    </FormControl>
  );
}
