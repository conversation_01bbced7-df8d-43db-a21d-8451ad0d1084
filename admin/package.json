{"name": "admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build"}, "dependencies": {"@creator-campus/common": "*", "@creator-campus/common-components": "*", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fontsource/inter": "^5.1.1", "@google-analytics/data": "^4.12.0", "@influxdata/influxdb-client": "^1.35.0", "@mui/icons-material": "^5.16.14", "@mui/joy": "5.0.0-beta.51", "@mui/material": "^5.16.14", "@mui/system": "^6.4.3", "algoliasearch": "^5.19.0", "axios": "^1.7.9", "dotenv": "^16.4.7", "firebase": "^11.2.0", "marked": "^13.0.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-infinite-scroll-component": "^6.1.0", "react-instantsearch": "^7.15.0", "react-markdown": "^9.0.3", "react-router-dom": "^6.28.2", "recharts": "^2.15.0", "rehype-raw": "^7.0.0"}, "devDependencies": {"@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react": "^4.3.4", "eslint": "^8.57.1", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.18", "typescript": "^5.7.3", "vite": "^5.4.11"}}