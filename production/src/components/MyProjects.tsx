import Box from '@mui/joy/Box';
import Typography from '@mui/joy/Typography';

import {useEffect, useState} from 'react';
import {onSnapshot} from 'firebase/firestore';
import {CircularProgress, Grid} from '@mui/joy';
import ProjectCard from './ProjectCard';
import CreateEditProjectModal from './modals/CreateEditProjectModal.tsx';
import {Project, User} from '@creator-campus/common';
import {useAuth, useSnackbar} from '@creator-campus/common-components';
import {CreateProjectIntroModal} from './modals/CreateProjectIntroModal.tsx';
import {CreateOpportunityIntroModal} from './modals/CreateOpportunityIntroModal.tsx';
import {CreateEditOpportunityModal} from './modals/CreateEditOpportunityModal.tsx';
import {Add} from '@mui/icons-material';
import Button from '@mui/joy/Button';

export default function MyProjects() {
  const [user, setUser] = useState<User | null>(null);
  const [newProject, setNewProject] = useState<Project | null>(null);

  const [createProjectModalsStage, setCreateProjectModalsStage] = useState<number>(-1);

  const {currentUser} = useAuth();
  const {showSnackbar} = useSnackbar();

  useEffect(() => {
    const unsub = onSnapshot(User.doc(currentUser!.uid), async (snapshot) => {
      const updatedUser = snapshot.data()!;
      if (user && updatedUser.projectIds.length < user.projectIds.length) {
        showSnackbar('Startup deleted.', 'success');
      }

      setUser(updatedUser);
    });

    return () => unsub();
  }, []);

  if (!user) {
    return (
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100vh',
        }}
      >
        <CircularProgress size='lg' />
      </Box>
    );
  }

  return (
    <>
      {createProjectModalsStage === 0 && (
        <CreateProjectIntroModal
          onNext={() => setCreateProjectModalsStage(1)}
          onCancel={() => setCreateProjectModalsStage(-1)}
        />
      )}
      {createProjectModalsStage === 1 && (
        <CreateEditProjectModal
          onSave={(_event, createdProject) => {
            setNewProject(createdProject);
            setCreateProjectModalsStage(2);
          }}
          onClose={() => setCreateProjectModalsStage(-1)}
        />
      )}
      {createProjectModalsStage === 2 && (
        <CreateOpportunityIntroModal
          onNext={() => setCreateProjectModalsStage(3)}
          onSkip={() => setCreateProjectModalsStage(-1)}
        />
      )}
      {createProjectModalsStage === 3 && (
        <CreateEditOpportunityModal
          project={newProject!}
          onClose={() => setCreateProjectModalsStage(-1)}
        />
      )}

      <Button
        variant='outlined'
        color='primary'
        startDecorator={<Add />}
        sx={{alignSelf: 'left', mr: 'auto'}}
        onClick={() => (user.projectIds.length === 0 ? setCreateProjectModalsStage(0) : setCreateProjectModalsStage(1))}
      >
        New Startup
      </Button>

      {user.projectIds.length === 0 ? (
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '70vh',
          }}
        >
          <Typography level='body-md'>Startups you create will appear here.</Typography>
        </Box>
      ) : (
        <Grid
          container
          spacing={2}
          sx={{flexGrow: 1, mt: 2}}
        >
          {user.projectIds.map((projectId) => (
            // 12 = occupy full viewport width
            // 6 = occupy half viewport width
            <Grid
              key={projectId}
              xs={12}
              sm={12}
              md={12}
              lg={6}
              xl={6}
            >
              <ProjectCard
                projectId={projectId}
                editMode={true}
              />
            </Grid>
          ))}
        </Grid>
      )}
    </>
  );
}
