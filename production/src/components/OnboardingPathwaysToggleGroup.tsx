import {Checkbox, List, ListItem, ListItemDecorator} from '@mui/joy';
import Typography from '@mui/joy/Typography';
import {ReactNode, useEffect, useState} from 'react';
import Divider from '@mui/joy/Divider';
import Stack from '@mui/joy/Stack';
import {OnboardingPathway, OnboardingPathwayId} from '@creator-campus/common';

interface Props {
  initialRoles?: OnboardingPathway[];
  onRolesChanged: (selectedIds: OnboardingPathway[]) => void;
  onRoleSelected: (role: OnboardingPathway | null) => void;
}

export default function OnboardingPathwaysToggleGroup({initialRoles, onRolesChanged, onRoleSelected}: Props) {
  const [selectedIds, setSelectedIds] = useState<OnboardingPathway[]>(initialRoles || []);

  const checkboxTexts: Record<OnboardingPathwayId, ReactNode> = {
    founder: (
      <>
        <Typography level={'title-sm'}>Founder</Typography>
        <Typography level={'body-sm'}> or </Typography>
        <Typography level={'title-sm'}>Freelancer</Typography>
        <Typography level={'body-sm'}> with a business</Typography>
      </>
    ),
    talent: (
      <>
        <Typography level={'title-sm'}>Startup Talent</Typography>
        <Typography level={'body-sm'}> looking for opportunities</Typography>
      </>
    ),
    supporter: (
      <>
        <Typography level={'title-sm'}>Supporter</Typography>
        <Typography level={'body-sm'}> (not a Founder or Startup Talent)</Typography>
      </>
    ),
  };

  useEffect(() => {
    onRolesChanged(selectedIds);
  }, [selectedIds]);

  return (
    <List
      key={'onboarding-pathways-toggle'}
      sx={{
        '--List-gap': '0.5rem',
        '--ListItem-paddingY': '1rem',
        '--ListItem-radius': '8px',
        '--ListItemDecorator-size': '32px',
      }}
    >
      {OnboardingPathway.values().map((pathway) => (
        <Stack key={pathway.id}>
          {pathway === OnboardingPathway.SUPPORTER && <Divider sx={{mt: 1.5, mb: 0.5}} />}
          <ListItem
            variant='outlined'
            sx={{boxShadow: 'sm'}}
          >
            <ListItemDecorator>
              <pathway.icon />
            </ListItemDecorator>
            <Checkbox
              overlay
              checked={selectedIds.includes(pathway)}
              value={pathway.id}
              label={checkboxTexts[pathway.id]}
              onChange={(event) => {
                onRoleSelected(event.target.checked ? pathway : null);

                if (event.target.checked) {
                  if (pathway === OnboardingPathway.SUPPORTER) {
                    setSelectedIds([pathway]);
                  } else {
                    setSelectedIds((prev) => [...prev.filter((p) => p !== OnboardingPathway.SUPPORTER), pathway]);
                  }
                } else {
                  setSelectedIds((prev) => prev.filter((p) => p !== pathway));
                }
              }}
              sx={{flexGrow: 1, flexDirection: 'row-reverse'}}
              slotProps={{
                action: ({checked}) => ({
                  sx: (theme) => ({
                    ...(checked && {
                      inset: -1,
                      border: '2px solid',
                      borderColor: theme.vars.palette.primary[500],
                    }),
                  }),
                }),
              }}
            />
          </ListItem>
        </Stack>
      ))}
    </List>
  );
}
