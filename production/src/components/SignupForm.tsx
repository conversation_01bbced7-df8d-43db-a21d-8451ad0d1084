import {FormEvent, useState} from 'react';
import Button from '@mui/joy/Button';
import FormControl from '@mui/joy/FormControl';
import FormLabel from '@mui/joy/FormLabel';
import Link from '@mui/joy/Link';
import Input from '@mui/joy/Input';
import Typography from '@mui/joy/Typography';
import Stack from '@mui/joy/Stack';
import {createUserWithEmailAndPassword, deleteUser, sendEmailVerification} from 'firebase/auth';
import {useNavigate} from 'react-router-dom';
import {Alert, FormHelperText, Tooltip} from '@mui/joy';
import {Check, InfoOutlined, LinkedIn, WarningRounded, Launch} from '@mui/icons-material';
import {auth, getDomainFromEmail, logger, MembershipApplicationReceiptEmailTemplate, NonexistentUniversity, Role, sendEmail, trimChar, University, User} from '@creator-campus/common';
import {useSnackbar, LoadingIndicator, useSignupString} from '@creator-campus/common-components';
import CreatePasswordInput from './CreatePasswordInput.tsx';
import {EMAIL_INPUT_SLOT_PROPS, GRADUATION_YEAR_INPUT_SLOT_PROPS, PASSWORD_INPUT_SLOT_PROPS} from '../constants/styles.ts';
import {TermsAndConditionsCheckbox} from './TermsAndConditionsCheckbox.tsx';
import {isValidLinkedinUsername} from '../utils.tsx';
import {UniversitiesAutocomplete} from './UniversitiesAutocomplete.tsx';
import {MembershipCriteriaAlert} from './MembershipCriteriaAlert.tsx';

interface Props {
  variant: 'students' | 'graduates';
  initialEmail?: string;
}

export default function SignupForm({variant, initialEmail}: Props) {
  // Graduates only
  const [graduationYear, setGraduationYear] = useState<string>('');
  const [linkedinUsername, setLinkedinUsername] = useState<string>('');

  const [email, setEmail] = useState<string>(initialEmail || '');
  const [password, setPassword] = useState<string>('');
  const [confirmPassword, setConfirmPassword] = useState<string>('');

  const [universityId, setUniversityId] = useState<string | NonexistentUniversity | null>(null);

  const [passwordsMatch, setPasswordsMatch] = useState(true);
  const [accountAlreadyExists, setAccountAlreadyExists] = useState(false);
  const [longPassword, setLongPassword] = useState(true);
  const [linkedinOkay, setLinkedinOkay] = useState(true);

  const [loading, setLoading] = useState(false);

  const navigate = useNavigate();
  const {showErrorSnackbar, showSnackbar} = useSnackbar();

  async function signUpNewUser(email: string, password: string, universityId: string, linkedin: string | null) {
    if (await User.existsWithUniversityEmail(email)) {
      showSnackbar('Somebody has already signed up using this email.', 'danger');
      return;
    }

    await createUserWithEmailAndPassword(auth(), email, password)
      .then(async (creds) => {
        const user = creds.user;

        await sendEmailVerification(user).catch(logger.error);

        await User.create(user.uid, email, universityId, variant === 'graduates' ? {role: Role.GRADUATE, universityEmail: null, linkedin} : undefined).catch(async (e) => {
          logger.error(e);
          showErrorSnackbar(e, 'Error initialising user. Please try again.');
          await deleteUser(user).catch(logger.error);
          return;
        });

        await sendEmail(email, new MembershipApplicationReceiptEmailTemplate());

        navigate('/onboarding');
      })
      .catch((e) => {
        if (e.code === 'auth/email-already-in-use') {
          setAccountAlreadyExists(true);
        } else {
          logger.error(e);
          showErrorSnackbar(e, 'Error signing up.');
        }
      });
  }

  function checkFormValid(linkedin: string | null, password: string, confirmPassword: string) {
    const isLinkedinValid = linkedin === null || isValidLinkedinUsername(linkedin);
    setLinkedinOkay(isLinkedinValid);

    const doPasswordsMatch = password === confirmPassword;
    setPasswordsMatch(doPasswordsMatch);

    const isLongPassword = password.length >= 8;
    setLongPassword(isLongPassword);

    return isLinkedinValid && doPasswordsMatch && isLongPassword;
  }

  return (
    <Stack
      gap={2}
      sx={{mt: 1, pl: {xs: 0, sm: 2}, pr: {xs: 0, sm: 2}}}
    >
      {variant === 'graduates' && <MembershipCriteriaAlert />}
      {universityId instanceof NonexistentUniversity && (
        <Alert color='danger'>
          <Typography
            level='title-sm'
            //@ts-ignore
            color='danger-500'
          >
            Are you using your university email? If so, perhaps your university isn't part of Creator Campus yet. Request access{' '}
            <Link
              href='https://forms.gle/LP7bHedT5LJY9NE36'
              target='_blank'
            >
              here.
            </Link>
          </Typography>
        </Alert>
      )}
      {accountAlreadyExists && (
        <Alert color='danger'>
          <Typography
            data-cy={'account-already-exists-alert'}
            level='title-sm'
            //@ts-ignore
            color='danger-500'
          >
            An account has already been created with that email. Please <Link href={'/login'}>sign in</Link> instead.
          </Typography>
        </Alert>
      )}
      <form
        onSubmit={async (event: FormEvent) => {
          event.preventDefault();

          let linkedin: string | null = trimChar(linkedinUsername.trim(), '/').trim();
          linkedin = linkedin === '' ? null : linkedin;

          if (checkFormValid(linkedin, password, confirmPassword)) {
            setLoading(true);
            await signUpNewUser(email, password, universityId as string, linkedin).catch((e) => showErrorSnackbar(e, 'Error signing up.'));
            setLoading(false);
          }
        }}
      >
        <FormControl required>
          <Stack direction={'row'}>
            <FormLabel>{variant === 'graduates' ? 'Personal email' : 'University email'}</FormLabel>
          </Stack>
          <Input
            type='email'
            autoComplete='email'
            data-cy={'email-input'}
            value={email}
            placeholder={variant === 'graduates' ? '<EMAIL>' : '<EMAIL>'}
            onChange={(e) => {
              setEmail(e.target.value);
              if (variant === 'students') {
                setAccountAlreadyExists(false);
              }
            }}
            slotProps={EMAIL_INPUT_SLOT_PROPS}
            onBlur={async (event) => {
              if (variant === 'students') {
                setUniversityId(null);
              }

              const email = event.target.value;
              if (variant === 'graduates' || email.length === 0) {
                return;
              }

              const domain = getDomainFromEmail(email);

              const uniId = await University.idFromDomain(domain);
              setUniversityId(uniId);
            }}
            endDecorator={
              variant === 'graduates' ? null : typeof universityId === 'string' ? (
                <Tooltip title={universityId}>
                  <Check color='success' />
                </Tooltip>
              ) : universityId instanceof NonexistentUniversity ? (
                // @ts-ignore
                <WarningRounded color='danger' />
              ) : null
            }
          />
          {variant === 'students' && (
            <Typography
              level={'body-sm'}
              sx={{mt: 0.5}}
            >
              {typeof universityId === 'string' && universityId}
            </Typography>
          )}
        </FormControl>

        {variant === 'graduates' && (
          <FormControl required>
            <FormLabel>University</FormLabel>
            <UniversitiesAutocomplete onSelect={setUniversityId} />
          </FormControl>
        )}

        {variant === 'graduates' && (
          <FormControl required>
            <FormLabel>Graduation year</FormLabel>
            <Input
              type='number'
              value={graduationYear}
              placeholder={`${new Date().getFullYear() - 2}`}
              onChange={(e) => {
                setGraduationYear(e.target.value);
              }}
              slotProps={GRADUATION_YEAR_INPUT_SLOT_PROPS}
            />
          </FormControl>
        )}

        {variant === 'graduates' && (
          <FormControl
            sx={{display: {sm: 'flex-column', md: 'flex-row'}}}
            error={!linkedinOkay}
          >
            <FormLabel>
              LinkedIn (Recommended){' '}
              <Link
                href='https://www.linkedin.com/in/'
                target='_blank'
                aria-label='Open LinkedIn settings'
              >
                <Launch sx={{fontSize: 16}} />
              </Link>
            </FormLabel>
            <Input
              key='linkedin'
              value={linkedinUsername}
              slotProps={{input: {maxLength: 100}}}
              placeholder='username'
              startDecorator={
                <Button
                  variant='soft'
                  color='neutral'
                  startDecorator={<LinkedIn />}
                >
                  linkedin.com/in/
                </Button>
              }
              onChange={(event) => {
                setLinkedinUsername(event.target.value);
              }}
            />
            {!linkedinOkay ? (
              <FormHelperText sx={{mt: 2}}>
                {' '}
                <InfoOutlined />
                Invalid LinkedIn URL
              </FormHelperText>
            ) : (
              <></>
            )}
          </FormControl>
        )}

        <CreatePasswordInput
          longPassword={longPassword}
          setLongPassword={setLongPassword}
          onChange={(value) => {
            setPassword(value);
          }}
        />

        <FormControl
          required
          error={!passwordsMatch}
        >
          <FormLabel>{useSignupString().confirm_password}</FormLabel>
          <Input
            data-cy={'confirm-password-input'}
            type='password'
            autoComplete='off'
            value={confirmPassword}
            onChange={(e) => {
              setConfirmPassword(e.target.value);
              setPasswordsMatch(true);
            }}
            slotProps={PASSWORD_INPUT_SLOT_PROPS}
          />
          {!passwordsMatch ? (
            <FormHelperText>
              {' '}
              <InfoOutlined />
              {useSignupString().password_match}
            </FormHelperText>
          ) : (
            <></>
          )}
        </FormControl>

        <Stack
          gap={3}
          sx={{mt: 1}}
        >
          <TermsAndConditionsCheckbox />
          <Button
            data-cy={'submit-button'}
            disabled={loading || typeof universityId !== 'string'}
            type='submit'
            fullWidth
          >
            {loading ? <LoadingIndicator size='sm' /> : 'Join now'}
          </Button>
        </Stack>
        <Typography
          level='body-xs'
          textAlign='center'
        >
          {useSignupString().email_verification}
        </Typography>
      </form>
    </Stack>
  );
}
