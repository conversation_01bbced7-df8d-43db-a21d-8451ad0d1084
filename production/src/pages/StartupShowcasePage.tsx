import Stack from '@mui/joy/Stack';
import Typography from '@mui/joy/Typography';
import {CreatorCampusLogo} from '../components/CreatorCampusLogo';
import {useNavigate, useParams} from 'react-router-dom';
import {useEffect, useState} from 'react';
import {httpsCallable} from 'firebase/functions';
import {functions, Project, StartupShowcaseProject, University} from '@creator-campus/common';
import {LoadingIndicator, PageShell} from '@creator-campus/common-components';
import ProjectCard from '../components/ProjectCard.tsx';
import {Grid} from '@mui/joy';
import {Dashboard} from '@mui/icons-material';
import Divider from '@mui/joy/Divider';

export function StartupShowcasePage() {
  const [startups, setStartups] = useState<Project[] | null>(null);
  const [uniLogoUrl, setUniLogoUrl] = useState<string | null>(null);

  const {universityId, token} = useParams();
  const navigate = useNavigate();
  const uniId = universityId?.replace(/-/g, ' ') || '';

  useEffect(() => {
    if (!universityId || !token) {
      navigate('/login');
      return;
    }

    University.getLogoUrl(uniId).then(setUniLogoUrl);

    httpsCallable(
      functions(),
      'validateStartupShowcaseToken',
    )({universityId: uniId, token})
      .then((response) => {
        const data = response.data as {valid: boolean; startups: StartupShowcaseProject[]};

        if (data.valid) {
          setStartups(data.startups.map((p) => Project.fromDbModel(p.id, p)));
        } else {
          navigate('/login');
        }
      })
      .catch(() => {
        navigate('/login');
      });
  }, []);

  const loading = startups === null || uniLogoUrl === null;

  if (loading) {
    return (
      <Stack
        height={'95vh'}
        justifyContent={'center'}
        alignItems={'center'}
      >
        <LoadingIndicator size='md' />
        <Typography
          level='body-sm'
          mt={2}
          maxWidth={300}
          textAlign={'center'}
        >
          We're finding all the startups to showcase. This could take up to a minute...
        </Typography>
      </Stack>
    );
  }

  function ShowcaseHeader() {
    return (
      <Stack width={'100%'}>
        <Stack
          direction={'row'}
          alignItems={'center'}
          width={'100%'}
          justifyContent={'space-between'}
          mb={3}
        >
          <Stack
            direction={'row'}
            spacing={1.5}
            alignItems={'center'}
            justifyContent={'space-between'}
            width={'100%'}
          >
            <img
              src={uniLogoUrl!}
              alt={`${universityId} logo`}
              style={{height: 52}}
            />
            <Typography
              level='h4'
              sx={{fontWeight: 'bold'}}
            >
              STARTUP SHOWCASE
            </Typography>
          </Stack>
        </Stack>

        <Divider />

        <Typography
          level='body-sm'
          mt={1}
        >
          {startups!.length} startup{startups!.length === 1 ? '' : 's'} showcased
        </Typography>
      </Stack>
    );
  }

  return (
    <PageShell>
      <Stack
        spacing={4}
        alignItems={'center'}
        justifyContent={'space-between'}
        width={'100%'}
        minHeight={'90vh'}
      >
        <ShowcaseHeader />

        {/* Grid of project cards */}
        {startups.length === 0 ? (
          <Stack
            spacing={0.5}
            alignItems={'center'}
            justifyContent={'center'}
          >
            <Dashboard />
            <Typography level='title-md'>No startups</Typography>
            <Typography level='body-sm'>{uniId} isn't showcasing any startups at the moment.</Typography>
          </Stack>
        ) : (
          <Grid
            container
            spacing={2}
            sx={{width: '100%', flexGrow: 1}}
          >
            {startups.map((project) => (
              <Grid
                key={project.id}
                xs={12}
                sm={12}
                md={12}
                lg={6}
                xl={4}
              >
                <ProjectCard
                  projectId={project.id}
                  hit={project}
                  editMode={false}
                  privacyMode={true}
                  withOpportunities={false}
                  withEditButton={false}
                />
              </Grid>
            ))}
          </Grid>
        )}

        <CreatorCampusLogo size={'40px'} />
      </Stack>
    </PageShell>
  );
}
