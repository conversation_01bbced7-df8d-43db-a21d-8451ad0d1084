import Stack from '@mui/joy/Stack';
import Typography from '@mui/joy/Typography';
import ProjectsHeaderButtons from './ProjectsHeaderButtons.tsx';

import {useExploreString} from '@creator-campus/common-components';

interface Props {
  selectedTab: 'explore' | 'my-startups';
}

export default function ProjectsHeader({selectedTab}: Props) {
  const strings = useExploreString();

  return (
    <Stack sx={{mb: 2}}>
      <Stack
        direction='row'
        justifyContent='space-between'
        sx={{width: '100%'}}
      >
        <Stack>
          <Typography level='h2'>{strings.header}</Typography>
          <Typography
            level='body-md'
            color='neutral'
          >
            {selectedTab === 'explore' ? 'Find exciting student and graduate startups to join.' : 'Create, view and manage your startups and opportunities.'}
          </Typography>
        </Stack>
        <ProjectsHeaderButtons selectedItem={selectedTab} />
      </Stack>
    </Stack>
  );
}
