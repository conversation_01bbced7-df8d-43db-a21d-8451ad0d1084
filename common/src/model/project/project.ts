import {deleteDoc, doc, DocumentReference, FieldValue, type FirestoreDataConverter, getDoc, QueryDocumentSnapshot, setDoc, type SnapshotOptions, Timestamp, updateDoc, type WithFieldValue} from 'firebase/firestore';
import {ProjectTag} from './projectTag';
import {firestore, storage} from '../../firebase';
import {User} from '../user/user';
import {generateRandomId} from '../../utils/misc';
import {getDownloadURL, ref, type StorageReference, uploadBytes} from 'firebase/storage';
import {Compensation} from '../opportunity/compensation';
import {Location} from '../opportunity/location';

export class Project {
  // The name of the Firestore collection for projects
  static readonly collectionName = 'projects';
  static readonly MAX_NAME_LEN = 100;
  static readonly MAX_SUMMARY_LEN = 150;
  static readonly MAX_DESC_LEN = 1000;

  // This Project document in Firestore
  readonly _doc: DocumentReference<Project, ProjectDbModel>;
  // A reference to this Project's folder in Firebase Storage
  readonly _logoStorage: StorageReference;
  readonly _logoUploadStorage: StorageReference;

  constructor(
    readonly id: string,
    readonly _compensations: Compensation[],
    readonly creationDate: Date,
    readonly description: string,
    readonly featuredUntil: Date | null,
    readonly hidden: boolean,
    readonly _locations: Location[],
    readonly logoUpdatedAt: Date,
    readonly name: string,
    readonly numOpenOpportunities: number,
    readonly opportunityTitles: string[],
    readonly opportunityDescriptions: string[],
    readonly ownerId: string,
    readonly score: number,
    readonly summary: string,
    readonly tags: ProjectTag[],
    readonly universityId: string,
    readonly website: string | null,
  ) {
    this._doc = Project.doc(id).withConverter<Project, ProjectDbModel>(new ProjectConverter());
    this._logoStorage = ref(storage(), `/projects/${id}/logo_200x200`);
    this._logoUploadStorage = ref(storage(), `/projects/${id}/logo`);
  }

  /**
   * Returns a reference to a project document in Firestore.
   * @param id The ID of the project.
   */
  public static doc(id: string) {
    return doc(firestore(), Project.collectionName, id).withConverter(new ProjectConverter());
  }

  public static async create(owner: User, name: string, summary: string, desc: string, tags: ProjectTag[], website: string | null, hidden: boolean = false) {
    const id = generateRandomId();
    const project = new Project(id, [], new Date(), desc, null, hidden, [], new Date(), name, 0, [], [], owner.id, 0, summary, tags, owner.universityId, website);

    await setDoc(Project.doc(id), project);

    return project;
  }

  /**
   * Fetches a Project from Firestore.
   * @param id The ID of the project to fetch.
   */
  public static async fetch(id: string) {
    const snapshot = await getDoc(doc(firestore(), Project.collectionName, id).withConverter(new ProjectConverter()));
    return snapshot.data() || null;
  }

  /**
   * Updates a project document given fields that are allowed to be updated.
   * @param name The new project name.
   * @param summary The new project summary.
   * @param desc The new project description.
   * @param tags The new project tags.
   * @param website The new project website URL.
   */
  public async update(name: string, summary: string, desc: string, tags: ProjectTag[], website: string) {
    await updateDoc(this._doc, {
      description: desc,
      name: name,
      summary: summary,
      tags: tags.map((t) => t.label),
      website: website,
    });
  }

  /**
   * Deletes a project in Firestore.
   * @param id The ID of the project document to delete.
   */
  public static async delete(id: string) {
    await deleteDoc(Project.doc(id));
  }

  /**
   * Deletes this Project in Firestore.
   */
  public async delete() {
    await deleteDoc(this._doc);
  }

  /**
   * Returns the public url to download this Project's logo.
   */
  public async getLogoUrl() {
    return await getDownloadURL(this._logoStorage);
  }

  /**
   * Updates this Project's logo in Firebase Storage.
   * @param logo The file of the logo to upload.
   */
  public async updateLogo(logo: File) {
    await uploadBytes(this._logoUploadStorage, logo);
    // Wait to make sure the file has uploaded before we notify listeners
    await new Promise((f) => setTimeout(f, 50));
    await this.notifyLogoUpdated();
  }

  /**
   * Notifies snapshot listeners of this Project that the logo has just been updated.
   * @private
   */
  private async notifyLogoUpdated() {
    await updateDoc(this._doc, {
      logoUpdatedAt: new Date(),
    });
  }

  /**
   * Returns a URL link to this project.
   * @param absolute Whether the URL should be absolute (i.e. include https://creatorcampus.io). Usually a link copied to the clipboard should be absolute. If using with the `navigate` function, use a relative url.
   */
  public getLink(absolute: boolean = true) {
    const url = `share/startup/${this.id}`;
    return absolute ? `${location.origin}/${url}` : url;
  }

  public static fromDbModel(projectId: string, project: ProjectDbModel | ProjectDbModelSafe | ProjectDbModelAlgolia) {
    return new Project(
      projectId,
      project.compensations.map((c) => Compensation.fromId(c)!),
      typeof project.creationDate === 'number' ? new Date(project.creationDate) : project.creationDate instanceof Timestamp ? project.creationDate.toDate() : project.creationDate,
      project.description,
      project.featuredUntil ? (typeof project.featuredUntil === 'number' ? new Date(project.featuredUntil) : project.featuredUntil instanceof Timestamp ? project.featuredUntil.toDate() : project.featuredUntil) : null,
      project.hidden,
      project.locations.map((l) => Location.fromId(l)!),
      typeof project.logoUpdatedAt === 'number' ? new Date(project.logoUpdatedAt) : project.logoUpdatedAt instanceof Timestamp ? project.logoUpdatedAt.toDate() : project.logoUpdatedAt,
      project.name,
      project.numOpenOpportunities,
      project.opportunityTitles,
      project.opportunityDescriptions,
      project.ownerId,
      project.score,
      project.summary,
      project.tags.map((t) => ProjectTag.fromId(t)!),
      project.universityId,
      project.website,
    );
  }
}

export interface ProjectDbModelNoDates {
  compensations: string[];
  description: string;
  fake: boolean;
  hidden: boolean;
  locations: string[];
  name: string;
  numOpenOpportunities: number;
  opportunityTitles: string[];
  opportunityDescriptions: string[];
  ownerId: string;
  score: number;
  summary: string;
  tags: string[];
  universityId: string;
  website: string | null;
}

export interface ProjectDbModelSafe extends ProjectDbModelNoDates {
  creationDate: number;
  featuredUntil: number | null;
  logoUpdatedAt: number;
}

/**
 * The structure of a project document in Firestore.
 */
export interface ProjectDbModel extends ProjectDbModelNoDates {
  creationDate: Timestamp;
  featuredUntil: Timestamp | null;
  logoUpdatedAt: Timestamp;
}

export interface ProjectDbModelAlgolia extends ProjectDbModelNoDates {
  creationDate: any;
  featuredUntil: any | null;
  logoUpdatedAt: any;
}

export interface StartupShowcaseProject extends ProjectDbModelSafe {
  id: string;
}

/**
 * Converts between project Firestore documents and Project objects.
 */
export class ProjectConverter implements FirestoreDataConverter<Project, ProjectDbModel> {
  toFirestore(project: WithFieldValue<Project>): WithFieldValue<ProjectDbModel> {
    return {
      compensations: project._compensations instanceof FieldValue ? project._compensations : (project._compensations as Compensation[]).map((c) => c.id),
      creationDate: project.creationDate instanceof FieldValue ? project.creationDate : Timestamp.fromDate(project.creationDate as Date),
      description: project.description,
      fake: false,
      featuredUntil: project.featuredUntil === null || project.featuredUntil instanceof FieldValue ? project.featuredUntil : Timestamp.fromDate(project.featuredUntil as Date),
      hidden: project.hidden,
      locations: project._locations instanceof FieldValue ? project._locations : (project._locations as Location[]).map((l) => l.id),
      logoUpdatedAt: project.logoUpdatedAt instanceof FieldValue ? project.logoUpdatedAt : Timestamp.fromDate(project.logoUpdatedAt as Date),
      name: project.name,
      numOpenOpportunities: project.numOpenOpportunities,
      opportunityTitles: project.opportunityTitles,
      opportunityDescriptions: project.opportunityDescriptions,
      ownerId: project.ownerId,
      score: project.score,
      summary: project.summary,
      tags: project.tags instanceof FieldValue ? project.tags : (project.tags as ProjectTag[]).map((t) => t.label),
      universityId: project.universityId,
      website: project.website,
    };
  }

  fromFirestore(snapshot: QueryDocumentSnapshot, options: SnapshotOptions): Project {
    const data = snapshot.data(options) as ProjectDbModel;
    return Project.fromDbModel(snapshot.id, data);
  }
}
