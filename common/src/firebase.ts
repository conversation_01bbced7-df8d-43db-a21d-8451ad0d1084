import {initializeApp, FirebaseApp} from 'firebase/app';
import {connectAuthEmulator, getAuth} from 'firebase/auth';
import {connectFirestoreEmulator, getFirestore} from 'firebase/firestore';
import {connectStorageEmulator, getStorage} from 'firebase/storage';
import {connectFunctionsEmulator, getFunctions} from 'firebase/functions';
import {config} from 'dotenv';

if (typeof location === 'undefined') {
  config({path: '.env'});
}

export const usingEmulators = typeof location !== 'undefined' && location.hostname === 'localhost';
export const demoMode = typeof location !== 'undefined' && location.host.includes('demo');

// Initialize Firebase config
const firebaseConfig = {
  apiKey: demoMode ? process.env.REACT_APP_FIREBASE_DEMO_API_KEY : process.env.REACT_APP_FIREBASE_API_KEY,
  authDomain: demoMode ? 'creator-campus-demo.firebaseapp.com' : 'creator-campus-app.firebaseapp.com',
  projectId: demoMode ? 'creator-campus-demo' : 'creator-campus-app',
  storageBucket: demoMode ? 'creator-campus-demo.firebasestorage.app' : 'creator-campus-app.appspot.com',
  messagingSenderId: demoMode ? process.env.REACT_APP_FIREBASE_DEMO_MESSAGING_SENDER_ID : process.env.REACT_APP_FIREBASE_MESSAGING_SENDER_ID,
  appId: demoMode ? process.env.REACT_APP_FIREBASE_DEMO_APP_ID : process.env.REACT_APP_FIREBASE_APP_ID,
  measurementId: demoMode ? process.env.REACT_APP_FIREBASE_DEMO_MEASUREMENT_ID : process.env.REACT_APP_FIREBASE_MEASUREMENT_ID,
};

let app: FirebaseApp | null = null;
const getApp = () => {
  if (!app) {
    app = initializeApp(firebaseConfig);
  }

  return app;
};

export const auth = () => getAuth(getApp());
export const firestore = () => getFirestore(getApp());
export const storage = () => getStorage(getApp());
export const functions = () => getFunctions(getApp(), 'europe-west2');

if (usingEmulators) {
  connectAuthEmulator(auth(), 'http://127.0.0.1:9099/');
  connectFirestoreEmulator(firestore(), '127.0.0.1', 8080);
  connectStorageEmulator(storage(), '127.0.0.1', 9199);
  connectFunctionsEmulator(functions(), '127.0.0.1', 5001);
  console.log('Connected to emulators.');
}

// Demo config
const firebaseConfigDemo = {
  apiKey: process.env.REACT_APP_FIREBASE_DEMO_API_KEY,
  authDomain: 'creator-campus-demo.firebaseapp.com',
  projectId: 'creator-campus-demo',
  storageBucket: 'creator-campus-demo.firebasestorage.app',
  messagingSenderId: process.env.REACT_APP_FIREBASE_DEMO_MESSAGING_SENDER_ID,
  appId: process.env.REACT_APP_FIREBASE_DEMO_APP_ID,
  measurementId: process.env.REACT_APP_FIREBASE_DEMO_MEASUREMENT_ID,
};

let demoApp: FirebaseApp | null = null;
const getDemoApp = () => {
  if (!demoApp) {
    demoApp = initializeApp(firebaseConfigDemo, 'demo');
  }

  return demoApp;
};

export const demoFirestore = () => getFirestore(getDemoApp());
