import {onDocumentCreated, onDocumentDeleted, onDocumentUpdated} from 'firebase-functions/v2/firestore';
import * as admin from 'firebase-admin';
import {FieldValue} from 'firebase-admin/firestore';
import {Project, StartupShowcaseProject, University, UniversityDbModelSafe} from '@creator-campus/common';
import {onCall} from 'firebase-functions/https';
import {initSentryForFunction} from './utils';
import * as Sentry from '@sentry/node';

export const onUniversityCreated = onDocumentCreated(
  {
    document: 'universities/{universityId}',
  },
  async (event) => {
    initSentryForFunction('onUniversityCreated');

    const universityId = event.params.universityId;
    console.log(`Detected university ${universityId} created.`);

    Sentry.addBreadcrumb({
      message: `University created: ${universityId}`,
      category: 'university',
      level: 'info',
      data: { universityId }
    });

    Sentry.addBreadcrumb({
      message: 'Adding university to global university list',
      category: 'firestore',
      level: 'info',
      data: { universityId }
    });

    await admin
      .firestore()
      .collection('global')
      .doc('platform')
      .update({
        universityList: FieldValue.arrayUnion(universityId),
      });

    Sentry.addBreadcrumb({
      message: 'Successfully added university to global list',
      category: 'firestore',
      level: 'info',
      data: { universityId }
    });
  },
);

export const onUniversityUpdated = onDocumentUpdated(
  {
    document: 'universities/{universityId}',
  },
  async (event) => {
    initSentryForFunction('onUniversityUpdated');

    if (!event.data) {
      console.error('Event data is undefined!');
      Sentry.addBreadcrumb({
        message: 'Event data is undefined in onUniversityUpdated',
        category: 'function',
        level: 'error'
      });
      return;
    }

    const universityId = event.params.universityId;
    console.log(`Detected university ${universityId} updated.`);

    const beforeData = event.data.before.data() as UniversityDbModelSafe;
    const afterData = event.data.after.data() as UniversityDbModelSafe;

    Sentry.addBreadcrumb({
      message: `University updated: ${universityId}`,
      category: 'university',
      level: 'info',
      data: {
        universityId,
        partnerStatusChanged: beforeData.partner !== afterData.partner,
        paymentStateChanged: beforeData.paymentState !== afterData.paymentState,
        reachedUserLimitChanged: beforeData.reachedUserLimit !== afterData.reachedUserLimit,
        showcaseEnabledChanged: beforeData.showcaseEnabled !== afterData.showcaseEnabled
      }
    });

    if (beforeData.partner !== afterData.partner) {
      console.log('Detected partner status changed.');

      Sentry.addBreadcrumb({
        message: 'Partner status changed',
        category: 'university',
        level: 'info',
        data: {
          universityId,
          wasPartner: beforeData.partner,
          isPartner: afterData.partner
        }
      });

      await admin
        .firestore()
        .collection(University.collectionName)
        .doc(universityId)
        .update({
          partnerSince: afterData.partner ? FieldValue.serverTimestamp() : null,
        });

      Sentry.addBreadcrumb({
        message: 'Updated partnerSince timestamp',
        category: 'firestore',
        level: 'info',
        data: {
          universityId,
          partnerSince: afterData.partner ? 'now' : null
        }
      });
    }

    const justReachedUserLimit = !beforeData.reachedUserLimit && afterData.reachedUserLimit;
    if (justReachedUserLimit && afterData.paymentState !== 'paying') {
      console.log('Detected user limit reached and not paying. Disabling partnership.');

      Sentry.addBreadcrumb({
        message: 'User limit reached and not paying, disabling partnership',
        category: 'university',
        level: 'warning',
        data: {
          universityId,
          paymentState: afterData.paymentState,
          reachedUserLimit: afterData.reachedUserLimit
        }
      });

      await admin.firestore().collection(University.collectionName).doc(universityId).update({
        partner: false,
      });

      Sentry.addBreadcrumb({
        message: 'Disabled partnership due to user limit reached',
        category: 'firestore',
        level: 'info',
        data: { universityId }
      });
    }
  },
);

export const onUniversityDeleted = onDocumentDeleted(
  {
    document: 'universities/{universityId}',
  },
  async (event) => {
    initSentryForFunction('onUniversityDeleted');

    const universityId = event.params.universityId;
    console.log(`Detected university ${universityId} deleted.`);

    Sentry.addBreadcrumb({
      message: `University deleted: ${universityId}`,
      category: 'university',
      level: 'info',
      data: { universityId }
    });

    Sentry.addBreadcrumb({
      message: 'Removing university from global university list',
      category: 'firestore',
      level: 'info',
      data: { universityId }
    });

    await admin
      .firestore()
      .collection('global')
      .doc('platform')
      .update({
        universityList: FieldValue.arrayRemove(universityId),
      });

    Sentry.addBreadcrumb({
      message: 'Successfully removed university from global list',
      category: 'firestore',
      level: 'info',
      data: { universityId }
    });
  },
);

export const validateStartupShowcaseToken = onCall(async (request) => {
  initSentryForFunction('validateStartupShowcaseToken');

  const {universityId, token} = request.data;
  console.log(`Validating startup showcase token for university ${universityId}...`);

  Sentry.addBreadcrumb({
    message: 'Validating startup showcase token',
    category: 'function',
    level: 'info',
    data: { universityId, hasToken: !!token }
  });

  if (!universityId || !token) {
    console.error(`At least one required argument is undefined. universityId=${universityId}, token=${token}`);

    Sentry.addBreadcrumb({
      message: 'Missing required arguments for startup showcase validation',
      category: 'validation',
      level: 'error',
      data: { universityId: !!universityId, token: !!token }
    });

    return {valid: false};
  }

  const uniSnapshot = await admin.firestore().collection(University.collectionName).doc(universityId).get();
  if (!uniSnapshot.exists) {
    console.error(`University ${universityId} does not exist.`);

    Sentry.addBreadcrumb({
      message: 'University does not exist',
      category: 'validation',
      level: 'error',
      data: { universityId }
    });

    return {valid: false};
  }

  const university = uniSnapshot.data() as UniversityDbModelSafe;
  if (!university.showcaseEnabled) {
    console.error(`University ${universityId} does not have showcase enabled.`);

    Sentry.addBreadcrumb({
      message: 'University does not have showcase enabled',
      category: 'validation',
      level: 'error',
      data: { universityId, showcaseEnabled: university.showcaseEnabled }
    });

    return {valid: false};
  }

  const privateUniSnapshot = await admin.firestore().collection(University.collectionName).doc(universityId).collection('private').doc('startupShowcase').get();
  if (!privateUniSnapshot.exists) {
    Sentry.addBreadcrumb({
      message: 'Private startup showcase document does not exist',
      category: 'validation',
      level: 'error',
      data: { universityId }
    });

    return {valid: false};
  }

  const privateData = privateUniSnapshot.data() as {token: string};

  if (privateData.token !== token) {
    console.error(`Tokens do not match. universityId=${universityId}, token=${token}`);

    Sentry.addBreadcrumb({
      message: 'Startup showcase tokens do not match',
      category: 'validation',
      level: 'error',
      data: { universityId }
    });

    return {valid: false};
  }

  Sentry.addBreadcrumb({
    message: 'Startup showcase token validated successfully',
    category: 'validation',
    level: 'info',
    data: { universityId }
  });

  const projectsSnapshot = await admin.firestore().collection(Project.collectionName).where('universityId', '==', universityId).get();

  Sentry.addBreadcrumb({
    message: 'Fetched university projects for startup showcase',
    category: 'firestore',
    level: 'info',
    data: { universityId, projectCount: projectsSnapshot.size }
  });

  const startups = projectsSnapshot.docs.map((d) => {
    const data = d.data();
    return {...data, id: d.id, creationDate: data.creationDate.toMillis(), logoUpdatedAt: data.logoUpdatedAt.toMillis()} as StartupShowcaseProject;
  });

  return {
    valid: true,
    startups,
  };
});
