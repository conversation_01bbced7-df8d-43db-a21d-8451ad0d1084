import {useEffect} from 'react';
import {Timestamp, updateDoc} from 'firebase/firestore';
import {logger, Project, UserDbModelSafe} from '@creator-campus/common';
import {useUser} from '@creator-campus/common-components';

export default function OnUserVisit() {
  const {user} = useUser();

  useEffect(() => {
    if (!user) {
      return;
    }

    console.log('Welcome to Creator Campus!');

    const twelveHours = 1000 * 60 * 60 * 12;
    const lastOnlineChangedSignificantly = Math.abs(user.lastOnline.getTime() - Date.now()) > twelveHours;

    if (lastOnlineChangedSignificantly) {
      logger.debug('Updating last online...');
      const userDocUpdate: Partial<UserDbModelSafe> = {lastOnline: Timestamp.now()};

      if (user.hideProfile) {
        // Unhide profile
        logger.debug('Unhiding profile...');
        userDocUpdate['hideProfile'] = false;

        // Unhide associated projects
        for (const projectId of user.projectIds) {
          logger.debug(`Unhiding project: ${projectId}`);
          updateDoc(Project.doc(projectId), {hidden: false});
        }
      }

      updateDoc(user._doc, userDocUpdate);
    }
  }, [user?.id]);

  // TODO: start tracking page views when we're paying for InfluxDB Cloud
  // const {pathname} = useLocation();
  // useEffect(() => {
  //   if (!user) {
  //     return;
  //   }
  //
  //   const metric = newAppMetric(user)
  //     .intField('pageViews', 1)
  //     .tag('page', pathname);
  //
  //   influxWriter.writePoint(metric);
  // }, [pathname]);

  return null;
}
