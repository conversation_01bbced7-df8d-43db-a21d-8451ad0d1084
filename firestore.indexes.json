{"indexes": [{"collectionGroup": "alumniApplications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "ip", "order": "ASCENDING"}, {"fieldPath": "ts", "order": "ASCENDING"}]}, {"collectionGroup": "discussion", "queryScope": "COLLECTION", "fields": [{"fieldPath": "numUpVotes", "order": "DESCENDING"}, {"fieldPath": "datePosted", "order": "DESCENDING"}]}, {"collectionGroup": "opportunityApplications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "opportunityId", "order": "ASCENDING"}, {"fieldPath": "projectOwnerId", "order": "ASCENDING"}, {"fieldPath": "dateAccepted", "order": "DESCENDING"}, {"fieldPath": "dateApplied", "order": "ASCENDING"}]}, {"collectionGroup": "people", "queryScope": "COLLECTION", "fields": [{"fieldPath": "numCompleteProfileRemindersSent", "order": "ASCENDING"}, {"fieldPath": "profileCompleted", "order": "ASCENDING"}, {"fieldPath": "dateJoined", "order": "ASCENDING"}]}, {"collectionGroup": "people", "queryScope": "COLLECTION", "fields": [{"fieldPath": "profileCompleted", "order": "ASCENDING"}, {"fieldPath": "dateJoined", "order": "ASCENDING"}]}, {"collectionGroup": "people", "queryScope": "COLLECTION", "fields": [{"fieldPath": "universityId", "order": "ASCENDING"}, {"fieldPath": "staffRole", "order": "ASCENDING"}]}, {"collectionGroup": "projects", "queryScope": "COLLECTION", "fields": [{"fieldPath": "university", "order": "ASCENDING"}, {"fieldPath": "numOpenOpportunities", "order": "ASCENDING"}]}, {"collectionGroup": "projects", "queryScope": "COLLECTION", "fields": [{"fieldPath": "universityId", "order": "ASCENDING"}, {"fieldPath": "numOpenOpportunities", "order": "ASCENDING"}]}], "fieldOverrides": [{"collectionGroup": "mail", "fieldPath": "delivery.expireAt", "ttl": true, "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}, {"order": "DESCENDING", "queryScope": "COLLECTION"}, {"arrayConfig": "CONTAINS", "queryScope": "COLLECTION"}]}]}