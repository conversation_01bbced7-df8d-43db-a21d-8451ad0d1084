import {logger, Project} from '@creator-campus/common';
import {LoadingIndicator, useMyProjectsString, useSnackbar} from '@creator-campus/common-components';
import Button from '@mui/joy/Button';
import DialogTitle from '@mui/joy/DialogTitle';
import WarningRoundedIcon from '@mui/icons-material/WarningRounded';
import Divider from '@mui/joy/Divider';
import DialogContent from '@mui/joy/DialogContent';
import DialogActions from '@mui/joy/DialogActions';
import ModalDialog from '@mui/joy/ModalDialog';
import Modal from '@mui/joy/Modal';
import {useState} from 'react';

interface Props {
  project: Project;
  onClose: (deleted: boolean) => void;
}

export function DeleteProjectDialog({project, onClose}: Props) {
  const [loading, setLoading] = useState(false);

  const {showErrorSnackbar} = useSnackbar();

  return (
    <Modal
      open={true}
      onClose={onClose}
    >
      <ModalDialog
        variant='outlined'
        role='alertdialog'
      >
        <DialogTitle>
          <WarningRoundedIcon />
          {useMyProjectsString().confirm_delete}
        </DialogTitle>
        <Divider />
        <DialogContent>{useMyProjectsString().delete_subheading}</DialogContent>
        <DialogActions>
          <Button
            disabled={loading}
            variant='solid'
            color='danger'
            onClick={async () => {
              setLoading(true);

              await project
                .delete()
                .then(() => {
                  onClose(true);
                })
                .catch((e) => {
                  logger.error(e);
                  showErrorSnackbar(e, 'Error deleting project.');
                });

              setLoading(false);
            }}
          >
            {loading ? <LoadingIndicator size='sm' /> : useMyProjectsString().delete}
          </Button>
          <Button
            variant='plain'
            color='neutral'
            onClick={() => onClose(false)}
          >
            {useMyProjectsString().cancel}
          </Button>
        </DialogActions>
      </ModalDialog>
    </Modal>
  );
}
