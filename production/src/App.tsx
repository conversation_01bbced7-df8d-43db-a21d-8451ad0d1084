import './App.css';
import {usingEmulators} from '@creator-campus/common';
import {AuthProvider, SnackbarProvider, TextProvider, UserProvider} from '@creator-campus/common-components';
import {ThemeProvider} from './providers/ThemeProvider.tsx';
import MaintenanceModeWrapper from './providers/MaintenanceModeWrapper.tsx';
import {EmulatorStatusWrapper} from './providers/EmulatorStatusWrapper.tsx';
import {router} from './routes.tsx';
import {RouterProvider} from 'react-router-dom';
import {loadStripe} from '@stripe/stripe-js';
import {ErrorBoundary} from '@sentry/react';
import {ErrorPage} from './pages/ErrorPage.tsx';

export const stripePromise = loadStripe(usingEmulators ? String(process.env.REACT_APP_STRIPE_PK_TEST) : String(process.env.REACT_APP_STRIPE_PK_LIVE));

export default function App() {
  return (
    <ErrorBoundary fallback={<ErrorPage />}>
      <EmulatorStatusWrapper>
        <AuthProvider>
          <MaintenanceModeWrapper>
            <SnackbarProvider>
              <UserProvider>
                <ThemeProvider>
                  <TextProvider>
                    <RouterProvider router={router} />
                  </TextProvider>
                </ThemeProvider>
              </UserProvider>
            </SnackbarProvider>
          </MaintenanceModeWrapper>
        </AuthProvider>
      </EmulatorStatusWrapper>
    </ErrorBoundary>
  );
}
