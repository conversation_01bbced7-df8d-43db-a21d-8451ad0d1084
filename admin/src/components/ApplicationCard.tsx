import Card from '@mui/joy/Card';
import Typography from '@mui/joy/Typography';
import {Stack} from '@mui/joy';
import {User} from '@creator-campus/common';

interface Props {
  user: User;
  showModal: (user: User) => void;
}

export default function ApplicationCard({user, showModal}: Props) {
  return (
    <Card
      onClick={() => showModal(user)}
      sx={{
        cursor: 'pointer',
        transition: 'background-color 0.2s',
        '&:hover': {
          backgroundColor: 'rgba(0, 0, 0, 0.05)',
        },
      }}
    >
      <Stack sx={{mx: 1}}>
        <Typography level='title-lg'>{user.name}</Typography>
        <Typography level='body-sm'>{user.email}</Typography>
        <Typography level='body-sm'>{user.universityId}</Typography>
      </Stack>
    </Card>
  );
}
