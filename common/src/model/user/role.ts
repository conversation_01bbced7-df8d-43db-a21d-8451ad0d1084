export type RoleId = 'student' | 'staff' | 'graduate' | 'mentor';

export class Role {
  static readonly STUDENT = new Role('student', 'Student', 'Students');
  static readonly STAFF = new Role('staff', 'Staff', 'Staff');
  static readonly GRADUATE = new Role('graduate', 'Graduate', 'Graduates');
  static readonly MENTOR = new Role('mentor', 'Mentor', 'Mentors');

  private constructor(
    readonly id: RoleId,
    readonly label: string,
    readonly labelPlural: string,
  ) {}

  static fromId(id: string | null) {
    if (id === 'External') {
      return Role.STAFF;
    }

    return this.values().find((c) => c.id === id) || null;
  }

  static values(): Role[] {
    return Object.values(Role);
  }
}
