const newUser = {
  email: `${new Date().getTime()}@creators.ac.uk`,
  password: 'password',
};

const existingUser = {
  email: '<EMAIL>',
  password: 'password',
};

describe('test sign-up', () => {
  beforeEach(() => {
    cy.visit('http://localhost:5173/signup');
  });

  function expectLocationPath(path: string) {
    cy.location().should((location) => {
      expect(location.pathname).to.eq(path);
    });
  }

  it('signs up', () => {
    // Text inputs
    cy.dataCy('email-input').should('be.visible').type(newUser.email);
    cy.dataCy('password-input').type(newUser.password);
    cy.dataCy('confirm-password-input').type(newUser.password);

    // Checkbox
    cy.dataCy('terms-and-conditions-checkbox').click();

    // Sign up button
    cy.dataCy('submit-button').should('be.enabled').click();

    expectLocationPath('/onboarding');

    // Log out
    cy.dataCy('sign-out-button').should('be.visible').click();
    expectLocationPath('/login');
  });

  it('does not allow sign-up with duplicate emails', () => {
    // Text inputs
    cy.dataCy('email-input').should('be.visible').type(existingUser.email);
    cy.dataCy('password-input').type(existingUser.password);
    cy.dataCy('confirm-password-input').type(existingUser.password);

    // Checkbox
    cy.dataCy('terms-and-conditions-checkbox').click();

    // Sign up button
    cy.dataCy('submit-button').should('be.enabled').click();
    cy.contains('Somebody has already signed up using this email.');
    expectLocationPath('/signup/students');
  });
});
