# “Technical” and “Business” Co-Founders

<p style='text-align: justify;'>Whilst it’s a little restrictive, for simplicity’s sake founders are generally categorised into either “technical” or “business” founders. Either you’re a coder, scientist, or engineer with the technical know-how to build a product without much outside assistance. Or, your area of expertise lies somewhere else like sales, product management, or industry experience and you’re put into the business bucket.</p>

<p style='text-align: justify;'>I started off my entrepreneurial journey as a business founder, with an idea for a mobile app. Given I had precisely zero understanding of how to build a mobile app, I found myself in a bit of a conundrum. It’s a situation I know many others find themselves in so I thought I would share this short story (if you have the good fortune of being technical, feel free to skip this section).</p>

---

<p style='text-align: justify;'>Recognise that as a business founder, it will usually be your job to persuade a good technical co-founder to join your team, and not the other way around. Good coders are in high demand and often have the luxury of choosing what projects they want to work on. That’s why you’ll need to prove your ability to execute upon your idea and avoid the stereotype of “naive business school student looking to bring on a computer science student to do all the hard work for them”. If the CTO’s job is to write code, your job is to do everything else.</p>

<p style='text-align: justify;'>In my own story, I unfortunately didn’t have a great technical co-founder within my network to bring on as CTO. So, after struggling for months to find the right co-founder by attending networking events, posting on social media groups, and pestering anyone I could, I decided I would learn to code and start building it myself. I came to appreciate the value in learning to code, and accepted it would be a skillset that would benefit me more broadly in life. It was scary and intimidating at the time, but looking back it was one of the best decisions I ever made for a few reasons:</p>

- <p style='text-align: justify;'>It allowed me to start making early progress on my mobile app. This determination also signalled to potential co-founders my commitment to making my idea a reality, and therefore enabled me to seek higher quality co-founders.</p>
- <p style='text-align: justify;'>Empowered me to engage in technical conversations with potential technical co-founders, and the software developers we recruited further down the line. I recognised my lack of understanding in technical conversations would always be a weakness and leave me vulnerable to exploitation by anyone who was technically fluent. I also felt the leader of a tech company probably ought to have some level of familiarity with code.</p>
- <p style='text-align: justify;'>Coding can be great fun.</p>

<p style='text-align: justify;'>That doesn’t mean I became an expert software developer by any stretch, but I was good enough to contribute lines of code. It also didn’t mean that I was responsible for both the commercial and technical side of the business - I still eventually found a CTO who was much better at coding than me to build the majority of the app.</p>

<p style='text-align: justify;'>I don’t want to give the impression that everyone who wants to start a business must learn to code, but I do think it’s worth considering. Here’s a good quote from Sam Altman:</p>

<span style="color: green;">*If you're not willing to do this, you should remember that there are far greater challenges coming in the course of a startup than learning how to code. You should also remember that you can probably learn to code in less time than it will take to find the right cofounder.*</span>

<span style="color: green;">**- Sam Altman**</span>