import Box from '@mui/joy/Box';
import {Edit} from '@mui/icons-material';
import {ChromePicker} from 'react-color';
import {useEffect, useRef, useState} from 'react';

interface Props {
  color: string;
  onChange: (color: string) => void;
}

export function ColorPickerBox({color, onChange}: Props) {
  const boxSize = 32;
  const colorPickerOffset = 12;

  const [colorPickerOpen, setColorPickerOpen] = useState<boolean>(false);

  const colorPickerRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (colorPickerRef.current && !colorPickerRef.current.contains(event.target as Node)) {
        setColorPickerOpen(false);
      }
    }

    if (colorPickerOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [colorPickerOpen]);

  return (
    <Box
      ref={colorPickerRef}
      position={'relative'}
    >
      <Box
        width={boxSize}
        height={boxSize}
        onClick={() => setColorPickerOpen(true)}
        sx={{
          backgroundColor: color,
          borderRadius: 'md',
          cursor: 'pointer',
          overflow: 'hidden',
          '&:hover .overlay': {
            opacity: 1,
          },
        }}
      >
        {/* Hover overlay with icon */}
        <Box
          className='overlay'
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            bgcolor: 'rgba(0, 0, 0, 0.2)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            opacity: 0,
            transition: 'opacity 0.3s ease',
            pointerEvents: 'none',
            borderRadius: 'inherit',
          }}
        >
          <Edit sx={{color: 'background.body', fontSize: 16}} />
        </Box>
      </Box>

      {/* Color picker shown on click */}
      {colorPickerOpen && (
        <Box
          sx={{
            position: 'absolute',
            zIndex: 1000,
            top: 0,
            left: boxSize + colorPickerOffset,
          }}
        >
          <ChromePicker
            color={color}
            onChange={(color) => {
              onChange(color.hex);
            }}
          />
        </Box>
      )}
    </Box>
  );
}
