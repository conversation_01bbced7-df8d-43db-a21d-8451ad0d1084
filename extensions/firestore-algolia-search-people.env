ALGOLIA_API_KEY=projects/${param:PROJECT_NUMBER}/secrets/firestore-algolia-search-people-ALGOLIA_API_KEY/versions/latest
ALGOLIA_APP_ID=YU8GOIF2ZY
COLLECTION_PATH=people
DATABASE_ID=(default)
DO_FULL_INDEXING=true
FIELDS=name,bio,fake,universityId,persona,profileCompleted,website,linkedinUsername,githubUsername,founder,openToWork,hideProfile,role,staffRole,karma,applicationStatus,featuredUntil,score,services
FORCE_DATA_SYNC=no
LOCATION=europe-west2
firebaseextensions.v1beta.function/location=europe-west2
firebaseextensions.v1beta.v2function/location=europe-west2