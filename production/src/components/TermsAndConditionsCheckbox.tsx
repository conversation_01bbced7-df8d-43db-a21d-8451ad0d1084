import Typography from '@mui/joy/Typography';
import Link from '@mui/joy/Link';
import {Checkbox} from '@mui/joy';
import FormControl from '@mui/joy/FormControl';

interface Props {
  sx?: object;
}

export function TermsAndConditionsCheckbox({sx}: Props) {
  return (
    <FormControl
      required
      sx={{...sx}}
    >
      <Checkbox
        data-cy={'terms-and-conditions-checkbox'}
        size='sm'
        name='checkbox'
        label={
          <Typography level='body-sm'>
            I have read and agree to the{' '}
            <Link
              href='https://app.termly.io/policy-viewer/policy.html?policyUUID=f9dc8f36-24ac-4e70-a62a-eb1f1f0c640a'
              target='_blank'
              sx={{zIndex: 1000}}
            >
              Terms of Service
            </Link>{' '}
            and{' '}
            <Link
              href='https://app.termly.io/policy-viewer/policy.html?policyUUID=a070d366-16e2-461f-8e88-c47ad9309334'
              target='_blank'
              sx={{zIndex: 1000}}
            >
              Privacy Policy
            </Link>
            .
          </Typography>
        }
      />
    </FormControl>
  );
}
