import {onDocumentCreated, onDocumentDeleted, onDocumentUpdated} from 'firebase-functions/v2/firestore';
import {DocumentReference, FieldValue} from 'firebase-admin/firestore';
import * as admin from 'firebase-admin';
import {DiscussionPost, DiscussionPostBase, DiscussionPostDbModelSafe, University, User} from '@creator-campus/common';
import {deletePostAttachments, movePendingAttachments, initSentryForFunction} from './utils';
import * as Sentry from '@sentry/node';

async function processCreatedPost(post: DiscussionPostDbModelSafe, postId: string, universityId?: string) {
  Sentry.addBreadcrumb({
    message: `Processing created post: ${postId}`,
    category: 'discussion',
    level: 'info',
    data: {
      postId,
      universityId: universityId || 'global',
      authorId: post.authorId,
      hasAttachments: post.attachments.length > 0,
      textLength: post.text.length
    }
  });

  const discussionDoc = universityId ? admin.firestore().collection(University.collectionName).doc(universityId) : admin.firestore().collection('global').doc('discussion');
  await discussionDoc.update({
    lastDiscussionPost: new Date(),
  });

  Sentry.addBreadcrumb({
    message: 'Updated last discussion post timestamp',
    category: 'firestore',
    level: 'info',
    data: { postId, universityId: universityId || 'global' }
  });

  const originFolder = DiscussionPostBase.getPendingUploadsFolder(universityId);
  const destinationFolder = DiscussionPost.getUploadsFolder(postId, universityId);

  Sentry.addBreadcrumb({
    message: 'Moving pending attachments for post',
    category: 'storage',
    level: 'info',
    data: {
      postId,
      attachmentCount: post.attachments.length,
      originFolder,
      destinationFolder
    }
  });

  await movePendingAttachments(post, originFolder, destinationFolder);
}

export const onUniDiscussionPostCreated = onDocumentCreated('universities/{universityId}/discussion/{postId}', async (event) => {
  initSentryForFunction('onUniDiscussionPostCreated');

  if (!event.data) {
    console.error('Event data was undefined!');
    Sentry.addBreadcrumb({
      message: 'Event data is undefined in onUniDiscussionPostCreated',
      category: 'function',
      level: 'error'
    });
    return;
  }

  const post = event.data.data() as DiscussionPostDbModelSafe;
  const postId = event.params.postId;
  const universityId = event.params.universityId;

  Sentry.addBreadcrumb({
    message: `University discussion post created: ${postId}`,
    category: 'discussion',
    level: 'info',
    data: { postId, universityId, authorId: post.authorId }
  });

  await processCreatedPost(post, postId, universityId);
});

export const onGlobalDiscussionPostCreated = onDocumentCreated('global/discussion/discussion/{postId}', async (event) => {
  initSentryForFunction('onGlobalDiscussionPostCreated');

  if (!event.data) {
    console.error('Event data was undefined!');
    Sentry.addBreadcrumb({
      message: 'Event data is undefined in onGlobalDiscussionPostCreated',
      category: 'function',
      level: 'error'
    });
    return;
  }

  const post = event.data.data() as DiscussionPostDbModelSafe;
  const postId = event.params.postId;

  Sentry.addBreadcrumb({
    message: `Global discussion post created: ${postId}`,
    category: 'discussion',
    level: 'info',
    data: { postId, universityId: 'global', authorId: post.authorId }
  });

  await processCreatedPost(post, postId);
});

async function processUpdatedPost(beforeData: DiscussionPostDbModelSafe, afterData: DiscussionPostDbModelSafe, docRef: DocumentReference) {
  const upVotesBefore = beforeData.upVotes.length;
  const upVotesAfter = afterData.upVotes.length;
  const upVotesChanged = upVotesBefore !== upVotesAfter;

  Sentry.addBreadcrumb({
    message: 'Processing updated post',
    category: 'discussion',
    level: 'info',
    data: {
      postId: docRef.id,
      authorId: afterData.authorId,
      upVotesBefore,
      upVotesAfter,
      upVotesChanged
    }
  });

  if (upVotesChanged) {
    const karmaChange = upVotesAfter - upVotesBefore;

    Sentry.addBreadcrumb({
      message: 'Post upvotes changed, updating author karma',
      category: 'discussion',
      level: 'info',
      data: {
        postId: docRef.id,
        authorId: afterData.authorId,
        karmaChange,
        newUpVoteCount: upVotesAfter
      }
    });

    // Update author's karma
    await admin
      .firestore()
      .collection(User.collectionName)
      .doc(afterData.authorId)
      .update({
        karma: FieldValue.increment(karmaChange),
      });

    return docRef.update({
      numUpVotes: upVotesAfter,
    });
  }

  return null;
}

export const onUniDiscussionPostUpdated = onDocumentUpdated('universities/{universityId}/discussion/{postId}', async (event) => {
  initSentryForFunction('onUniDiscussionPostUpdated');

  if (!event.data) {
    console.error('Event data was undefined!');
    Sentry.addBreadcrumb({
      message: 'Event data is undefined in onUniDiscussionPostUpdated',
      category: 'function',
      level: 'error'
    });
    return;
  }

  const beforeData = event.data.before.data() as DiscussionPostDbModelSafe;
  const afterData = event.data.after.data() as DiscussionPostDbModelSafe;
  const postId = event.params.postId;
  const universityId = event.params.universityId;

  Sentry.addBreadcrumb({
    message: `University discussion post updated: ${postId}`,
    category: 'discussion',
    level: 'info',
    data: { postId, universityId, authorId: afterData.authorId }
  });

  return await processUpdatedPost(beforeData, afterData, event.data.after.ref);
});

export const onGlobalDiscussionPostUpdated = onDocumentUpdated('global/discussion/discussion/{postId}', async (event) => {
  initSentryForFunction('onGlobalDiscussionPostUpdated');

  if (!event.data) {
    console.error('Event data was undefined!');
    Sentry.addBreadcrumb({
      message: 'Event data is undefined in onGlobalDiscussionPostUpdated',
      category: 'function',
      level: 'error'
    });
    return;
  }

  const beforeData = event.data.before.data() as DiscussionPostDbModelSafe;
  const afterData = event.data.after.data() as DiscussionPostDbModelSafe;
  const postId = event.params.postId;

  Sentry.addBreadcrumb({
    message: `Global discussion post updated: ${postId}`,
    category: 'discussion',
    level: 'info',
    data: { postId, universityId: 'global', authorId: afterData.authorId }
  });

  return await processUpdatedPost(beforeData, afterData, event.data.after.ref);
});

async function processDeletedPost(post: DiscussionPostDbModelSafe, postId: string, universityId?: string) {
  Sentry.addBreadcrumb({
    message: `Processing deleted post: ${postId}`,
    category: 'discussion',
    level: 'info',
    data: {
      postId,
      universityId: universityId || 'global',
      authorId: post.authorId,
      hasAttachments: post.attachments.length > 0
    }
  });

  const storageFolder = DiscussionPost.getUploadsFolder(postId, universityId);

  Sentry.addBreadcrumb({
    message: 'Deleting post attachments',
    category: 'storage',
    level: 'info',
    data: {
      postId,
      attachmentCount: post.attachments.length,
      storageFolder
    }
  });

  await deletePostAttachments(post, postId, storageFolder);
}

export const onUniDiscussionPostDeleted = onDocumentDeleted('universities/{universityId}/discussion/{postId}', async (event) => {
  initSentryForFunction('onUniDiscussionPostDeleted');

  if (!event.data) {
    console.error('Event data was undefined!');
    Sentry.addBreadcrumb({
      message: 'Event data is undefined in onUniDiscussionPostDeleted',
      category: 'function',
      level: 'error'
    });
    return;
  }

  const post = event.data.data() as DiscussionPostDbModelSafe;
  const postId = event.params.postId;
  const universityId = event.params.universityId;

  Sentry.addBreadcrumb({
    message: `University discussion post deleted: ${postId}`,
    category: 'discussion',
    level: 'info',
    data: { postId, universityId, authorId: post.authorId }
  });

  return await processDeletedPost(post, postId, universityId);
});

export const onGlobalDiscussionPostDeleted = onDocumentDeleted('global/discussion/discussion/{postId}', async (event) => {
  initSentryForFunction('onGlobalDiscussionPostDeleted');

  if (!event.data) {
    console.error('Event data was undefined!');
    Sentry.addBreadcrumb({
      message: 'Event data is undefined in onGlobalDiscussionPostDeleted',
      category: 'function',
      level: 'error'
    });
    return;
  }

  const post = event.data.data() as DiscussionPostDbModelSafe;
  const postId = event.params.postId;

  Sentry.addBreadcrumb({
    message: `Global discussion post deleted: ${postId}`,
    category: 'discussion',
    level: 'info',
    data: { postId, universityId: 'global', authorId: post.authorId }
  });

  return await processDeletedPost(post, postId);
});
