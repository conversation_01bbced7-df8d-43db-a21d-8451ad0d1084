import {type EmailTemplate} from './emailTemplate.js';

interface Data {
  // Recipient name
  name: string;
}

export function getMembershipApprovedTemplate(universityId: string) {
  return `membership_approved_${universityId.toLowerCase().replace(/\s/g, '_')}`;
}

export class MembershipApprovedCustomEmailTemplate implements EmailTemplate {
  readonly template: string;

  constructor(
    universityId: string,
    readonly data: Data,
  ) {
    this.template = getMembershipApprovedTemplate(universityId);
  }
}
