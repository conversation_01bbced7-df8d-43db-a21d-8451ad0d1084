import {Persona} from './persona';
import {Role} from './role';
import {getDownloadURL, ref, type StorageReference} from 'firebase/storage';
import {firestore, storage} from '../../firebase';
import {StaffRole} from '../university/staffRole';
import {DocumentReference} from 'firebase/firestore';
import {User, UserConverter, UserDbModel} from './user';
import {doc} from 'firebase/firestore';
import {Service} from './service';

/**
 * Represents an Algolia record for a user.
 */
export class UserHit {
  readonly _avatarStorage: StorageReference;
  readonly _avatarUploadStorage: StorageReference;
  readonly _doc: DocumentReference<User, UserDbModel>;

  constructor(
    readonly id: string,
    readonly bio: string,
    readonly featuredUntil: Date | null,
    readonly founder: boolean,
    readonly githubUsername: string | null,
    readonly karma: number,
    readonly linkedinUsername: string | null,
    readonly name: string,
    readonly openToWork: boolean,
    readonly persona: Persona | null,
    readonly profileCompleted: boolean,
    readonly role: Role | null,
    readonly services: Service[],
    readonly staffRole: StaffRole | null,
    readonly universityId: string,
    readonly website: string | null,
  ) {
    this._avatarStorage = ref(storage(), `/people/${id}/avatar_200x200`);
    this._avatarUploadStorage = ref(storage(), `/people/${id}/avatar`);
    this._doc = UserHit.doc(id);
  }

  public static doc(id: string) {
    return doc(firestore(), User.collectionName, id).withConverter<User, UserDbModel>(new UserConverter());
  }

  public static fromAlgoliaHit(hit: any) {
    return new UserHit(
      hit.objectID,
      hit.bio,
      hit.featuredUntil,
      hit.founder,
      hit.githubUsername,
      hit.karma,
      hit.linkedinUsername,
      hit.name,
      hit.openToWork,
      Persona.fromId(hit.persona),
      hit.profileCompleted,
      Role.fromId(hit.role),
      hit.services.map((s: string) => Service.fromId(s)!),
      StaffRole.fromId(hit.staffRole),
      hit.universityId,
      hit.website,
    );
  }

  /**
   * Returns a url link to this profile.
   * @param absolute Whether the url should be absolute (i.e. include creatorcampus.io). Usually a link copied to the clipboard should be absolute. If using with the `navigate` function, use a relative url.
   */
  public getLink(absolute: boolean = true) {
    return UserHit.getLink(this.id, absolute);
  }

  public static getLink(userId: string, absolute: boolean = true) {
    const url = `share/profile/${userId}`;
    return absolute ? `${location.origin}/${url}` : url;
  }

  public async getAvatarUrl() {
    // todo: include avatarUpdatedAt and dateJoined in UserHit, not just User
    // if (this.avatarUpdatedAt.getTime() === this.dateJoined.getTime()) {
    //   // Avatar has not been uploaded yet
    //   return null;
    // }

    return await getDownloadURL(this._avatarStorage);
  }

  public getGitHubUrl() {
    return this.githubUsername ? `https://github.com/${this.githubUsername}` : null;
  }

  public getLinkedInUrl() {
    return this.linkedinUsername ? `https://linkedin.com/in/${this.linkedinUsername}` : null;
  }

  public hasAnySocials() {
    return this.githubUsername !== null || this.linkedinUsername !== null || this.website !== null;
  }

  public getFirstName() {
    const nameParts = this.name.split(' ');
    if (nameParts.length === 0) {
      return this.name;
    }

    return nameParts[0];
  }

  public isStaff() {
    return !!this.staffRole;
  }
}
