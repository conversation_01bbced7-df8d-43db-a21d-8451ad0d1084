rules_version = '2';

service firebase.storage {
  match /b/{bucket}/o {
    function loggedIn() {
      return request.auth != null;
    }

    // ------------ //
    // ------------ //
    // ------------ //

    match /site-images/{imageId} {
      allow read: if true;
    }

    // ------------ //

    match /people/{userId}/{fileId} {
      allow read: if true;
      allow write: if loggedIn() && userId == request.auth.uid;
    }

    // ------------ //

    match /projects/{projectId}/{fileId} {
      allow read: if true;
      allow write: if loggedIn();
    }

    // ------------ //

    match /global/discussion/pending-uploads/{fileId} {
      allow create: if loggedIn();
    }

    match /global/discussion/posts/{postId}/{attachmentId} {
      allow read: if loggedIn();
      allow update, delete: if loggedIn();
    }

    match /global/discussion/posts/{postId}/comments/{commentId}/{attachmentId} {
      allow read: if loggedIn();
      allow update, delete: if loggedIn();
    }

    // ------------ //

    match /universities/{universityId}/{fileId} {
      allow read: if true;
      allow write: if loggedIn();
    }

    match /universities/{universityId}/discussion/pending-uploads/{fileId} {
      allow create: if loggedIn();
    }

    match /universities/{universityId}/discussion/posts/{postId}/{attachmentId} {
      allow read: if loggedIn();
      allow update, delete: if loggedIn();
    }

    match /universities/{universityId}/discussion/posts/{postId}/comments/{commentId}/{attachmentId} {
      allow read: if loggedIn();
      allow update, delete: if loggedIn();
    }
  }
}