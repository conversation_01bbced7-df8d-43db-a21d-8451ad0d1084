import {<PERSON><PERSON>, <PERSON><PERSON>} from '@mui/joy';
import {Email, MarkEmailRead} from '@mui/icons-material';
import Stack from '@mui/joy/Stack';
import Typography from '@mui/joy/Typography';
import {sendEmailVerification} from 'firebase/auth';
import {logger, User} from '@creator-campus/common';
import {LoadingIndicator, useAuth, useSnackbar} from '@creator-campus/common-components';
import {useEffect, useState} from 'react';
import {updateDoc} from 'firebase/firestore';

interface Props {
  buttonText: string;
  onNext?: () => void;
}

export function VerifyEmailForm({buttonText, onNext}: Props) {
  const [resendEmailLoading, setResendEmailLoading] = useState(false);
  const [nextEnabled, setNextEnabled] = useState(false);

  const {currentUser} = useAuth();
  const {showErrorSnackbar, showSnackbar} = useSnackbar();

  async function resendVerificationEmail() {
    setResendEmailLoading(true);

    await sendEmailVerification(currentUser!)
      .then(() => {
        showSnackbar('Check your email!', 'success');
      })
      .catch((e) => {
        if (e.code === 'auth/too-many-requests') {
          showSnackbar("You've reached the limit for the number of verification emails we can send you right now.", 'warning');
        } else {
          logger.error(e);
          showErrorSnackbar(e, 'Error sending verification email.');
        }
      });

    setResendEmailLoading(false);
  }

  useEffect(() => {
    // Every 3 seconds, check if the user has verified their email.
    // Polling is currently the only way to check if the user has verified their email.
    const interval = setInterval(async () => {
      await currentUser!.reload();
      setNextEnabled(currentUser!.emailVerified);
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  return (
    <Stack
      spacing={1}
      sx={{justifyContent: 'center', alignItems: 'end'}}
    >
      <Alert color='neutral'>
        <Stack
          gap={1.5}
          sx={{alignItems: 'center', p: 1}}
        >
          <Stack
            direction='row'
            alignItems='center'
            justifyContent='center'
            spacing={1.5}
          >
            <MarkEmailRead />
            <Typography level='title-sm'>{nextEnabled ? "Thanks for verifying your email! Head into the platform when you're ready." : `Please click on the verification link we sent to ${currentUser!.email}. Then check back here! Be sure to check your spam folder.`}</Typography>
          </Stack>
          <Button
            startDecorator={<Email />}
            disabled={nextEnabled || resendEmailLoading}
            variant='plain'
            color='neutral'
            onClick={resendVerificationEmail}
            sx={{width: 150}}
          >
            {resendEmailLoading ? <LoadingIndicator size='sm' /> : 'Resend email'}
          </Button>
        </Stack>
      </Alert>
      <Button
        variant='solid'
        disabled={!nextEnabled}
        onClick={async () => {
          await updateDoc(User.doc(currentUser!.uid), {emailVerified: true});

          if (onNext) {
            onNext();
          }
        }}
      >
        {buttonText}
      </Button>
    </Stack>
  );
}
