import {UserHit} from '@creator-campus/common';
import {LoadingIndicator, PageHeader, PageShell, useUser, AlgoliaIndexExplorer} from '@creator-campus/common-components';
import PeopleFilterOptions from '../components/PeopleFilterOptions.tsx';
import {Grid} from '@mui/joy';
import ProfileCard from '../components/ProfileCard.tsx';
import {Navigate} from 'react-router-dom';

export default function MentorsPage() {
  const {user, university} = useUser();

  if (!university?.partner) {
    return <Navigate to='/startups/explore' />;
  }

  return (
    <PageShell banners={{profileIncomplete: {hideChildrenIfShown: false}, activateCommunity: {hideChildrenIfShown: false}}}>
      <PageHeader
        title={'Mentors'}
        subtitle={'Get expert advice to help grow your startup.'}
      />
      {!user || !university ? (
        <LoadingIndicator />
      ) : (
        <AlgoliaIndexExplorer
          index={'people'}
          initialFilters={{
            profileCompleted: ':true',
            role: ':Mentor',
            hideProfile: ':false',
            universityId: `:"${university.id}"`,
          }}
          filterPanel={(filters, onFilterChange) => (
            <PeopleFilterOptions
              user={user}
              university={university}
              filters={filters}
              onFilterChange={onFilterChange}
              withUniversityFilter={false}
            />
          )}
          placeholderPrefix={"I'm looking for"}
          searchPlaceholders={['a business advisor', 'a robotics consultant', 'a creative advisor', 'an experienced engineer', 'a freelance advisor', 'a social entrepreneur', 'a sustainability expert']}
          placeholderOffset={152}
          itemBuilder={(hit) => {
            const userHit = UserHit.fromAlgoliaHit(hit);
            return (
              <Grid
                xs={12}
                sm={6}
                md={6}
                lg={6}
                xl={4}
                key={userHit.id}
                sx={{mb: 2}}
              >
                <ProfileCard
                  key={userHit.id}
                  me={user}
                  user={userHit}
                />
              </Grid>
            );
          }}
        />
      )}
    </PageShell>
  );
}
