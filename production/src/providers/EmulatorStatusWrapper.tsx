import Typography from '@mui/joy/Typography';
import {EmulatorsStatus, usingEmulators} from '@creator-campus/common';
import {LoadingIndicator} from '@creator-campus/common-components';
import Stack from '@mui/joy/Stack';
import {ReactNode, useEffect, useState} from 'react';
import {onSnapshot} from 'firebase/firestore';
import Card from '@mui/joy/Card';

interface Props {
  children: ReactNode;
}

export function EmulatorStatusWrapper({children}: Props) {
  const [emulatorsReady, setEmulatorsReady] = useState<boolean>(!usingEmulators);

  useEffect(() => {
    if (!usingEmulators) {
      return;
    }

    const unsub = onSnapshot(EmulatorsStatus.doc(), (docSnap) => {
      console.log('Emulators ready: ', docSnap.data()?.ready);
      setEmulatorsReady(docSnap.data()?.ready || false);
    });

    return () => unsub();
  }, []);

  if (usingEmulators && !emulatorsReady) {
    return (
      <Stack
        height={'90vh'}
        justifyContent={'center'}
        alignItems={'center'}
      >
        <Card
          sx={{
            height: 50,
            width: 300,
            justifyContent: 'center',
            alignItems: 'center',
            textAlign: 'center',
          }}
        >
          <Stack
            direction={'row'}
            spacing={2}
          >
            <LoadingIndicator size='sm' />
            <Typography level='title-md'>Waiting for emulators to start...</Typography>
          </Stack>
        </Card>
      </Stack>
    );
  }

  return <>{children}</>;
}
