import {createContext, ReactNode, useContext} from 'react';
import baseLangStrings from '../contexts/text/base-text-state.json';

const TextContext = createContext<typeof baseLangStrings>(baseLangStrings);

export function TextProvider({children}: {children: ReactNode}) {
  return <TextContext.Provider value={baseLangStrings}>{children}</TextContext.Provider>;
}

export function useCompanyNameString() {
  return useContext(TextContext).public.pages.company_name;
}

export function useExploreString() {
  return useContext(TextContext).public.pages.explore;
}

export function useMyProjectsString() {
  return useContext(TextContext).public.pages.my_projects;
}

export function useSidebarString() {
  return useContext(TextContext).public.pages.sidebar;
}

export function usePeopleString() {
  return useContext(TextContext).public.pages.people;
}

export function useConnectModalString() {
  return useContext(TextContext).public.pages.connect_modal;
}

export function useNewProjectModalString() {
  return useContext(TextContext).public.pages.new_project_modal;
}

export function useLoginString() {
  return useContext(TextContext).public.pages.login;
}

export function useSignupString() {
  return useContext(TextContext).public.pages.signup;
}

export function useProfileString() {
  return useContext(TextContext).public.pages.my_profile;
}

export function useResetEmailPasswordString() {
  return useContext(TextContext).public.pages.reset_email_password;
}

export function useUploadFileString() {
  return useContext(TextContext).public.pages.upload_file;
}

export function useCreateOpportunityString() {
  return useContext(TextContext).public.pages.create_opportunity;
}

export function useProjectCardString() {
  return useContext(TextContext).public.pages.project_card;
}

export function useProfileOnboardingString() {
  return useContext(TextContext).public.pages.profile_onboarding;
}

export function useActivateCommunityString() {
  return useContext(TextContext).public.pages.activate_community;
}
