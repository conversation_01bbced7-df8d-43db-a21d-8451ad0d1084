import * as admin from 'firebase-admin';
import {AdminEmailTemplate, ApprovedAlumnus, CREATOR_CAMPUS_ADMINS, DiscussionPostBaseDbModel, EmailTemplate, getEmailContent, Opportunity, OpportunityDbModelSafe, Project, ProjectDbModelAlgolia, StaffRole, University, User, UserDbModelSafe} from '@creator-campus/common';
import {Timestamp} from 'firebase-admin/firestore';
import * as Sentry from '@sentry/node';

export const getMetricsCollection = (universityId: string) => {
  return admin.firestore().collection(University.collectionName).doc(universityId).collection('metrics').doc('metrics');
};

export async function movePendingAttachments(post: DiscussionPostBaseDbModel, originFolder: string, destinationFolder: string) {
  console.log(`Moving pending images and attachments from ${originFolder} to ${destinationFolder}...`);

  const promises = [];
  for (const attachment of post.attachments) {
    const pendingFile = admin.app().storage().bucket().file(`${originFolder}/${attachment.id}`);
    promises.push(pendingFile.move(`${destinationFolder}/${attachment.id}`));
  }

  await Promise.all(promises);
}

export async function deletePostAttachments(post: DiscussionPostBaseDbModel, postId: string, storageFolder: string) {
  console.log(`Deleting images and attachments for post/comment ${postId}...`);

  const promises = [];
  for (const attachment of post.attachments) {
    const file = admin.app().storage().bucket().file(`${storageFolder}/${attachment.id}`);
    promises.push(file.delete());
  }

  await Promise.all(promises);
}

export async function sendEmail(recipient: string, template: EmailTemplate) {
  await admin.firestore().collection('mail').add(getEmailContent(recipient, template));
}

export async function emailCreatorCampusAdmins(subject: string, body: string) {
  const promises: Promise<any>[] = [];
  for (const email of CREATOR_CAMPUS_ADMINS) {
    promises.push(sendEmail(email, new AdminEmailTemplate({subject, body})));
  }
  await Promise.all(promises);
}

/**
 * Docs in the `col` must have an `expiresAt` field and a `universityId` field.
 */
export async function deleteExpiredDocs(col: string) {
  console.log(`Fetching expired docs in collection: ${col}...`);
  const expiredApprovals = await admin.firestore().collection(col).where('expiresAt', '<', Timestamp.now()).get();
  console.log(`Found ${expiredApprovals.size} expired docs to delete.`);

  const deletePromises: Promise<any>[] = [];
  const numExpiredByUniversity: Record<string, number> = {};

  for (const doc of expiredApprovals.docs) {
    const approval = doc.data() as ApprovedAlumnus;

    console.log(`Deleting ${doc.id}...`);
    deletePromises.push(doc.ref.delete().catch((e) => console.error('Failed to delete doc:', e)));

    numExpiredByUniversity[approval.universityId] = (numExpiredByUniversity[approval.universityId] ?? 0) + 1;
  }

  await Promise.all(deletePromises);
  return numExpiredByUniversity;
}

export async function fetchUniversityOwners(universityId: string) {
  const ownersSnapshot = await admin.firestore().collection(User.collectionName).where('universityId', '==', universityId).where('staffRole', '==', StaffRole.OWNER.id).get();
  return ownersSnapshot.docs.map((d) => d.data() as UserDbModelSafe);
}

/**
 * Depends on:
 * - Project updated (numOpenOpportunities, description, summary, website)
 * - Owner updated (karma, last online)
 * @param projectId
 * @param project
 */
export async function calculateProjectScore(projectId: string, project?: ProjectDbModelAlgolia) {
  if (!project) {
    const projectSnapshot = await admin.firestore().collection(Project.collectionName).doc(projectId).get();
    project = projectSnapshot.data() as ProjectDbModelAlgolia;
  }

  const founderSnapshot = await admin.firestore().collection(User.collectionName).doc(project.ownerId).get();
  const founder = founderSnapshot.data() as UserDbModelSafe;

  const latestOppSnapshot = await admin.firestore().collection(Opportunity.collectionName).where('projectId', '==', projectId).orderBy('datePosted', 'desc').limit(1).get();
  const latestOpp = latestOppSnapshot.docs[0]?.data() as OpportunityDbModelSafe;

  const now = new Date();
  const daysSinceDatePosted = (now.getTime() - (project.creationDate as Timestamp).toMillis()) / 1000 / 60 / 60 / 24;
  const daysSinceFounderLastOnline = (now.getTime() - (founder.lastOnline as Timestamp).toMillis()) / 1000 / 60 / 60 / 24;
  const daysSinceLatestOppAdded = latestOpp ? (now.getTime() - (latestOpp.datePosted as Timestamp).toMillis()) / 1000 / 60 / 60 / 24 : 99999;

  console.log('\nCalculating project score for', projectId);

  const numOpenOpportunitiesScore = project.numOpenOpportunities ** 0.6 * 3;
  console.log('numOpenOpportunities score:', numOpenOpportunitiesScore);

  const websiteScore = project.website ? 2 : 0;
  console.log('website score:', websiteScore);

  const descriptionScore = project.description.length ** 0.2;
  console.log('description score:', descriptionScore);

  const summaryScore = project.summary.length ** 0.25;
  console.log('summary score:', summaryScore);

  const daysSinceDatePostedScore = -1 * daysSinceDatePosted ** 0.1;
  console.log(`daysSinceDatePosted (${daysSinceDatePosted.toFixed(1)}) score:`, daysSinceDatePostedScore);

  const daysSinceFounderLastOnlineScore = -1 * daysSinceFounderLastOnline ** 0.3;
  console.log(`daysSinceFounderLastOnline (${daysSinceFounderLastOnline.toFixed(1)}) score:`, daysSinceFounderLastOnlineScore);

  const daysSinceLatestOppAddedScore = Math.max(-5, -1 * daysSinceLatestOppAdded ** 0.35);
  console.log(`daysSinceLatestOppAdded (${daysSinceLatestOppAdded.toFixed(1)}) score:`, daysSinceLatestOppAddedScore);

  const founderKarmaScore = founder.karma ** 0.5;
  console.log('founderKarma score:', founderKarmaScore);

  const score = numOpenOpportunitiesScore + websiteScore + descriptionScore + summaryScore + daysSinceDatePostedScore + daysSinceFounderLastOnlineScore + daysSinceLatestOppAddedScore + founderKarmaScore;
  console.log('Final score:', score);

  return score;
}

/**
 * Depends on:
 * - User updated (karma, linkedin, github, website, bio, lastOnline)
 * @param userId
 */
export async function calculateProfileScore(userId: string) {
  const userSnapshot = await admin.firestore().collection(User.collectionName).doc(userId).get();
  const userData = userSnapshot.data() as UserDbModelSafe;

  const now = new Date();
  const daysSinceLastOnline = (now.getTime() - (userData.lastOnline as Timestamp).toMillis()) / 1000 / 60 / 60 / 24;
  const daysSinceJoined = (now.getTime() - (userData.dateJoined as Timestamp).toMillis()) / 1000 / 60 / 60 / 24;

  console.log('\nCalculating profile score for', userId);

  const karmaScore = userData.karma ** 0.6;
  console.log('karma score:', karmaScore);

  const linkedinScore = userData.linkedinUsername ? 2 : 0;
  console.log('linkedin score:', linkedinScore);

  const githubScore = userData.githubUsername ? 1 : 0;
  console.log('github score:', githubScore);

  const websiteScore = userData.website ? 1.5 : 0;
  console.log('website score:', websiteScore);

  const bioScore = userData.bio.length ** 0.25;
  console.log('bio score:', bioScore);

  const daysSinceLastOnlineScore = -1 * daysSinceLastOnline ** 0.5;
  console.log(`daysSinceLastOnline (${daysSinceLastOnline.toFixed(1)}) score:`, daysSinceLastOnlineScore);

  const daysSinceJoinedScore = -1 * daysSinceJoined ** 0.15;
  console.log(`daysSinceJoined (${daysSinceJoined.toFixed(1)}) score:`, daysSinceJoinedScore);

  const score = karmaScore + linkedinScore + githubScore + websiteScore + bioScore + daysSinceLastOnlineScore + daysSinceJoinedScore;
  console.log('Final score:', score);

  return score;
}

export function initSentryForFunction(functionName: string) {
  Sentry.setTag('function', functionName);
  Sentry.addBreadcrumb({
    message: `Detected function start: ${functionName}`,
    category: 'function',
    level: 'info',
  });
}
