name: Build app

on:
  workflow_call:
    secrets:
      FIREBASE_SERVICE_ACCOUNT_CREATOR_CAMPUS_APP:
        required: true
  pull_request:
  push:
    branches-ignore:
      - main
    paths:
      - 'admin/**'
      - 'common/**'
      - 'common-components/**'
      - 'production/**'
      - 'public/**'
      - 'package.json'
      - 'firebase.json'
      - '.firebaserc'
      - 'tsconfig.json'
      - 'vite.config.ts'
      - '.github/workflows/build_app.yml'

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Setup Node 20
        uses: actions/setup-node@v3
        with:
          node-version: 20.x
          cache: 'yarn'

      # Authenticate with Google Cloud
      - name: Authenticate with Google Cloud
        uses: google-github-actions/auth@v2
        with:
          project_id: 'creator-campus-app'
          credentials_json: '${{ secrets.FIREBASE_SERVICE_ACCOUNT_CREATOR_CAMPUS_APP }}' # Secret defined in GitHub repo

      # Fetch secrets from Google Secret Manager
      - name: Fetch secrets
        run: |
          echo "Fetching latest secrets list..."
          # List all secret names
          gcloud secrets list --format="value(name)" > secret_list.txt

          # Prepare the .env file
          > .env

          # Retrieve and write each secret
          while IFS= read -r SECRET_NAME; do
            if [[ $SECRET_NAME == REACT_APP* ]]; then
              SECRET_VALUE=$(gcloud secrets versions access latest --secret="$SECRET_NAME")
              echo "$SECRET_NAME=$SECRET_VALUE" >> .env
              echo "Exported $SECRET_NAME"
            fi
          done < secret_list.txt

      # Install dependencies
      - name: Install dependencies
        run: yarn install --immutable

      # Build app
      - name: Build app
        run: env $(cat .env | xargs) NODE_OPTIONS="--max-old-space-size=8192" yarn build

      # Upload the production app as an artifact
      - name: Upload build artifact (main app)
        uses: actions/upload-artifact@v4
        with:
          name: production-dist
          path: production/dist
          retention-days: 2

      # Upload the admin app as an artifact
      - name: Upload build artifact (admin app)
        uses: actions/upload-artifact@v4
        with:
          name: admin-dist
          path: admin/dist
          retention-days: 2
