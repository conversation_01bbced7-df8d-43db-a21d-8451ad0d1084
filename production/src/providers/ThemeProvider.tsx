import {CssVarsProvider, extendTheme, Theme} from '@mui/joy/styles';
import {ReactNode, useEffect, useState} from 'react';
import {useUser} from '@creator-campus/common-components';
import tinycolor from 'tinycolor2';
import CssBaseline from '@mui/joy/CssBaseline';
import GlobalStyles from '@mui/joy/GlobalStyles';
import theme from '../theme.tsx';

interface ThemeContextProps {
  children: ReactNode;
}

export function ThemeProvider({children}: ThemeContextProps) {
  const {university} = useUser();

  const [appTheme, setAppTheme] = useState<Theme>(theme);

  function generateColorScale(baseHex: string) {
    const base = tinycolor(baseHex);

    // MUI-style tonal scale
    const shades = {
      50: base.clone().lighten(52).toHexString(),
      100: base.clone().lighten(37).toHexString(),
      200: base.clone().lighten(26).toHexString(),
      300: base.clone().lighten(12).toHexString(),
      400: base.clone().lighten(6).toHexString(),
      500: base.clone().toHexString(),
      600: base.clone().darken(6).toHexString(),
      700: base.clone().darken(12).toHexString(),
      800: base.clone().darken(18).toHexString(),
      900: base.clone().darken(24).toHexString(),
    };

    return {
      ...shades,

      // Derived theme tokens using appropriate shades
      solidBg: shades[500],
      solidActiveBg: shades[600],

      outlinedBorder: shades[500],
      outlinedColor: shades[700],
      outlinedActiveBg: shades[100],

      softColor: shades[800],
      softBg: shades[200],
      softActiveBg: shades[300],

      plainColor: shades[700],
      plainActiveBg: shades[100],
    };
  }

  useEffect(() => {
    async function loadTheme() {
      const primaryColor = university?.branding?.primaryColor;

      if (!primaryColor) {
        setAppTheme(theme);
        return;
      }

      const palette = {
        primary: generateColorScale(primaryColor),
        primary_soft: generateColorScale(primaryColor),
      };

      const dynamicTheme = extendTheme({
        colorSchemes: {
          light: {
            palette: {
              ...theme.colorSchemes.light.palette,
              // Override the primary color
              ...palette,
            },
          },
          dark: {
            palette: {
              ...theme.colorSchemes.dark.palette,
              // Override the primary color
              ...palette,
            },
          },
        },
      });

      setAppTheme(dynamicTheme);
    }

    loadTheme();
  }, [university]);

  return (
    <CssVarsProvider
      defaultMode='light'
      disableTransitionOnChange
      theme={appTheme}
    >
      <CssBaseline />
      <GlobalStyles
        styles={{
          ':root': {
            '--Collapsed-breakpoint': '769px', // form will stretch when viewport is below `769px`
            '--Cover-width': '50vw', // must be `vw` only
            '--Form-maxWidth': '800px',
            '--Transition-duration': '0.4s', // set to `none` to disable transition
          },
        }}
      />
      {children}
    </CssVarsProvider>
  );
}
