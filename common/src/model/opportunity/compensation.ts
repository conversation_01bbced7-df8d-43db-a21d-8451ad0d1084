import Money from '@mui/icons-material/Money';
import ShowChart from '@mui/icons-material/ShowChart';
import AccessTime from '@mui/icons-material/AccessTime';
import VolunteerActivism from '@mui/icons-material/VolunteerActivism';
import {ElementType} from 'react';

export class Compensation {
  static readonly CASH = new Compensation('Cash', 'Cash', Money, () => false);
  static readonly EQUITY = new Compensation('Equity', 'Equity', ShowChart, () => false);
  static readonly FUTURE_EQUITY = new Compensation('Future equity', 'Future equity', AccessTime, () => false);
  static readonly VOLUNTEER = new Compensation('Volunteer', 'Volunteer', VolunteerActivism, () => true);

  private constructor(
    readonly id: string,
    readonly label: string,
    readonly icon: ElementType,
    readonly isExclusiveWith: (other: Compensation) => boolean,
  ) {}

  static fromId(id: string): Compensation | undefined {
    return this.values().find((c) => c.id === id);
  }

  static values(): Compensation[] {
    return Object.values(Compensation);
  }

  static getRandom() {
    return this.values()[Math.floor(Math.random() * this.values().length)]!;
  }
}
