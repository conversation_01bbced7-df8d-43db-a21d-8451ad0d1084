import {FormEvent, useEffect, useState} from 'react';
import Button from '@mui/joy/Button';
import FormControl from '@mui/joy/FormControl';
import FormLabel from '@mui/joy/FormLabel';
import Input from '@mui/joy/Input';
import Typography from '@mui/joy/Typography';
import Stack from '@mui/joy/Stack';
import {Alert, Checkbox, DialogActions, DialogContent, DialogTitle, ModalDialog} from '@mui/joy';
import {demoFirestore, firestore, functions, generateRandomId, MembershipApprovedEmailTemplate, sendEmail, University, UniversityConverter, usingEmulators} from '@creator-campus/common';
import {useSignupString, useSnackbar, LoadingIndicator} from '@creator-campus/common-components';
import {collection, getDocs} from 'firebase/firestore';
import Modal from '@mui/joy/Modal';
import Option from '@mui/joy/Option';
import Select from '@mui/joy/Select';
import {FirebaseProject} from '../model/firebaseProject.ts';
import {httpsCallable} from 'firebase/functions';
import {ContentCopy} from '@mui/icons-material';
import IconButton from '@mui/joy/IconButton';
import Tooltip from '@mui/joy/Tooltip';

interface FormElements extends HTMLFormControlsCollection {
  email: HTMLInputElement;
  password: HTMLInputElement;
  university: HTMLInputElement;
  persistent: HTMLInputElement;
  firstName: HTMLInputElement;
  lastName: HTMLInputElement;
}

interface AddUserFormElement extends HTMLFormElement {
  readonly elements: FormElements;
}

interface Props {
  open: boolean;
  setOpen: (open: boolean) => void;
}

export default function AddUserModal({open, setOpen}: Props) {
  const [universities, setUniversities] = useState<University[]>([]);
  const [university, setUniversity] = useState<University | null>(null);
  const [willBeOwner, setWillBeOwner] = useState<boolean>(false);
  const [firebaseProject, setFirebaseProject] = useState<FirebaseProject>(FirebaseProject.PRODUCTION);
  const [error, setError] = useState<string | null>(null);
  const [password, setPassword] = useState<string>(generateRandomId(6));

  const [loading, setLoading] = useState(false);

  const {showErrorSnackbar, showSnackbar} = useSnackbar();

  async function signUpNewUser(email: string, password: string, university: University, firstName: string, lastName: string) {
    // Create auth user
    const result = await httpsCallable(
      functions(),
      'createUser',
    )({
      email,
      password,
      userType: firebaseProject.id,
      universityId: university.id,
      firstName,
      lastName,
      willBeOwner,
    });

    const error = result.data as string | null;
    if (error) {
      showSnackbar('Something went wrong creating the user.');
      console.error(error);
      return;
    }

    if (firebaseProject === FirebaseProject.PRODUCTION) {
      await sendEmail(email, new MembershipApprovedEmailTemplate({name: `${firstName} ${lastName}`}));
    }

    await navigator.clipboard
      .writeText(password)
      .then(() => {
        showSnackbar('User added and password copied to clipboard.', 'success');
      })
      .catch(() => {
        showSnackbar('User added, but password could not be copied to clipboard.', 'warning');
      });

    setOpen(false);
  }

  function checkFormValid(firstName: string, lastName: string, email: string, password: string) {
    if (firstName.length === 0) {
      setError('First name is required.');
      return false;
    }

    if (lastName.length === 0) {
      setError('Last name is required.');
      return false;
    }

    if (email.length === 0) {
      setError('Email is required.');
      return false;
    } else if (!email.includes('@')) {
      setError('Email is invalid.');
      return false;
    }

    if (password.length < 6) {
      setError('Password is too short.');
      return false;
    }

    return true;
  }

  useEffect(() => {
    if (!open) {
      setError(null);
      setLoading(false);
      setUniversity(null);
    }
  }, [open]);

  useEffect(() => {
    const db = firebaseProject === FirebaseProject.PRODUCTION ? firestore() : demoFirestore();

    getDocs(collection(db, University.collectionName).withConverter(new UniversityConverter())).then((snapshot) => {
      setUniversities(snapshot.docs.map((d) => d.data()));
    });
  }, [firebaseProject]);

  return (
    <Modal
      open={open}
      onClose={() => setOpen(false)}
    >
      <ModalDialog>
        <form
          onSubmit={async (event: FormEvent<AddUserFormElement>) => {
            event.preventDefault();

            const formElements = event.currentTarget.elements;
            const data = {
              email: formElements.email.value.trim(),
              password: formElements.password.value.trim(),
              firstName: formElements.firstName.value.trim(),
              lastName: formElements.lastName.value.trim(),
            };

            if (!university) {
              setError('Please select a university from the dropdown.');
              return;
            }

            if (checkFormValid(data.firstName, data.lastName, data.email, data.password)) {
              setLoading(true);
              try {
                await signUpNewUser(data.email, data.password, university, data.firstName, data.lastName);
              } catch (e) {
                showErrorSnackbar(e, 'Error adding user. Check the console for details.');
              }
              setLoading(false);
            }
          }}
        >
          <DialogTitle>Add user</DialogTitle>
          <DialogContent>
            This form creates a user with a verified email address.
            <Stack
              gap={2}
              sx={{mt: 2, overflowX: 'hidden'}}
            >
              {error && (
                <Alert color='danger'>
                  <Typography
                    level='title-sm'
                    //@ts-ignore
                    color='danger-500'
                  >
                    {error}
                  </Typography>
                </Alert>
              )}
              <Stack spacing={2}>
                <FormControl required>
                  <FormLabel>User type:</FormLabel>
                  <Select
                    sx={{width: '250px', zIndex: 100, mb: 0.5}}
                    value={firebaseProject}
                    placeholder='Select...'
                    onChange={(_, userType) => {
                      if (userType) {
                        setFirebaseProject(userType);
                        setUniversity(null);
                        setUniversities([]);
                      }
                    }}
                    size='sm'
                  >
                    {FirebaseProject.values()
                      .filter((p) => !usingEmulators || p.id === 'production')
                      .map((projectType) => (
                        <Option
                          key={projectType.id}
                          value={projectType}
                        >
                          {projectType.label}
                        </Option>
                      ))}
                  </Select>
                </FormControl>
                <Stack
                  direction={{xs: 'column', sm: 'row'}}
                  spacing={2}
                >
                  <FormControl required>
                    <FormLabel>First name</FormLabel>
                    <Input
                      sx={{width: {xs: '100%', sm: '90%'}}}
                      type='text'
                      name='firstName'
                      autoComplete='firstName'
                    />
                  </FormControl>
                  <FormControl required>
                    <FormLabel>Last name</FormLabel>
                    <Input
                      sx={{width: {xs: '100%', sm: '90%'}}}
                      type='text'
                      name='lastName'
                      autoComplete='lastName'
                    />
                  </FormControl>
                </Stack>

                <Stack spacing={0.5}>
                  <FormControl required>
                    <FormLabel>University:</FormLabel>
                    <Select
                      sx={{width: '250px', zIndex: 100, mb: 0.5}}
                      value={university}
                      placeholder='Select...'
                      onChange={(_, uni) => {
                        if (uni) {
                          setUniversity(uni);
                        }
                      }}
                      size='sm'
                    >
                      {universities.map((uni) => (
                        <Option
                          key={uni.id}
                          value={uni}
                        >
                          {uni.name}
                        </Option>
                      ))}
                    </Select>
                  </FormControl>

                  <Tooltip title={"This user will have the 'Owner' role."}>
                    <Checkbox
                      label={"Give user the 'Owner' role"}
                      size={'sm'}
                      checked={willBeOwner}
                      onChange={(e) => {
                        setWillBeOwner(e.target.checked);
                      }}
                    />
                  </Tooltip>
                </Stack>

                <FormControl required>
                  <FormLabel>Email</FormLabel>
                  <Input
                    type='email'
                    name='email'
                    autoComplete='email'
                  />
                </FormControl>
                <FormControl required>
                  <FormLabel>{useSignupString().password}</FormLabel>
                  <Input
                    name='password'
                    value={password}
                    onChange={(e) => {
                      setPassword(e.target.value);
                    }}
                    endDecorator={
                      <IconButton
                        onClick={async () => {
                          await navigator.clipboard.writeText(password);
                          showSnackbar('Password copied to clipboard!');
                        }}
                      >
                        <ContentCopy />
                      </IconButton>
                    }
                  />
                </FormControl>
              </Stack>
            </Stack>
          </DialogContent>
          <DialogActions>
            <Button
              disabled={loading}
              type='submit'
            >
              {loading ? <LoadingIndicator size='sm' /> : 'Add user'}
            </Button>
            <Button
              variant='plain'
              color='neutral'
              onClick={() => setOpen(false)}
            >
              Cancel
            </Button>
          </DialogActions>
        </form>
      </ModalDialog>
    </Modal>
  );
}
