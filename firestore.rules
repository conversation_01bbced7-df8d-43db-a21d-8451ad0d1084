rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    function loggedIn() {
      return request.auth != null;
    }

    // The value of request.auth.token.email_verified is only updated when
    // the user logs out and in again so this shouldn't be used
    // function emailVerified() {
    // 	return request.auth.token.email_verified;
    // }

    function user() {
      return get(/databases/$(database)/documents/people/$(request.auth.uid)).data;
    }

    function profileComplete() {
      return user().profileCompleted;
    }

    function onlyAffectedFieldsAre(fields) {
      return request.resource.data.diff(resource.data).affectedKeys().hasOnly(fields);
    }

    function fieldsAreUnchanged(fields) {
      return !request.resource.data.diff(resource.data).affectedKeys().hasAny(fields);
    }

    function ownerIsUpdatingStaffRole() {
      let u = user();
      return u.staffRole == 'owner' && u.universityId == resource.data.universityId && onlyAffectedFieldsAre(['staffRole']);
    }

    function isUniAdmin(uniId) {
      // uniId can be null to represent an admin of any university
        let u = user();
      let fromCorrectUni = uniId == null || u.universityId == uniId;
      let hasCorrectRole = u.staffRole == 'owner' || u.staffRole == 'admin';

        return fromCorrectUni && hasCorrectRole;
    }

    function isCreatorCampusAdmin() {
      let email = request.auth.token.email;
      return email == '<EMAIL>' || email == '<EMAIL>';
    }

    // ------------ //
    // ------------ //
    // ------------ //

    match /approvedMentors/{mentorId} {
      // Allow uni admins to view
      allow read: if loggedIn() && isUniAdmin(null);
      // Allow uni admins to add/edit/delete mentor invites for their own university
      allow create, update: if loggedIn() && isUniAdmin(null) && request.resource.data.universityId == user().universityId;
      allow delete: if loggedIn() && isUniAdmin(null) && resource.data.universityId == user().universityId;
    }

    // ------------ //

    match /config/{configId} {
    // Allow anyone to read config (e.g. maintenance mode)
      allow get: if true
      allow list, write: if false
    }

    // ------------ //

    match /email_templates/{templateId} {
      allow read: if loggedIn() && isCreatorCampusAdmin();
      // Email templates are managed via the Firebase console
      allow write: if false;
    }

    // ------------ //

    match /global/platform {
      // Allow users to read platform info
      allow read: if loggedIn();
      allow write: if isCreatorCampusAdmin();
    }

    match /global/discussion {
      // Allow users to read discussion info
      allow read: if loggedIn();
      allow write: if isCreatorCampusAdmin();
    }

    match /global/discussion/discussion/{postId} {
      // Allow users to read global discussion posts
      allow read, create: if loggedIn();
      // Allow users to edit their own posts
      allow update, delete: if loggedIn() && (resource.data.authorId == request.auth.uid || onlyAffectedFieldsAre(['upVotes']) || isCreatorCampusAdmin())
    }

    match /global/discussion/discussion/{postId}/comments/{commentId} {
      // Allow users to read global discussion comments
      allow read, create: if loggedIn();
      // Allow users to edit their own comments
      allow update, delete: if loggedIn() && (resource.data.authorId == request.auth.uid || onlyAffectedFieldsAre(['upVotes']) || isCreatorCampusAdmin())
    }

    // ------------ //

    match /mail/{mailId} {
      allow create: if loggedIn()
    }

    // ------------ //

    match /opportunities/{oppId} {
      // Allow anyone to read opportunities (for opportunity sharing)
      allow read: if true
      // Only the project owner can edit opportunities
      allow write: if loggedIn() && (request.resource.data.ownerId == request.auth.uid || resource.data.ownerId == request.auth.uid);
    }

    // ------------ //

    match /opportunityApplications/{applicationId} {
      // Only the project owner can read applications
      allow read: if loggedIn() && resource.data.projectOwnerId == request.auth.uid;
      // The applicant can only write IDs with themselves as the author
      allow create: if loggedIn() && request.resource.data.applicantId == request.auth.uid;
      allow update: if loggedIn() && resource.data.projectOwnerId == request.auth.uid;
    }

    // ------------ //

    match /people/{userId} {
      allow read: if true  // Allow publicly shareable profile links
      allow update: if onlyAffectedFieldsAre(['lastViewedDiscussion']) || (loggedIn() && (request.auth.uid == userId || ownerIsUpdatingStaffRole()))
      // Allow users to update their own profile, but only let admins change their applicationStatus to 'accepted'
      allow write: if loggedIn() && ((request.auth.uid == userId && request.resource.data.applicationStatus != 'accepted') || isCreatorCampusAdmin())
    }

    match /people/{userId}/private/{docId} {
      allow read, write: if loggedIn() && (request.auth.uid == userId || isCreatorCampusAdmin())
    }

    // ------------ //

    match /projects/{projectId} {
      allow get: if true  // Allow publicly shareable project links
      allow list: if loggedIn() && (profileComplete() || isUniAdmin(null))
      allow create: if loggedIn() && request.resource.data.ownerId == request.auth.uid && (request.resource.data.hidden || profileComplete())
      allow update: if loggedIn() && (request.resource.data.ownerId == request.auth.uid || onlyAffectedFieldsAre(['roles']) || isCreatorCampusAdmin()) && (request.resource.data.hidden || profileComplete())
      allow delete: if loggedIn() && resource.data.ownerId == request.auth.uid && (resource.data.hidden || profileComplete())
    }

    // ------------ //

    match /universities/{uniId} {
      // Allow registering users to find their uni from their email domain
      allow read: if true
      allow write: if loggedIn() && (isCreatorCampusAdmin() || (isUniAdmin(uniId) && fieldsAreUnchanged(['dateJoined', 'domain', 'reachedUserLimit', 'almostReachedUserLimit'])))
    }

    match /universities/{uniId}/alumniApplications/{applicationId} {
      // Allow university staff to read alumni applications
      allow read: if loggedIn() && isUniAdmin(uniId)
      // Write operations are handled by Firebase Functions
      allow write: if false
    }

    match /universities/{uniId}/discussion/{postId} {
      // Allow users to read discussion posts in their own university
      allow read, create: if loggedIn()
      // Allow users to edit their own posts
      allow update, delete: if loggedIn() && (resource.data.authorId == request.auth.uid || onlyAffectedFieldsAre(['upVotes']) || user().staffRole != null)
    }

    match /universities/{uniId}/discussion/{postId}/comments/{commentId} {
      // Allow users to read discussion comments in their own university
      allow read, create: if loggedIn() && user().universityId == uniId
      // Allow users to edit their own comments
      allow update, delete: if loggedIn() && (resource.data.authorId == request.auth.uid || onlyAffectedFieldsAre(['upVotes']) || user().staffRole != null)
    }

    match /universities/{uniId}/metrics/metrics {
      allow read: if loggedIn() && user().staffRole != null;
      // Metric write operations happen in Firebase Functions
      allow write: if isCreatorCampusAdmin();
    }

    match /universities/{uniId}/private/{docId} {
      allow read, write: if loggedIn() && (isUniAdmin(uniId) || isCreatorCampusAdmin())
    }
  }
}
