{"compilerOptions": {"module": "commonjs", "target": "es2020", "lib": ["es2020"], "outDir": "lib", "rootDir": "src", "sourceMap": true, "strict": true, "esModuleInterop": true, "skipLibCheck": true, "moduleResolution": "node", "baseUrl": ".", "resolveJsonModule": true, "paths": {"common": ["../common/dist"]}, "forceConsistentCasingInFileNames": true}, "include": ["src/**/*.ts"], "exclude": ["node_modules", "lib"]}