import {useEffect, useState} from 'react';
import Button from '@mui/joy/Button';
import FormControl from '@mui/joy/FormControl';
import Input from '@mui/joy/Input';
import Stack from '@mui/joy/Stack';
import SearchRoundedIcon from '@mui/icons-material/SearchRounded';
import Typography from '@mui/joy/Typography';
import {Box} from '@mui/joy';
import {useSearchBox, UseSearchBoxProps, useStats} from 'react-instantsearch';
import {Clear} from '@mui/icons-material';
import IconButton from '@mui/joy/IconButton';
import {useTheme} from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import {useLocation} from 'react-router-dom';
import {influxWriter, newAppMetric} from '@creator-campus/common';
import {useUser} from '../providers/UserProvider';

interface Props extends UseSearchBoxProps {
  placeholderPrefix?: string;
  searchPlaceholders?: string[];
  placeholderOffset?: number;
  placeholderCyclePeriodMs?: number;
}

export function AlgoliaSearchBar({placeholderPrefix = 'Search...', searchPlaceholders, placeholderOffset = 0, placeholderCyclePeriodMs = 4000, ...algoliaProps}: Props) {
  const {nbHits} = useStats();
  const {query, refine} = useSearchBox(algoliaProps);
  const [inputValue, setInputValue] = useState(query);
  const {pathname} = useLocation();
  const {user} = useUser();

  const [currentPlaceholderIndex, setCurrentPlaceholderIndex] = useState<number | null>(searchPlaceholders ? Math.floor(Math.random() * searchPlaceholders.length) : null);
  const [placeholderFading, setPlaceholderFading] = useState(false);

  const theme = useTheme();
  const isXsScreen = useMediaQuery(theme.breakpoints.down('sm'));

  useEffect(() => {
    if (!searchPlaceholders) {
      return;
    }

    const interval = setInterval(() => {
      setPlaceholderFading(true);
      setTimeout(() => {
        setCurrentPlaceholderIndex((prevIndex) => (prevIndex! + 1) % searchPlaceholders.length);
        setPlaceholderFading(false);
      }, 500); // This should match the placeholder's transition duration
    }, placeholderCyclePeriodMs);

    return () => clearInterval(interval);
  }, [searchPlaceholders]);

  useEffect(() => {
    if (inputValue.trim() === '') {
      refine('');
    }
  }, [inputValue]);

  function AlgoliaLogo() {
    return (
      <Box
        component='span'
        maxHeight={15}
        onClick={() => {
          const algoliaUrl = 'https://www.algolia.com/';
          window.open(algoliaUrl, '_blank');
        }}
        style={{cursor: 'pointer'}}
        sx={{
          width: '70px',
        }}
      >
        <img
          src='https://upload.wikimedia.org/wikipedia/commons/4/4c/Algolia_logo_full_blue.svg'
          alt='Algolia Logo'
          style={{maxWidth: '100%', maxHeight: '100%'}}
        />
      </Box>
    );
  }

  return (
    <form
      action=''
      role='search'
      noValidate
      onSubmit={(event) => {
        event.preventDefault();
        event.stopPropagation();

        refine(inputValue);

        const activeElement = document.activeElement as HTMLElement;
        if (activeElement instanceof HTMLElement) {
          activeElement.blur();
        }

        if (!user) {
          return;
        }

        const metric = newAppMetric(user).intField('searches', 1).tag('page', pathname);
        influxWriter.writePoint(metric);
      }}
    >
      <Stack
        spacing={1}
        direction='row'
        sx={{mb: 2}}
      >
        <FormControl sx={{flex: 1}}>
          <Input
            placeholder={isXsScreen ? 'Search...' : placeholderPrefix}
            startDecorator={<SearchRoundedIcon />}
            endDecorator={
              inputValue && (
                <IconButton onClick={() => setInputValue('')}>
                  <Clear />
                </IconButton>
              )
            }
            type='search'
            slotProps={{input: {maxLength: 500}}}
            value={inputValue}
            onChange={(event) => setInputValue(event.currentTarget.value)}
            sx={{
              '& input[type="search"]::-webkit-search-cancel-button': {
                display: 'none',
              },
            }}
          />
          {inputValue === '' && currentPlaceholderIndex !== null && (
            <Typography
              position='absolute'
              left={placeholderOffset}
              top={'50%'}
              sx={{
                color: 'grey',
                opacity: placeholderFading ? 0 : 1,
                transform: 'translateY(-50%)',
                transition: 'opacity 0.5s ease-in-out',
                pointerEvents: 'none',
                display: {xs: 'none', sm: 'block'},
              }}
            >
              {`${searchPlaceholders![currentPlaceholderIndex]}...`}
            </Typography>
          )}
        </FormControl>
        <Button
          variant='solid'
          color='primary'
          type='submit'
        >
          Search
        </Button>
      </Stack>
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          width: '100%',
        }}
      >
        <Typography
          level='body-sm'
          sx={{flex: 1}}
        >
          {nbHits.toString()} results
        </Typography>
        <Typography
          level='body-sm'
          endDecorator={<AlgoliaLogo />}
        >
          powered by
        </Typography>
      </Box>
    </form>
  );
}
