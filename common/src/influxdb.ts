import {InfluxDB, Point} from '@influxdata/influxdb-client';
import {Timespan} from './model/influxdb/timespan';
import {logger} from './logger';
import {User} from './model/user/user';

export const influxMeasurement = 'metrics';
const appMetricsMeasurement = 'app_metrics';
export const influxUrl = 'https://eu-central-1-1.aws.cloud2.influxdata.com';
export const influxOrg = 'CreatorCampus';
export const influxBucket = 'test-bucket2';

const writeOptions = {
  flushInterval: 5000, // flush every 5s if there are points to send
  maxRetries: 3,
  defaultTags: {}, // default tags added to each point
};

const client = new InfluxDB({
  url: influxUrl,
  token: process.env.REACT_APP_INFLUX_DB_TOKEN,
});

export const influxQuery = client.getQueryApi(influxOrg);
export const influxWriter = client.getWriteApi(influxOrg, influxBucket, undefined, writeOptions);
export const newAppMetric = (user: User) =>
  new Point(appMetricsMeasurement)
    .tag('universityId', user.universityId)
    .tag('applicationStatus', user.applicationStatus)
    .tag('profileCompleted', `${user.profileCompleted}`)
    .tag('staffRole', user.staffRole?.id || 'none')
    .tag('role', user.role?.id || 'none')
    .tag('persona', user.persona?.id || 'none')
    .tag('founder', `${user.founder}`)
    .tag('openToWork', `${user.openToWork}`)
    .tag('karmaLevel', user.karma < 10 ? 'low' : user.karma < 50 ? 'medium' : 'high');

export class InfluxData {
  ts: Date;
  value: number;

  constructor(ts: Date, value: number) {
    this.ts = ts;
    this.value = value;
  }
}

export async function fetchInfluxData(fields: string[], timespan: Timespan, universityId?: string): Promise<Map<string, InfluxData[]>> {
  const now = new Date();

  let stop;
  if (timespan.secsDuration > 86400) {
    const startOfToday = new Date(Date.UTC(now.getFullYear(), now.getMonth(), now.getDate()));

    stop = new Date(startOfToday);
    stop.setDate(startOfToday.getDate() + 1); // Include points up to start of tomorrow
  } else if (timespan.secsDuration > 3600) {
    stop = new Date();
    stop.setHours(stop.getHours() + 1);
    stop.setMinutes(0, 0, 0);
  } else {
    stop = new Date();
  }

  const start = new Date(stop.getTime() - timespan.secsDuration * 1000);

  const query = `
      from(bucket: "${influxBucket}")
        |> range(start: ${start.toISOString()}, stop: ${stop.toISOString()})
        |> filter(fn: (r) => r._measurement == "${influxMeasurement}")
        |> filter(fn: (r) => contains(value: r._field, set: [${fields.map((field) => `"${field}"`)}]))
        ${universityId ? `|> filter(fn: (r) => r.university == "${universityId}")` : ''}
        |> aggregateWindow(every: ${timespan.step}, fn: sum)
        |> fill(value: 0)
    `;

  const rows: any[] = [];
  return new Promise((resolve, reject) => {
    influxQuery.queryRows(query, {
      next(row, tableMeta) {
        const o = tableMeta.toObject(row);
        rows.push(o);
      },
      error(error) {
        logger.error('Query failed:', error);
        reject(error);
      },
      complete() {
        const data: Map<string, InfluxData[]> = new Map<string, InfluxData[]>();
        for (const field of fields) {
          let fieldData = rows.filter((row) => row._field === field);
          logger.debug(`Fetched Influx data (${field}):`, fieldData);

          fieldData = fieldData.map((row) => {
            const date = new Date(row._time);

            if (timespan.secsDuration > 86400) {
              date.setDate(date.getDate() - 1);
            } else if (timespan.secsDuration > 3600) {
              date.setHours(date.getHours() - 1);
            }

            return new InfluxData(date, row._value);
          });

          // Pad data if empty
          if (fieldData.length === 0) {
            fieldData.push(new InfluxData(new Date(new Date().getTime() - timespan.secsDuration * 1000), 0));
            fieldData.push(new InfluxData(new Date(), 0));
          }

          data.set(field, fieldData);
        }

        resolve(data);
      },
    });
  });
}
