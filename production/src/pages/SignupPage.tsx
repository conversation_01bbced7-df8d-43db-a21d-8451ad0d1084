import Link from '@mui/joy/Link';
import Typography from '@mui/joy/Typography';
import Stack from '@mui/joy/Stack';
import {TabData} from '@creator-campus/common';
import {Tabs, useSignupString} from '@creator-campus/common-components';
import {Diversity3, People, School} from '@mui/icons-material';
import SignupForm from '../components/SignupForm.tsx';
import {useParams, useSearchParams} from 'react-router-dom';
import {CommunitySignupTab} from '../components/CommunitySignupTab.tsx';
import {ProductionLandingPageShell} from '../components/ProductionLandingPageShell.tsx';

export default function SignupPage() {
  const params = useParams();
  const [searchParams, _setSearchParams] = useSearchParams();

  const tabs: TabData[] = [
    {
      displayName: 'Students & Staff',
      leadingIcon: <People />,
      path: 'students',
      content: (
        <SignupForm
          variant={'students'}
          initialEmail={searchParams.get('email') || undefined}
        />
      ),
    },
    {
      displayName: 'Graduates',
      leadingIcon: <School />,
      path: 'graduates',
      content: <SignupForm variant={'graduates'} />,
    },
    {
      displayName: 'Community',
      leadingIcon: <Diversity3 />,
      path: 'community',
      content: <CommunitySignupTab />,
    },
  ];

  return (
    <ProductionLandingPageShell>
      <Stack gap={1}>
        <Typography level='h3'>{useSignupString().sign_up}</Typography>
        <Typography level='body-sm'>
          Already a member?{' '}
          <Link
            href={'/login'}
            level='title-sm'
          >
            Sign in.
          </Link>
        </Typography>
      </Stack>
      <Tabs
        tabs={tabs.map((t) => ({
          ...t,
          path: `/signup/${t.path}`,
        }))}
        value={`/signup/${params.tab}`}
        leftPadding={0}
      />
    </ProductionLandingPageShell>
  );
}
