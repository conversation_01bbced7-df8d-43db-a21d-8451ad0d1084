import IconButton from '@mui/joy/IconButton';
import {PushPin, PushPinOutlined} from '@mui/icons-material';
import Tooltip from '@mui/joy/Tooltip';
import {useState} from 'react';
import {FeatureContentModal} from './FeatureContentModal.tsx';
import {Project, UserHit} from '@creator-campus/common';

interface Props {
  content: UserHit | Project;
}

export function FeatureContentButton({content}: Props) {
  const [modalOpen, setModalOpen] = useState<boolean>(false);

  const featured = !!content.featuredUntil;

  return (
    <>
      {modalOpen && (
        <FeatureContentModal
          content={content}
          onClose={() => setModalOpen(false)}
        />
      )}
      <Tooltip
        enterTouchDelay={0}
        variant='solid'
        title={`${featured ? 'Stop featuring' : 'Feature'} this at the top of the page.`}
        placement={'top'}
      >
        <IconButton
          variant={'soft'}
          sx={{width: 2}}
          size={'sm'}
          onClick={() => setModalOpen(true)}
        >
          {featured ? <PushPin color={'primary'} /> : <PushPinOutlined />}
        </IconButton>
      </Tooltip>
    </>
  );
}
