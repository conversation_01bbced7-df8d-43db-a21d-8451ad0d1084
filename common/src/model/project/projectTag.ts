import AddShoppingCart from '@mui/icons-material/AddShoppingCart';
import Agriculture from '@mui/icons-material/Agriculture';
import AttachMoney from '@mui/icons-material/AttachMoney';
import AutoAwesome from '@mui/icons-material/AutoAwesome';
import BikeScooter from '@mui/icons-material/BikeScooter';
import Biotech from '@mui/icons-material/Biotech';
import CameraAlt from '@mui/icons-material/CameraAlt';
import DesignServices from '@mui/icons-material/DesignServices';
import Diamond from '@mui/icons-material/Diamond';
import Diversity2 from '@mui/icons-material/Diversity2';
import Event from '@mui/icons-material/Event';
import Factory from '@mui/icons-material/Factory';
import Fastfood from '@mui/icons-material/Fastfood';
import FeaturedVideo from '@mui/icons-material/FeaturedVideo';
import FitnessCenter from '@mui/icons-material/FitnessCenter';
import Forest from '@mui/icons-material/Forest';
import Gite from '@mui/icons-material/Gite';
import HealthAndSafety from '@mui/icons-material/HealthAndSafety';
import LibraryMusic from '@mui/icons-material/LibraryMusic';
import MiscellaneousServices from '@mui/icons-material/MiscellaneousServices';
import Newspaper from '@mui/icons-material/Newspaper';
import PrecisionManufacturing from '@mui/icons-material/PrecisionManufacturing';
import School from '@mui/icons-material/School';
import Security from '@mui/icons-material/Security';
import SportsEsports from '@mui/icons-material/SportsEsports';
import Tag from '@mui/icons-material/Tag';
import Terminal from '@mui/icons-material/Terminal';
import TravelExplore from '@mui/icons-material/TravelExplore';
import Tv from '@mui/icons-material/Tv';
import ViewInAr from '@mui/icons-material/ViewInAr';
import {type ElementType} from 'react';

export class ProjectTag {
  static readonly EDUCATION = new ProjectTag('Education', 'Education', School);
  static readonly HEALTHCARE = new ProjectTag('Healthcare', 'Healthcare', HealthAndSafety);
  static readonly FINANCE = new ProjectTag('Finance', 'Finance', AttachMoney);
  static readonly SOCIAL_ENTERPRISE = new ProjectTag('Social Enterprise', 'Social Enterprise', Diversity2);
  static readonly SUSTAINABILITY = new ProjectTag('Sustainability', 'Sustainability', Forest);
  static readonly AGRICULTURE = new ProjectTag('Agriculture', 'Agriculture', Agriculture);
  static readonly ART_AND_DESIGN = new ProjectTag('Art & Design', 'Art & Design', DesignServices);
  static readonly EVENTS = new ProjectTag('Events', 'Events', Event);
  static readonly FOOD_AND_BEVERAGES = new ProjectTag('Food & Beverages', 'Food & Beverages', Fastfood);
  static readonly FASHION = new ProjectTag('Fashion', 'Fashion', Diamond);
  static readonly ECOMMERCE = new ProjectTag('eCommerce', 'eCommerce', AddShoppingCart);
  static readonly SAAS = new ProjectTag('SaaS', 'SaaS', Terminal);
  static readonly TRAVEL_AND_TOURISM = new ProjectTag('Travel & Tourism', 'Travel & Tourism', TravelExplore);
  static readonly GAMING = new ProjectTag('Gaming', 'Gaming', SportsEsports);
  static readonly ENTERTAINMENT = new ProjectTag('Entertainment', 'Entertainment', Tv);
  static readonly SPORTS_AND_FITNESS = new ProjectTag('Sports & Fitness', 'Sports & Fitness', FitnessCenter);
  static readonly HEALTH_AND_WELLNESS = new ProjectTag('Health & Wellness', 'Health & Wellness', HealthAndSafety);
  static readonly REAL_ESTATE_AND_PROPERTY = new ProjectTag('Real Estate & Property', 'Real Estate & Property', Gite);
  static readonly AI = new ProjectTag('AI', 'AI', AutoAwesome);
  static readonly ROBOTICS = new ProjectTag('Robotics', 'Robotics', PrecisionManufacturing);
  static readonly AR_VR = new ProjectTag('AR/VR', 'AR/VR', ViewInAr);
  static readonly CYBERSECURITY = new ProjectTag('Cybersecurity', 'Cybersecurity', Security);
  static readonly BIOTECH = new ProjectTag('BioTech', 'BioTech', Biotech);
  static readonly TRANSPORTATION = new ProjectTag('Transportation', 'Transportation', BikeScooter);
  static readonly MANUFACTURING = new ProjectTag('Manufacturing', 'Manufacturing', Factory);
  static readonly MARKETING_AND_ADVERTISING = new ProjectTag('Marketing & Advertising', 'Marketing & Advertising', FeaturedVideo);
  static readonly SOCIAL_MEDIA = new ProjectTag('Social Media', 'Social Media', Tag);
  static readonly MEDIA_AND_JOURNALISM = new ProjectTag('Media & Journalism', 'Media & Journalism', Newspaper);
  static readonly MUSIC_AND_AUDIO = new ProjectTag('Music & Audio', 'Music & Audio', LibraryMusic);
  static readonly PHOTO_AND_VIDEO = new ProjectTag('Photo & Video', 'Photo & Video', CameraAlt);
  static readonly OTHER = new ProjectTag('Other', 'Other', MiscellaneousServices);

  private constructor(
    readonly id: string,
    readonly label: string,
    readonly icon: ElementType,
  ) {}

  static fromId(id: string): ProjectTag | undefined {
    return this.values().find((c) => c.id === id);
  }

  static values(): ProjectTag[] {
    return Object.values(ProjectTag);
  }

  static getRandom() {
    return this.values()[Math.floor(Math.random() * this.values().length)]!;
  }
}
