import {FieldValue} from 'firebase-admin/firestore';
import {onDocumentCreated, onDocumentDeleted, onDocumentUpdated} from 'firebase-functions/v2/firestore';
import * as admin from 'firebase-admin';
import {getInfluxWriter, newPoint} from './influxdb';
import {Opportunity, ProjectDbModelSafe, User} from '@creator-campus/common';
import {calculateProjectScore} from './utils';

export const onProjectCreated = onDocumentCreated(
  {
    document: 'projects/{projectId}',
    secrets: ['REACT_APP_INFLUX_DB_TOKEN'],
  },
  async (event) => {
    if (!event.data) {
      console.error('Event data is undefined!');
      return;
    }

    const data = event.data.data();
    const projectId = event.params.projectId;
    console.log(`Detected project ${projectId} created.`);

    // Update metrics
    console.log('Updating metrics...');
    const influxToken = String(process.env.REACT_APP_INFLUX_DB_TOKEN);
    const influxWriter = getInfluxWriter(influxToken);

    const point = newPoint().intField('projectsCreated', 1).tag('university', data.universityId);
    influxWriter.writePoint(point);
  },
);

export const onProjectUpdated = onDocumentUpdated(
  {
    document: 'projects/{projectId}',
  },
  async (event) => {
    if (!event.data) {
      console.error('Event data is undefined!');
      return;
    }

    const beforeData = event.data.before.data() as ProjectDbModelSafe;
    const afterData = event.data.after.data() as ProjectDbModelSafe;
    const projectId = event.params.projectId;
    console.log(`Detected project ${projectId} updated.`);

    const scoreUpdated = beforeData.numOpenOpportunities !== afterData.numOpenOpportunities || beforeData.description !== afterData.description || beforeData.summary !== afterData.summary || beforeData.website !== afterData.website;

    if (scoreUpdated) {
      const newScore = await calculateProjectScore(projectId, afterData);

      console.log(`Project score changed from ${beforeData.score} to ${newScore}. Updating...`);
      return event.data.after.ref.update({
        score: newScore,
      });
    }
  },
);

export const onProjectDeleted = onDocumentDeleted('projects/{projectId}', async (event) => {
  if (!event.data) {
    console.error('Event data is undefined!');
    return;
  }

  const data = event.data.data();
  const projectId = event.params.projectId;
  console.log(`Detected project ${projectId} deleted.`);

  // Remove project ID from user
  console.log(`Detaching project from owner: ${data.ownerId}...`);
  await admin
    .firestore()
    .collection(User.collectionName)
    .doc(data.ownerId)
    .update({
      projectIds: FieldValue.arrayRemove(projectId),
    })
    .catch(() => console.log('Could not remove project ID from user. This is expected behaviour if this Function was triggered by a user deletion.'));

  // Delete related opportunities
  const opportunitiesSnapshot = await admin.firestore().collection(Opportunity.collectionName).where('projectId', '==', projectId).get();
  console.log(`Deleting ${opportunitiesSnapshot.size} related opportunities...`);
  for (const opp of opportunitiesSnapshot.docs) {
    await opp.ref.delete();
  }

  // Delete project files in Firebase Storage
  admin
    .app()
    .storage()
    .bucket()
    .deleteFiles({prefix: `projects/${projectId}/`}, (e) => {
      if (e) {
        console.error(e);
      } else {
        console.log('Deleted related project files in Firebase Storage.');
      }
    });
});
