import {useEffect, useRef, useState} from 'react';
import {DiscussionPost, DiscussionPostConverter, firestore, logger, NonexistentUser, StaffRole, University, User} from '@creator-campus/common';
import {FirestoreInfiniteScroll, LoadingIndicator, useActivateCommunityString, useSnackbar, useUser} from '@creator-campus/common-components';
import DiscussionPostCard from '../components/DiscussionPostCard.tsx';
import {collection, getDoc, Timestamp, updateDoc} from 'firebase/firestore';
import DiscussionInput from '../components/DiscussionInput.tsx';
import Box from '@mui/joy/Box';
import Button from '@mui/joy/Button';
import {ArrowUpward, PushPin} from '@mui/icons-material';
import Stack from '@mui/joy/Stack';
import DiscussionCommentsModal from '../components/modals/DiscussionCommentsModal.tsx';
import AccordionSummary from '@mui/joy/AccordionSummary';
import Accordion from '@mui/joy/Accordion';
import AccordionGroup from '@mui/joy/AccordionGroup';
import AccordionDetails from '@mui/joy/AccordionDetails';
import {accordionClasses} from '@mui/joy';
import {useParams} from 'react-router-dom';
import {BlurryOverlay} from './BlurryOverlay.tsx';
import {NoPostsPlaceholder} from './NoPostsPlaceholder.tsx';

export const discussionFeedWidth = 700;

interface Props {
  scrollableTarget: HTMLDivElement | null;
  universityId?: string;
}

export default function DiscussionContent({scrollableTarget, universityId}: Props) {
  const postHeight = 180; // Approx height of a post in pixels
  const backToTopThresh = 250;

  // Populated lazily as posts made by unseen users are loaded.
  const cachedUsers = useRef<Record<string, User | NonexistentUser>>({});
  const cachedProfilePicUrls = useRef<Record<string, string>>({});
  const [_cacheVersion, setCacheVersion] = useState<number>(0);

  const [pinnedPosts, setPinnedPosts] = useState<DiscussionPost[]>([]);
  const [showBackToTop, setShowBackToTop] = useState<boolean>(false);
  const [initialCommentsModalPost, setInitialCommentsModalPost] = useState<DiscussionPost | null>(null);

  const {user, profilePicUrl, university} = useUser();
  const {showErrorSnackbar} = useSnackbar();
  const params = useParams();
  const strings = useActivateCommunityString();

  const scrollToTop = () => {
    if (scrollableTarget) {
      scrollableTarget.scrollTo({top: 0, behavior: 'smooth'});
    }
  };

  // Helper functions to update caches and force a re-render
  function cacheUser(userId: string, userData: User | NonexistentUser) {
    cachedUsers.current[userId] = userData;
    setCacheVersion((prev) => prev + 1);
  }

  function cacheProfilePicUrl(userId: string, url: string) {
    cachedProfilePicUrls.current[userId] = url;
    setCacheVersion((prev) => prev + 1);
  }

  useEffect(() => {
    // Open the relevant post comments if the discussion page was reached via /discussion/post/{universityId}/{postId}
    if (params.postId) {
      getDoc(DiscussionPost.doc(params.postId, universityId)).then((postSnapshot) => {
        const post = postSnapshot.data();
        if (post) {
          setInitialCommentsModalPost(post);
        }
      });
    }
  }, [universityId, params.postId]);

  useEffect(() => {
    async function fetchPinnedPosts() {
      const pinnedPosts = [];

      if (universityId) {
        if (!university?.pinnedPosts) {
          return;
        }

        pinnedPosts.push(...university.pinnedPosts);
      } else {
        const globalPinnedPosts = await DiscussionPost.fetchGlobalPinnedPostIds();
        pinnedPosts.push(...globalPinnedPosts);
      }

      // Fetch all pinned posts from IDs in the global/university discussion document
      const promises = [];
      for (const postId of pinnedPosts) {
        promises.push(getDoc(DiscussionPost.doc(postId, universityId)));
      }

      Promise.all(promises).then((postSnapshots) => {
        const existingSnapshots = postSnapshots.filter((snapshot) => snapshot.exists());
        setPinnedPosts(existingSnapshots.map((snapshot) => snapshot.data()!));
      });
    }

    fetchPinnedPosts();
  }, [universityId, university?.pinnedPosts]);

  useEffect(() => {
    const handleScroll = () => {
      if (scrollableTarget) {
        const position = scrollableTarget.scrollTop;
        setShowBackToTop(position > backToTopThresh);
      }
    };

    if (scrollableTarget) {
      scrollableTarget.addEventListener('scroll', handleScroll);
      return () => scrollableTarget.removeEventListener('scroll', handleScroll);
    }
  }, []);

  useEffect(() => {
    if (user !== null) {
      cacheUser(user.id, user);

      if (profilePicUrl !== null) {
        cacheProfilePicUrl(user.id, profilePicUrl);
      }
    }
  }, [user, profilePicUrl]);

  async function updateLastSeen() {
    if (!user) {
      return;
    }

    await updateDoc(user._doc, {
      lastViewedDiscussion: Timestamp.now(),
    }).catch((e) => showErrorSnackbar(e, 'Error updating unread posts.'));
  }

  useEffect(() => {
    updateLastSeen();
    window.addEventListener('beforeunload', updateLastSeen);

    return () => {
      window.removeEventListener('beforeunload', updateLastSeen);
      updateLastSeen();
    };
  }, []);

  /**
   * Updates `cachedUsers` and `cachedProfilePicUrls` with full `User` data for any unseen users in posts or comments.
   * @param newUserIds The new user IDs seen in newly fetched posts or comments.
   */
  function updateCachedUsers(newUserIds: string[]) {
    const unknownUserIds = new Set(newUserIds.filter((x) => !new Set(Object.keys(cachedUsers)).has(x)));

    unknownUserIds.forEach(async (userId) => {
      const u = await User.fetch(userId);
      cacheUser(userId, u || new NonexistentUser());

      if (u) {
        u.getAvatarUrl()
          .then((url) => {
            cacheProfilePicUrl(userId, url);
          })
          .catch(logger.error);
      }
    });

    if (unknownUserIds.size > 0) {
      logger.debug(`Fetched new data for ${unknownUserIds.size} user(s)`);
    }
  }

  if (user === null) {
    return <LoadingIndicator sx={{height: '80vh'}} />;
  }

  return (
    <>
      {/*Comments modal to show immediately on page load if the discussion page was reached via /discussion/{postId}*/}
      {initialCommentsModalPost && (
        <DiscussionCommentsModal
          post={initialCommentsModalPost}
          cachedUsers={cachedUsers.current}
          cachedProfilePicUrls={cachedProfilePicUrls.current}
          updateCachedUsers={updateCachedUsers}
          onClose={() => setInitialCommentsModalPost(null)}
          universityId={universityId}
        />
      )}

      {/*Back to top button*/}
      <Box
        sx={{
          position: 'fixed',
          top: 30,
          left: {xs: '50%', sm: '50%', md: '60%'},
          transform: 'translateX(-50%)',
          zIndex: 1000,
          opacity: showBackToTop ? 1 : 0,
          transition: 'opacity 0.1s',
        }}
      >
        <Button
          variant='soft'
          onClick={scrollToTop}
        >
          Back to top
          <ArrowUpward sx={{ml: 0.5}} />
        </Button>
      </Box>

      {/*Input*/}
      <DiscussionInput
        profilePicUrl={profilePicUrl || ''}
        universityId={universityId}
      />

      {/*Pinned DiscussionPostCards*/}
      {pinnedPosts.length > 0 && (
        <AccordionGroup
          sx={{
            maxWidth: discussionFeedWidth,
            width: '100%',
            mt: 2,
            mb: 2,
            justifySelf: 'center',
            backgroundColor: 'rgba(244, 98, 36, 0.2)',
            borderRadius: 4,
            [`& .${accordionClasses.root}`]: {
              transition: '0.2s ease',
              '& button': {
                transition: '0.2s ease',
                paddingTop: '0.5rem',
                paddingBottom: '0.5rem',
                borderRadius: 'sm',
              },
              '& button:hover': {
                background: 'rgba(244, 98, 36, 0.2)',
              },
            },
          }}
        >
          <Accordion>
            <AccordionSummary>
              <PushPin />
              Pinned posts ({pinnedPosts.length})
            </AccordionSummary>
            <AccordionDetails>
              <Stack alignItems={'center'}>
                {pinnedPosts.map((post, i) => {
                  return (
                    <DiscussionPostCard
                      key={`pinned-${post.id}`}
                      initialPost={post}
                      withCommentsButton={true}
                      cachedUsers={cachedUsers.current}
                      cachedProfilePicUrls={cachedProfilePicUrls.current}
                      updateCachedUsers={updateCachedUsers}
                      sx={{
                        mb: 1,
                        mt: i === 0 ? 3 : 0,
                        maxWidth: discussionFeedWidth,
                        width: '97%',
                      }}
                      universityId={universityId}
                    />
                  );
                })}
              </Stack>
            </AccordionDetails>
          </Accordion>
        </AccordionGroup>
      )}

      {/*DiscussionPostCards*/}
      <FirestoreInfiniteScroll
        col={universityId ? collection(firestore(), University.collectionName, universityId, 'discussion') : collection(firestore(), 'global', 'discussion', 'discussion')}
        order={{sortField: 'datePosted', direction: 'desc'}}
        itemHeight={postHeight}
        converter={new DiscussionPostConverter()}
        scrollFullPage={true}
        noItemsPlaceholder={
          universityId && !university?.partner && user.staffRole === StaffRole.OWNER ? (
            <Box sx={{position: 'relative'}}>
              <BlurryOverlay
                subtitle={strings.overlay.discussion_page_subtitle}
                button={'activate'}
              >
                <NoPostsPlaceholder />
              </BlurryOverlay>
            </Box>
          ) : (
            <NoPostsPlaceholder />
          )
        }
        onItemAdded={(fetchedPost) => {
          updateCachedUsers([fetchedPost.authorId]);
        }}
        onFetchMore={(fetchedPosts) => {
          updateCachedUsers(fetchedPosts.map((p) => p.authorId));
        }}
        itemBuilder={(post, i, total) => {
          return (
            <DiscussionPostCard
              key={post.id}
              initialPost={post}
              withCommentsButton={true}
              cachedUsers={cachedUsers.current}
              cachedProfilePicUrls={cachedProfilePicUrls.current}
              updateCachedUsers={updateCachedUsers}
              sx={{
                mb: i === total - 1 ? 8 : 3,
                mt: i === 0 ? 3 : 0,
                maxWidth: discussionFeedWidth,
                width: '100%',
              }}
              universityId={universityId}
            />
          );
        }}
        sx={{display: 'flex', flexDirection: 'column', scrollbarWidth: 'none', alignItems: 'center'}}
      />
    </>
  );
}
