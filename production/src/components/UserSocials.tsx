import SocialIconButton from './SocialIconButton.tsx';
import {GitHub, LinkedIn} from '@mui/icons-material';
import {UserHit} from '@creator-campus/common';

interface Props {
  user: UserHit;
}

export default function UserSocials({user}: Props) {
  return (
    <>
      {user.githubUsername && (
        <SocialIconButton
          icon={<GitHub />}
          link={user.getGitHubUrl()!}
        />
      )}
      {user.linkedinUsername && (
        <SocialIconButton
          icon={<LinkedIn />}
          link={user.getLinkedInUrl()!}
        />
      )}
    </>
  );
}
