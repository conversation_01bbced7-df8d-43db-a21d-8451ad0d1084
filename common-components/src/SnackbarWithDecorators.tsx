import Button from '@mui/joy/Button';
import Snackbar from '@mui/joy/Snackbar';
import PlaylistAddCheckCircleRoundedIcon from '@mui/icons-material/PlaylistAddCheckCircleRounded';
import {ReactElement, useEffect, useState} from 'react';
import useMediaQuery from '@mui/material/useMediaQuery';

interface Props {
  text: string;
  icon?: ReactElement;
  color?: 'primary' | 'neutral' | 'success' | 'warning' | 'danger';
  isOpen: boolean;
  onClose?: () => void;
  autoHideMs?: number | null;
  actionText?: string;
  action?: () => void;
  sx?: object;
}

export function SnackbarWithDecorators({text, icon = <PlaylistAddCheckCircleRoundedIcon />, color = 'neutral', isOpen, onClose, autoHideMs = 2500, actionText, action, sx}: Props) {
  const [open, setOpen] = useState(false);

  // Use useMediaQuery to determine screen size
  const isXsOrSm = useMediaQuery('(max-width:900px)');

  useEffect(() => {
    setOpen(isOpen);
  }, [isOpen]);

  return (
    <Snackbar
      variant='soft'
      color={color}
      autoHideDuration={autoHideMs}
      open={open}
      sx={{zIndex: 9999, ...sx}}
      onClose={(_, reason) => {
        if (reason === 'clickaway') {
          return;
        } else {
          setOpen(false);
          if (onClose) {
            onClose();
          }
        }
      }}
      // Conditionally set anchorOrigin based on screen size
      anchorOrigin={isXsOrSm ? {vertical: 'bottom', horizontal: 'right'} : {vertical: 'top', horizontal: 'right'}}
      startDecorator={icon}
      endDecorator={
        action && actionText ? (
          <Button
            onClick={action}
            size='sm'
            variant='soft'
            color={color}
          >
            {actionText}
          </Button>
        ) : null
      }
    >
      {text}
    </Snackbar>
  );
}
