import {TiArrowUpThick} from 'react-icons/ti';
import Box from '@mui/joy/Box';
import {TooltipChip} from './TooltipChip.tsx';

interface Props {
  karma: number;
  size?: 'sm' | 'md' | 'lg';
}

export default function KarmaChip({karma, size = 'sm'}: Props) {
  return (
    <TooltipChip
      tooltip={'Earn karma by participating in discussion with others.'}
      icon={<TiArrowUpThick style={{fontSize: '13px'}} />}
      content={<Box>{karma}</Box>}
      size={size}
      color={'success'}
    />
  );
}
