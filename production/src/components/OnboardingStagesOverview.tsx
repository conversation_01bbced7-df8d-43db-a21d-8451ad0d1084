import {keyframes} from '@emotion/react';
import {Stack, useTheme} from '@mui/joy';
import {CreatorCampusLogo} from './CreatorCampusLogo.tsx';
import ButtonGroup from '@mui/joy/ButtonGroup';
import Chip from '@mui/joy/Chip';
import {useMemo} from 'react';

interface Props {
  currentStageIndex: number;
  numStages: number;
}

export function OnboardingStagesOverview({currentStageIndex, numStages}: Props) {
  const theme = useTheme();

  const pulseVariant = useMemo(() => {
    return keyframes`
      0% {
        background-color: ${theme.palette.primary.softBg};
      }
      30% {
        background-color: ${theme.palette.primary.solidBg};
      }
      60% {
        background-color: ${theme.palette.primary.softBg};
      }
      100% {
        background-color: ${theme.palette.primary.softBg};
      }
    `;
  }, [theme]);

  function getSx(i: number) {
    let radiusSx = {};
    if (i === 0) {
      radiusSx = {borderTopRightRadius: 0, borderBottomRightRadius: 0};
    } else if (i === numStages - 1) {
      radiusSx = {borderTopLeftRadius: 0, borderBottomLeftRadius: 0};
    }

    const baseSx = {
      minWidth: {xs: 50, sm: 60},
      minHeight: 10,
      ...radiusSx,
    };

    if (i === currentStageIndex) {
      return {
        ...baseSx,
        backgroundColor: theme.palette.primary.softBg,
        animation: `${pulseVariant} 3s infinite ease-in-out`,
      };
    }

    return baseSx;
  }

  return (
    <Stack
      spacing={1}
      sx={{alignItems: 'center'}}
    >
      <CreatorCampusLogo />
      {numStages > 1 && (
        <ButtonGroup color='white'>
          {[...Array(numStages)].map((_, i) => (
            <Chip
              key={`onboarding-stage-${i}`}
              variant={currentStageIndex === numStages - 1 ? 'solid' : currentStageIndex > i ? 'solid' : 'soft'}
              color={currentStageIndex >= i ? 'primary' : 'neutral'}
              sx={getSx(i)}
            />
          ))}
        </ButtonGroup>
      )}
    </Stack>
  );
}
