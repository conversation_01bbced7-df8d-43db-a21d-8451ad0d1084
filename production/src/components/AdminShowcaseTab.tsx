import Stack from '@mui/joy/Stack';
import Typography from '@mui/joy/Typography';
import Switch, {switchClasses} from '@mui/joy/Switch';
import Button from '@mui/joy/Button';
import {University} from '@creator-campus/common';
import {useSnackbar} from '@creator-campus/common-components';
import {getDoc, setDoc, updateDoc} from 'firebase/firestore';
import {useEffect, useState} from 'react';
import {Input} from '@mui/joy';
import {ContentCopy, OpenInNew} from '@mui/icons-material';
import Tooltip from '@mui/joy/Tooltip';

interface Props {
  university: University;
}

export function AdminShowcaseTab({university}: Props) {
  const {showSnackbar} = useSnackbar();

  // null when loading
  const [showcaseEnabled, setShowcaseEnabled] = useState<boolean | null>(university.showcaseEnabled);
  const [token, setToken] = useState<string | null>(null);

  useEffect(() => {
    getDoc(University.privateShowcaseDoc(university.id)).then((snapshot) => {
      setToken(snapshot.data()?.token || generateToken());
    });
  }, []);

  useEffect(() => {
    setShowcaseEnabled(university.showcaseEnabled);
  }, [university.showcaseEnabled]);

  function generateToken() {
    const array = new Uint8Array(16);
    window.crypto.getRandomValues(array);
    return Array.from(array, (byte) => byte.toString(16).padStart(2, '0')).join('');
  }

  function ShowcaseSwitch() {
    return (
      <Switch
        sx={{
          [`& .${switchClasses.thumb}`]: {
            transition: 'width 0.2s, left 0.2s',
          },
          '--Switch-thumbSize': '17px',
          '--Switch-trackWidth': '40px',
          '--Switch-trackHeight': '22px',
        }}
        checked={showcaseEnabled || university.showcaseEnabled}
        disabled={showcaseEnabled === null}
        onChange={async (event) => {
          const checked = event.target.checked;

          setShowcaseEnabled(null);

          if (checked) {
            const newToken = generateToken();
            setToken(newToken);
            await setDoc(University.privateShowcaseDoc(university.id), {
              token: newToken,
            });
          }

          await updateDoc(university.doc(), {
            showcaseEnabled: checked,
          });

          if (checked) {
            showSnackbar('Showcase enabled.');
          } else {
            showSnackbar('Showcase disabled.');
          }
        }}
      />
    );
  }

  function InputActionButtons() {
    return (
      <Stack direction={'row'}>
        <Tooltip
          title='Open link'
          arrow
          placement={'top'}
        >
          <Button
            size='sm'
            variant='soft'
            color='neutral'
            component='a'
            href={university.getShowcaseUrl(token!)}
            target='_blank'
            rel='noopener noreferrer'
            aria-label='Open showcase link'
            sx={{borderRadius: 0, mr: 0.01}}
          >
            <OpenInNew />
          </Button>
        </Tooltip>

        <Tooltip
          title={'Copy link'}
          arrow
          placement={'top'}
        >
          <Button
            size='sm'
            variant='soft'
            color='neutral'
            onClick={async () => {
              await navigator.clipboard.writeText(university.getShowcaseUrl(token!));
              showSnackbar('Link copied to clipboard.');
            }}
            aria-label='Copy showcase link'
            sx={{borderTopLeftRadius: 0, borderBottomLeftRadius: 0}}
          >
            <ContentCopy />
          </Button>
        </Tooltip>
      </Stack>
    );
  }

  if (token === null) {
    return <></>;
  }

  return (
    <Stack
      spacing={1.5}
      sx={{maxWidth: 650}}
    >
      <Stack spacing={0.2}>
        <Typography level={'title-sm'}>Showcase your community's startups and opportunities</Typography>
        <Typography level={'body-sm'}>Share your community's startups and opportunities with the world. When enabled, your showcase will be publicly accessible.</Typography>
      </Stack>
      <Stack
        direction={'row'}
        spacing={1}
        alignItems={'center'}
      >
        <ShowcaseSwitch />
        <Input
          value={university.getShowcaseUrl(token)}
          readOnly
          disabled={!showcaseEnabled}
          variant='outlined'
          color='neutral'
          sx={{'--Input-decoratorChildHeight': '35px', flexGrow: 1}}
          endDecorator={<InputActionButtons />}
        />
      </Stack>
    </Stack>
  );
}
